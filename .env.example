APP_NAME=GREENTİM
APP_INOSUIT_NAME="İnoSuit"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME=${APP_NAME}

INOSUIT_MAIL_MAILER=smtp
INOSUIT_MAIL_HOST=mailpit
INOSUIT_MAIL_PORT=1025
INOSUIT_MAIL_USERNAME=null
INOSUIT_MAIL_PASSWORD=null
INOSUIT_MAIL_ENCRYPTION=null
INOSUIT_MAIL_FROM_ADDRESS="<EMAIL>"
INOSUIT_MAIL_FROM_NAME=${APP_INOSUIT_NAME}

AWS_ENDPOINT=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

GREENTIM_FRONTEND_URL="http://localhost:3000"
INOSUIT_FRONTEND_URL="http://localhost:3001"

SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=password

FEEDBACK_ADMIN_EMAIL=<EMAIL>
FEEDBACK_INOSUIT_EMAIL=<EMAIL>
NOTIFICATION_INOSUIT_EMAIL=<EMAIL>

ECO_MENTOR_API_KEY=

UNCERTAINTY_TEST_USER_IDS="1,2,3"
