<?php

use Illuminate\Support\Facades\Route;

Route::prefix('/eco-mentor')->middleware(['eco.mentor.api.key'])->group(function () {
    Route::post('/login', [\App\Http\Controllers\EcoMentorController::class, 'login']);
    Route::post('/register', [\App\Http\Controllers\EcoMentorController::class, 'register']);

    Route::middleware(['eco.mentor.token'])->group(function () {
        Route::get('/facilities', [\App\Http\Controllers\EcoMentorController::class, 'getFacilities']);
        Route::get('/facilities/species', [\App\Http\Controllers\EcoMentorController::class, 'getFacilitySpecies']);
        Route::post('/facilities/sync', [\App\Http\Controllers\EcoMentorController::class, 'syncFacilities']);
        Route::get('/sectors', [\App\Http\Controllers\EcoMentorController::class, 'getSectors']);
        Route::get('/forms/years', [\App\Http\Controllers\EcoMentorController::class, 'getYears']);
        Route::get('/forms/months', [\App\Http\Controllers\EcoMentorController::class, 'getMonths']);
        Route::get('/forms/categories', [\App\Http\Controllers\EcoMentorController::class, 'getCategories']);
        Route::get('/forms/detail', [\App\Http\Controllers\EcoMentorController::class, 'getFormDetail']);
        Route::post('/forms/calculate', [\App\Http\Controllers\EcoMentorController::class, 'calculateForm']);
        Route::get('/reports', [\App\Http\Controllers\EcoMentorController::class, 'getReports']);
        Route::post('/reports', [\App\Http\Controllers\EcoMentorController::class, 'createReport']);
        Route::post('/files/url', [\App\Http\Controllers\EcoMentorController::class, 'createFileUrl']);
    });
});
