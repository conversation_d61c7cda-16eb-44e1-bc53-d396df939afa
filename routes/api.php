<?php

use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\PermissionEnum;
use App\Utils\GeneralUtil;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('/companies')->group(function () {
    Route::get('/personnel-numbers', [\App\Http\Controllers\CompanyController::class, 'getPersonnelNumbers']);
    Route::get('/sectors', [\App\Http\Controllers\CompanyController::class, 'getSectors']);
});

Route::prefix('/companies')->middleware('auth')->group(function () {
    Route::get('/profile', [\App\Http\Controllers\CompanyController::class, 'getCompanyProfile']);
    Route::get('/nace-codes', [\App\Http\Controllers\CompanyController::class, 'getNaceCodes']);
    Route::put('/', [\App\Http\Controllers\CompanyController::class, 'updateCompany'])->middleware([GeneralUtil::can(PermissionEnum::AUTHORIZATION)]);
});

Route::prefix('/configs')->group(function () {
    Route::get('/', [\App\Http\Controllers\ConfigController::class, 'getConfigs']);
    Route::get('/languages', [\App\Http\Controllers\ConfigController::class, 'getLanguages']);
});

Route::prefix('/educations')->group(function () {
    Route::get('/', [\App\Http\Controllers\EducationController::class, 'getEducations']);
});

Route::prefix('/users')->group(function () {
    Route::post('/login', [\App\Http\Controllers\UserController::class, 'login']);
    Route::post('/register', [\App\Http\Controllers\UserController::class, 'register']);
    Route::post('/verify-email', [\App\Http\Controllers\UserController::class, 'verifyEmail']);
    Route::post('/forgot-password/send-mail', [\App\Http\Controllers\UserController::class, 'sendForgotPasswordMail']);
    Route::post('/forgot-password/reset-password', [\App\Http\Controllers\UserController::class, 'resetPassword']);
    Route::post('/logout', [\App\Http\Controllers\UserController::class, 'logout'])->middleware('auth');
    Route::get('/profile', [\App\Http\Controllers\UserController::class, 'getProfile'])->middleware('auth');
    Route::put('/profile', [\App\Http\Controllers\UserController::class, 'updateProfile'])->middleware('auth');
    Route::post('/profile/photo', [\App\Http\Controllers\UserController::class, 'updateProfilePhoto'])->middleware('auth');
    Route::delete('/profile/photo', [\App\Http\Controllers\UserController::class, 'deleteProfilePhoto'])->middleware('auth');
    Route::put('/password', [\App\Http\Controllers\UserController::class, 'updatePassword'])->middleware('auth');
});

Route::prefix('/tubitak')->group(function () {
    Route::post('/applications', [\App\Http\Controllers\TubitakController::class, 'createApplications']);
});

Route::prefix('/green-transformations')->group(function () {
    Route::post('/', [\App\Http\Controllers\GreenTransformationController::class, 'createGreenTransformation']);
});

Route::prefix('/facilities')->middleware('auth')->group(function () {
    Route::get('/species', [\App\Http\Controllers\FacilityController::class, 'getSpecies'])->middleware(GeneralUtil::can(PermissionEnum::FACILITY_LIST));
    Route::get('/', [\App\Http\Controllers\FacilityController::class, 'getFacilities'])->middleware(GeneralUtil::can(PermissionEnum::FACILITY_LIST));
    Route::post('/', [\App\Http\Controllers\FacilityController::class, 'createFacilities'])->middleware(GeneralUtil::can(PermissionEnum::FACILITY_OPERATION));
    Route::put('/{id}', [\App\Http\Controllers\FacilityController::class, 'updateFacilities'])->middleware(GeneralUtil::can(PermissionEnum::FACILITY_OPERATION));
    Route::delete('/{id}', [\App\Http\Controllers\FacilityController::class, 'deleteFacilities'])->middleware(GeneralUtil::can(PermissionEnum::FACILITY_OPERATION));
});

Route::prefix('/forms')->middleware(['auth', 'facility', GeneralUtil::can(PermissionEnum::FORM)])->group(function () {
    Route::get('/years', [\App\Http\Controllers\FormController::class, 'getYears']);
    Route::get('/months', [\App\Http\Controllers\FormController::class, 'getMonths']);
    Route::get('/categories', [\App\Http\Controllers\FormController::class, 'getCategories']);
    Route::get('/detail/{slug}', [\App\Http\Controllers\FormController::class, 'getFormsBySlug']);
    Route::post('/calculate/results', [\App\Http\Controllers\FormController::class, 'calculateFormResults']);
});

Route::prefix('/uncertainties')->middleware(['auth', 'facility', GeneralUtil::can(PermissionEnum::FORM)])->group(function () {
    Route::get('/', [\App\Http\Controllers\UncertaintyController::class, 'getUncertainty']);
});

Route::prefix('/reports')->middleware(['auth', 'facility', GeneralUtil::can(PermissionEnum::REPORT, PermissionEnum::REPORT_SELF)])->group(function () {
    Route::get('/', [\App\Http\Controllers\ReportController::class, 'getReports']);
    Route::get('/detail', [\App\Http\Controllers\ReportController::class, 'getReportDetail']);
    Route::post('/', [\App\Http\Controllers\ReportController::class, 'createReport']);
    Route::put('/', [\App\Http\Controllers\ReportController::class, 'updateReport']);
    Route::delete('/{id}', [\App\Http\Controllers\ReportController::class, 'deleteReport']);
});
Route::prefix('/reports')->middleware(['auth', 'facility', GeneralUtil::can(PermissionEnum::REPORT)])->group(function () {
    Route::get('/summary', [\App\Http\Controllers\ReportController::class, 'getReportsSummary']);
    Route::get('/years', [\App\Http\Controllers\ReportController::class, 'getReportsYears']);
    Route::get('/group-by/facilities', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByFacilities']);
    Route::get('/group-by/facilities/pdf', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByFacilitiesForPDF']);
    Route::get('/group-by/years', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByYears']);
    Route::get('/group-by/years/pdf', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByYearsForPDF']);
    Route::get('/group-by/years/analysis', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByYearsForAnalysis']);
    Route::get('/group-by/categories', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByCategories']);
    Route::get('/group-by/categories/pdf', [\App\Http\Controllers\ReportController::class, 'getReportsGroupByCategoriesForPDF']);
    Route::get('/categories-gases', [\App\Http\Controllers\ReportController::class, 'getCategoriesGases']);
});

Route::prefix('/authorizations')->middleware(['auth', GeneralUtil::can(PermissionEnum::AUTHORIZATION)])->group(function () {
    Route::get('/roles', [\App\Http\Controllers\AuthorizationController::class, 'getRoles']);
    Route::get('/employees', [\App\Http\Controllers\AuthorizationController::class, 'getEmployees']);
    Route::post('/employees', [\App\Http\Controllers\AuthorizationController::class, 'createEmployees']);
    Route::put('/employees/{id}', [\App\Http\Controllers\AuthorizationController::class, 'updateEmployees']);
    Route::delete('/employees/{id}', [\App\Http\Controllers\AuthorizationController::class, 'deleteEmployees']);
    Route::post('/employees/upload', [\App\Http\Controllers\AuthorizationController::class, 'uploadEmployeesExcel']);
});

Route::prefix('/tips')->middleware(['auth'])->group(function () {
    Route::get('/contents', [\App\Http\Controllers\TipController::class, 'getContents']);
    Route::post('/contents/read', [\App\Http\Controllers\TipController::class, 'readContents']);
});

Route::prefix('/feedbacks')->middleware(['auth'])->group(function () {
    Route::get('/subjects', [\App\Http\Controllers\FeedbackController::class, 'getSubjects']);
    Route::post('/', [\App\Http\Controllers\FeedbackController::class, 'createFeedbacks']);
});

Route::prefix('/files')->middleware(['auth'])->group(function () {
    Route::post('/', [\App\Http\Controllers\FileController::class, 'createFile']);
    Route::post('/url', [\App\Http\Controllers\FileController::class, 'createFileUrl']);
    Route::delete('/', [\App\Http\Controllers\FileController::class, 'deleteFile']);
});

Route::prefix('/artificial')->middleware(['auth'])->group(function () {
    Route::post('/questions', [\App\Http\Controllers\ArtificialController::class, 'createQuestion']);
    Route::put('/questions', [\App\Http\Controllers\ArtificialController::class, 'updateQuestion']);
});

Route::prefix('/iso-standards')->middleware(['auth', 'facility', 'report', GeneralUtil::can(PermissionEnum::REPORT)])->group(function () {
    Route::get('/', [\App\Http\Controllers\IsoStandardController::class, 'getIsoStandards']);
    Route::get('/detail', [\App\Http\Controllers\IsoStandardController::class, 'getIsoStandardDetail']);
    Route::get('/years', [\App\Http\Controllers\IsoStandardController::class, 'getYears']);
    Route::get('/years-gases', [\App\Http\Controllers\IsoStandardController::class, 'getYearsGases']);
    Route::get('/categories-gases', [\App\Http\Controllers\IsoStandardController::class, 'getCategoriesGases']);
    Route::get('/facilities', [\App\Http\Controllers\IsoStandardController::class, 'getFacilities']);
    Route::get('/materialities', [\App\Http\Controllers\IsoStandardController::class, 'getMaterialities']);
    Route::get('/classifications', [\App\Http\Controllers\IsoStandardController::class, 'getClassifications']);
    Route::get('/sources-and-classifications', [\App\Http\Controllers\IsoStandardController::class, 'getSourcesAndClassifications']);
    Route::get('/multipliers', [\App\Http\Controllers\IsoStandardController::class, 'getMultipliers']);
    Route::get('/uncertainties', [\App\Http\Controllers\IsoStandardController::class, 'getUncertainties']);
    Route::post('/', [\App\Http\Controllers\IsoStandardController::class, 'createIsoStandard']);
    Route::delete('/', [\App\Http\Controllers\IsoStandardController::class, 'deleteIsoStandard']);
});

Route::prefix('/iso-standards')->group(function () {
    Route::post('/access-token', [\App\Http\Controllers\IsoStandardController::class, 'createAccessToken']);
});

Route::prefix('/inosuit')->group(function () {
    Route::get('/associations', [\App\Http\Controllers\Inosuit\InosuitController::class, 'getAssociations']);
    Route::get('/periods', [\App\Http\Controllers\Inosuit\InosuitController::class, 'getPeriods']);
    Route::get('/companies/applications/questions', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getApplicationQuestions']);
    Route::post('/files', [\App\Http\Controllers\Inosuit\InosuitController::class, 'createFile']);
    Route::put('/users/password', [\App\Http\Controllers\Inosuit\InosuitController::class, 'updatePassword']);
    Route::post('/companies/applications', [\App\Http\Controllers\Inosuit\CompanyController::class, 'createApplication']);
    Route::post('/mentors/applications', [\App\Http\Controllers\Inosuit\MentorController::class, 'createApplication']);
});
Route::prefix('/inosuit')->middleware(['auth', 'inosuit.period'])->group(function () {
    Route::get('/activities', [\App\Http\Controllers\Inosuit\InosuitController::class, 'getActivities']);
    Route::get('/companies/papers/questions', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getPaperQuestions']);

    Route::prefix('/tim-admin')->middleware(GeneralUtil::privileges(RolePrivilegeEnum::TIM_ADMIN))->group(function () {
        Route::get('/periods', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getPeriods']);
        Route::get('/papers', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getPapers']);
        Route::get('/companies', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCompanies']);
        Route::get('/companies/papers/detail', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCompanyPaperDetail']);
        Route::get('/companies/applications', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCompanyApplications']);
        Route::get('/companies/applications/detail', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCompanyApplicationDetail']);
        Route::get('/companies/profile', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCompanyProfile']);
        Route::get('/mentors', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getMentors']);
        Route::get('/mentors/papers/detail', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getMentorPaperDetail']);
        Route::get('/mentors/applications', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getMentorApplications']);
        Route::get('/mentors/applications/detail', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getMentorApplicationDetail']);
        Route::get('/mentors/profile', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getMentorProfile']);
        Route::get('/meetings', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getMeetings']);
        Route::get('/calendars', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCalendars']);
        Route::get('/calendars/companies', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getCalendarCompanies']);
        Route::get('/exports/period-reports', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getPeriodReportExport']);
        Route::get('/exports/applications', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'getApplicationExport']);
        Route::post('/periods', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'createPeriod']);
        Route::post('/calendars', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'createCalendar']);
        Route::put('/companies', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateCompany']);
        Route::put('/companies/papers', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateCompanyPaper']);
        Route::put('/companies/applications', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateCompanyApplication']);
        Route::put('/mentors', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateMentor']);
        Route::put('/mentors/papers', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateMentorPaper']);
        Route::put('/mentors/applications', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateMentorApplication']);
        Route::put('/associations', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateAssociation']);
        Route::put('/periods', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updatePeriod']);
        Route::put('/calendars', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'updateCalendar']);
        Route::delete('/periods', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'deletePeriod']);
        Route::delete('/calendars', [\App\Http\Controllers\Inosuit\TimAdminController::class, 'deleteCalendar']);
    });
    Route::prefix('/associations')->middleware(GeneralUtil::privileges(RolePrivilegeEnum::ASSOCIATION))->group(function () {
        Route::get('/periods', [\App\Http\Controllers\Inosuit\AssociationController::class, 'getPeriods']);
        Route::get('/papers', [\App\Http\Controllers\Inosuit\AssociationController::class, 'getPapers']);
        Route::get('/companies', [\App\Http\Controllers\Inosuit\AssociationController::class, 'getCompanies']);
        Route::get('/companies/profile', [\App\Http\Controllers\Inosuit\AssociationController::class, 'getCompanyProfile']);
        Route::get('/mentors', [\App\Http\Controllers\Inosuit\AssociationController::class, 'getMentors']);
        Route::get('/mentors/profile', [\App\Http\Controllers\Inosuit\AssociationController::class, 'getMentorProfile']);
    });
    Route::prefix('/companies')->middleware(GeneralUtil::privileges(RolePrivilegeEnum::COMPANY))->group(function () {
        Route::get('/periods', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getPeriods']);
        Route::get('/profile', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getProfile']);
        Route::get('/papers', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getPapers']);
        Route::get('/papers/detail', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getPaperDetail']);
        Route::get('/papers/terms', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getPaperTerms']);
        Route::get('/mentors', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getMentors']);
        Route::get('/meetings', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getMeetings']);
        Route::get('/calendars', [\App\Http\Controllers\Inosuit\CompanyController::class, 'getCalendars']);
        Route::post('/papers', [\App\Http\Controllers\Inosuit\CompanyController::class, 'createPaper']);
        Route::post('/meetings', [\App\Http\Controllers\Inosuit\CompanyController::class, 'createMeeting']);
        Route::put('/papers', [\App\Http\Controllers\Inosuit\CompanyController::class, 'updatePaper']);
        Route::put('/current-period', [\App\Http\Controllers\Inosuit\CompanyController::class, 'updateCurrentPeriod']);
        Route::put('/meetings', [\App\Http\Controllers\Inosuit\CompanyController::class, 'updateMeeting']);
        Route::delete('/meetings', [\App\Http\Controllers\Inosuit\CompanyController::class, 'deleteMeeting']);
    });
    Route::prefix('/mentors')->middleware(GeneralUtil::privileges(RolePrivilegeEnum::MENTOR))->group(function () {
        Route::get('/periods', [\App\Http\Controllers\Inosuit\MentorController::class, 'getPeriods']);
        Route::get('/profile', [\App\Http\Controllers\Inosuit\MentorController::class, 'getProfile']);
        Route::get('/papers', [\App\Http\Controllers\Inosuit\MentorController::class, 'getPapers']);
        Route::get('/papers/detail', [\App\Http\Controllers\Inosuit\MentorController::class, 'getPaperDetail']);
        Route::get('/papers/terms', [\App\Http\Controllers\Inosuit\MentorController::class, 'getPaperTerms']);
        Route::get('/companies', [\App\Http\Controllers\Inosuit\MentorController::class, 'getCompanies']);
        Route::get('/meetings', [\App\Http\Controllers\Inosuit\MentorController::class, 'getMeetings']);
        Route::get('/calendars', [\App\Http\Controllers\Inosuit\MentorController::class, 'getCalendars']);
        Route::post('/papers', [\App\Http\Controllers\Inosuit\MentorController::class, 'createPaper']);
        Route::post('/meetings', [\App\Http\Controllers\Inosuit\MentorController::class, 'createMeeting']);
        Route::put('/profile', [\App\Http\Controllers\Inosuit\MentorController::class, 'updateProfile']);
        Route::put('/papers', [\App\Http\Controllers\Inosuit\MentorController::class, 'updatePaper']);
        Route::put('/meetings', [\App\Http\Controllers\Inosuit\MentorController::class, 'updateMeeting']);
        Route::put('/calendars', [\App\Http\Controllers\Inosuit\MentorController::class, 'updateCalendar']);
        Route::delete('/meetings', [\App\Http\Controllers\Inosuit\MentorController::class, 'deleteMeeting']);
    });
});

Route::prefix('/admin')->middleware(['auth', 'admin'])->group(function () {
    Route::post('/configs', [\App\Http\Controllers\Admin\ConfigController::class, 'createOrUpdateConfigs']);

    Route::get('/users', [\App\Http\Controllers\Admin\UserController::class, 'getUsers']);
    Route::post('/users/send-custom-mail', [\App\Http\Controllers\Admin\UserController::class, 'sendCustomMail']);
    Route::post('/users/access-token', [\App\Http\Controllers\Admin\UserController::class, 'createUserAccessToken']);

    Route::get('/pending-users', [\App\Http\Controllers\Admin\PendingUserController::class, 'getPendingUsers']);
    Route::post('/pending-users/approve', [\App\Http\Controllers\Admin\PendingUserController::class, 'approvePendingUsers']);
    Route::post('/pending-users/deny', [\App\Http\Controllers\Admin\PendingUserController::class, 'denyPendingUsers']);

    Route::get('/forms', [\App\Http\Controllers\Admin\FormController::class, 'getForms']);
    Route::put('/forms/{id}', [\App\Http\Controllers\Admin\FormController::class, 'updateForms']);
    Route::get('/forms/results', [\App\Http\Controllers\Admin\FormController::class, 'getFormResults']);
    Route::post('/forms', [\App\Http\Controllers\Admin\FormController::class, 'createForms']);
    Route::put('/forms/results/{id}', [\App\Http\Controllers\Admin\FormController::class, 'updateFormResults']);
    Route::delete('/forms/results/{id}', [\App\Http\Controllers\Admin\FormController::class, 'deleteFormResults']);
    Route::get('/forms/inputs', [\App\Http\Controllers\Admin\FormController::class, 'getInputs']);
    Route::post('/forms/inputs', [\App\Http\Controllers\Admin\FormController::class, 'createInputs']);
    Route::put('/forms/inputs/{id}', [\App\Http\Controllers\Admin\FormController::class, 'updateInputs']);
    Route::get('/forms/groups', [\App\Http\Controllers\Admin\FormController::class, 'getGroups']);
    Route::post('/forms/groups', [\App\Http\Controllers\Admin\FormController::class, 'createGroups']);
    Route::put('/forms/groups/{id}', [\App\Http\Controllers\Admin\FormController::class, 'updateGroups']);
    Route::delete('/forms/groups/{id}', [\App\Http\Controllers\Admin\FormController::class, 'deleteGroups']);
    Route::put('/forms/groups/values/{id}', [\App\Http\Controllers\Admin\FormController::class, 'updateGroupValues']);

    Route::get('/feedbacks', [\App\Http\Controllers\Admin\FeedbackController::class, 'getFeedbacks']);
    Route::put('/feedbacks/complete/{id}', [\App\Http\Controllers\Admin\FeedbackController::class, 'completeFeedbacks']);

    Route::get('/tips', [\App\Http\Controllers\Admin\TipController::class, 'getTips']);
    Route::get('/tips/contents', [\App\Http\Controllers\Admin\TipController::class, 'getContents']);
    Route::post('/tips/contents', [\App\Http\Controllers\Admin\TipController::class, 'createContents']);
    Route::put('/tips/contents/{id}', [\App\Http\Controllers\Admin\TipController::class, 'updateContents']);

    Route::get('/operations/fields', [\App\Http\Controllers\Admin\OperationController::class, 'getFields']);
    Route::get('/operations/groups', [\App\Http\Controllers\Admin\OperationController::class, 'getGroups']);
    Route::get('/operations/multipliers', [\App\Http\Controllers\Admin\OperationController::class, 'getMultipliers']);
    Route::get('/operations/multiplier-by-values', [\App\Http\Controllers\Admin\OperationController::class, 'getMultiplierByValues']);
    Route::post('/operations/multipliers', [\App\Http\Controllers\Admin\OperationController::class, 'createMultipliers']);
    Route::delete('/operations/multipliers/{id}', [\App\Http\Controllers\Admin\OperationController::class, 'deleteMultipliers']);

    Route::get('/uncertainties/fields', [\App\Http\Controllers\Admin\UncertaintyController::class, 'getFields']);
    Route::get('/uncertainties/groups', [\App\Http\Controllers\Admin\UncertaintyController::class, 'getGroups']);
    Route::get('/uncertainties', [\App\Http\Controllers\Admin\UncertaintyController::class, 'getUncertainties']);
    Route::post('/uncertainties', [\App\Http\Controllers\Admin\UncertaintyController::class, 'createUncertainty']);
    Route::delete('/uncertainties', [\App\Http\Controllers\Admin\UncertaintyController::class, 'deleteUncertainty']);

    Route::get('/iso-standards/group-values', [\App\Http\Controllers\Admin\IsoStandardController::class, 'getGroupValues']);
    Route::get('/iso-standards/classifications', [\App\Http\Controllers\Admin\IsoStandardController::class, 'getClassifications']);
    Route::get('/iso-standards/classifications-gasses', [\App\Http\Controllers\Admin\IsoStandardController::class, 'getClassificationsGasses']);
    Route::post('/iso-standards/classifications', [\App\Http\Controllers\Admin\IsoStandardController::class, 'createClassifications']);
    Route::delete('/iso-standards/classifications', [\App\Http\Controllers\Admin\IsoStandardController::class, 'deleteClassifications']);

    Route::get('/exports/excel', [\App\Http\Controllers\Admin\ExportController::class, 'excelExport']);
    Route::get('/exports/excel-esg', [\App\Http\Controllers\Admin\ExportController::class, 'excelExportForEsg']);
});

Route::prefix('/super-admin')->middleware('admin.super')->group(function () {
    Route::post('/tips/add', [\App\Http\Controllers\SuperAdmin\TipController::class, 'addTip']);

    Route::get('/esg/reports', [\App\Http\Controllers\SuperAdmin\EsgController::class, 'getReports']);
    Route::get('/esg/reports/files', [\App\Http\Controllers\SuperAdmin\EsgController::class, 'getReportFiles']);
    Route::get('/esg/facilities/counts', [\App\Http\Controllers\SuperAdmin\EsgController::class, 'getFacilityCounts']);
    Route::post('/esg/login', [\App\Http\Controllers\SuperAdmin\EsgController::class, 'login']);

    Route::get('/finance/users/profile', [\App\Http\Controllers\SuperAdmin\FinanceController::class, 'getUserProfile']);
    Route::get('/finance/companies/profile', [\App\Http\Controllers\SuperAdmin\FinanceController::class, 'getCompaniesProfile']);
});
