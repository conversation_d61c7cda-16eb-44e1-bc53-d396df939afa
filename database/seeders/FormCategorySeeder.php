<?php

namespace Database\Seeders;

use App\Enums\LanguageEnum;
use App\Models\FormCategory;
use App\Models\Language;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FormCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (FormCategory::query()->count() > 0) {
            return;
        }
        $categories = [
            [
                'id' => 1,
                'name_tr' => 'Kategori 1',
                'description_tr' => 'Doğrudan Emisyonlar',
                'name_en' => 'Category 1',
                'description_en' => 'Direct Emissions',
            ],
            [
                'id' => 2, 'name_tr' =>
                'Kategori 2',
                'description_tr' => 'İthal Edilen Enerjiden Kaynaklı Emisyonlar',
                'name_en' => 'Category 2',
                'description_en' => 'Emissions from Imported Energy',
            ],
            [
                'id' => 3, 'name_tr' => 'Kategori 3',
                'description_tr' => '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>isyonlar',
                'name_en' => 'Category 3',
                'description_en' => 'Emissions from Transportation',
            ],
            [
                'id' => 4,
                'name_tr' => 'Kategori 4',
                'description_tr' => 'Kuruluş Tarafından Kullanılan Ürünler ve Hizmetlerin Emisyonları',
                'name_en' => 'Category 4',
                'description_en' => 'Emissions from Products and Services Used by the Organization',
            ],
            [
                'id' => 5,
                'name_tr' => 'Kategori 5',
                'description_tr' => 'Kuruluş Tarafından Üretilen Ürünlerin Üretim Sonrası Emisyonları',
                'name_en' => 'Category 5',
                'description_en' => 'Post-Production Emissions of Products Produced by the Organization',
            ],
        ];
        $languages = [];
        foreach ($categories as &$c) {
            $c['created_at'] = now();
            $c['updated_at'] = now();
            $languages = array_merge($languages, $this->createLanguageForCategory($c, LanguageEnum::TURKISH));
            $languages = array_merge($languages, $this->createLanguageForCategory($c, LanguageEnum::ENGLISH));
            unset($c['name_tr']);
            unset($c['description_tr']);
            unset($c['name_en']);
            unset($c['description_en']);
        }

        DB::beginTransaction();
        try {
            FormCategory::query()->insert($categories);
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Error($e);
        }
    }

    private function createLanguageForCategory(array $category, LanguageEnum $lang): array
    {
        return [
            [
                'name' => $category['name_' . $lang->toString()],
                'lang_type' => $lang,
                'related_id' => $category['id'],
                'related_model' => FormCategory::class,
                'related_property' => 'name',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => $category['description_' . $lang->toString()],
                'lang_type' => $lang,
                'related_id' => $category['id'],
                'related_model' => FormCategory::class,
                'related_property' => 'description',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
    }
}
