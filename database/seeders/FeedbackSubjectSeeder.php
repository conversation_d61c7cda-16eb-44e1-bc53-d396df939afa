<?php

namespace Database\Seeders;

use App\Enums\FeedbackSubjectEnum;
use App\Enums\LanguageEnum;
use App\Models\Language;
use Illuminate\Database\Seeder;

class FeedbackSubjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Is exist control
        $addedSubjects = Language::query()->where('related_model', '=', FeedbackSubjectEnum::class)->get();
        if ($addedSubjects->count() > 0) {
            return;
        }

        $subjects = [
            [
                'id' => 1,
                'name_tr' => 'Hesaplama',
                'name_en' => 'Calculation',
            ],
            [
                'id' => 2,
                'name_tr' => 'Eğitimler',
                'name_en' => 'Educations',
            ],
            [
                'id' => 3,
                'name_tr' => 'Dijital Mentor',
                'name_en' => 'Digital Mentor',
            ],
            [
                'id' => 4,
                'name_tr' => 'Raporlar',
                'name_en' => 'Reports',
            ],
            [
                'id' => 5,
                'name_tr' => 'Tesisler',
                'name_en' => 'Facilities',
            ],
        ];

        $subjectLanguages = [];
        foreach ($subjects as &$s) {
            $s['created_at'] = now();
            $s['updated_at'] = now();
            $subjectLanguages[] = Language::createLanguage($s['name_tr'], LanguageEnum::TURKISH, $s['id'], FeedbackSubjectEnum::class, 'subject');
            $subjectLanguages[] = Language::createLanguage($s['name_en'], LanguageEnum::ENGLISH, $s['id'], FeedbackSubjectEnum::class, 'subject');
            unset($s['name_tr']);
            unset($s['name_en']);
        }
        Language::query()->insert($subjectLanguages);
    }
}
