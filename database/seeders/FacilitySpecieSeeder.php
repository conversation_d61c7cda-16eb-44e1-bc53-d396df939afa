<?php

namespace Database\Seeders;

use App\Models\FacilitySpecie;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FacilitySpecieSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (FacilitySpecie::query()->count() > 0) {
            return;
        }
        $species = [
            ['id' => 1, 'name' => 'Genel Merkez', 'order' => 1],
            ['id' => 2, 'name' => 'Fabrika', 'order' => 2],
            ['id' => 3, 'name' => 'Atölye', 'order' => 3],
            ['id' => 4, 'name' => 'Tedarikçi', 'order' => 4],
            ['id' => 5, 'name' => 'Atık Tesisi', 'order' => 5],
            ['id' => 6, 'name' => 'Arıtma Tesisi', 'order' => 6],
            ['id' => 7, 'name' => 'Diğer', 'order' => 7],
        ];
        foreach ($species as &$s) {
            $s['created_at'] = now();
            $s['updated_at'] = now();
        }
        FacilitySpecie::query()->insert($species);
    }
}
