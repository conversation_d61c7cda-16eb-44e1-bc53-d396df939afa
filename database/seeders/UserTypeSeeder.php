<?php

namespace Database\Seeders;

use App\Models\UserType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (UserType::query()->count() > 0) {
            return;
        }
        $types = [
            ['id' => 1, 'name' => 'İhracatçı Firma'],
            ['id' => 2, 'name' => 'İhracatçı Birlikleri Genel Sekreterlikleri'],
        ];
        foreach ($types as &$t) {
            $t['created_at'] = now();
            $t['updated_at'] = now();
        }
        UserType::query()->insert($types);
    }
}
