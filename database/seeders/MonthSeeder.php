<?php

namespace Database\Seeders;

use App\Enums\MonthEnum;
use App\Models\Language;
use App\Enums\LanguageEnum;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MonthSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Is exist control
        $addedLanguages = Language::query()->where('related_model', '=', MonthEnum::class)->get();
        if ($addedLanguages->count() > 0) {
            return;
        }

        // Old month model languages
        Language::query()->where('related_model', 'App\\Models\\Month')->delete();

        // Add month enum languages
        $monthIds = MonthEnum::values();
        $languages = [];
        foreach ($monthIds as $id) {
            $languages[] = $this->createLanguageForMonth($id, LanguageEnum::TURKISH);
            $languages[] = $this->createLanguageForMonth($id, LanguageEnum::ENGLISH);
        }
        Language::query()->insert($languages);
    }

    private function createLanguageForMonth(int $monthId, LanguageEnum $lang): array
    {
        return [
            'name' => now()->day(1)->month($monthId)->locale($lang->toString())->translatedFormat('F'),
            'lang_type' => $lang,
            'related_id' => $monthId,
            'related_model' => MonthEnum::class,
            'related_property' => 'value',
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
