<?php

namespace Database\Seeders;

use App\Models\CompanyPersonnelNumber;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanyPersonnelNumberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (CompanyPersonnelNumber::query()->count() > 0) {
            return;
        }
        $personnelNumbers = [
            ['id' => 1, 'number_range' => '0-20'],
            ['id' => 2, 'number_range' => '21-50'],
            ['id' => 3, 'number_range' => '51-100'],
            ['id' => 4, 'number_range' => '101-200'],
            ['id' => 5, 'number_range' => '201-500'],
            ['id' => 6, 'number_range' => '501-1000'],
            ['id' => 7, 'number_range' => '1001-3000'],
            ['id' => 8, 'number_range' => '3001-5000'],
            ['id' => 9, 'number_range' => '5000+'],
        ];
        foreach ($personnelNumbers as &$p) {
            $p['created_at'] = now();
            $p['updated_at'] = now();
        }
        CompanyPersonnelNumber::query()->insert($personnelNumbers);
    }
}
