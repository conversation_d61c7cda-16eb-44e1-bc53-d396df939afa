<?php

namespace Database\Seeders;

use App\Models\Config;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configs = [
            ['id' => 1, 'key' => 'register_type_id', 'value' => 1],
        ];
        foreach ($configs as &$c) {
            $c['created_at'] = now();
            $c['updated_at'] = now();
        }
        if (Config::query()->count() == 0) {
            Config::query()->insert($configs);
        }

        $applicationTypeConfig = Config::query()->where('key', 'application_user_type_id')->first();
        if ($applicationTypeConfig) {
            $applicationTypeConfig->key = 'register_type_id';
            $applicationTypeConfig->save();
        }
        $lastYearNumbersConfig = Config::query()->where('key', 'last_year_numbers')->first();
        if (!$lastYearNumbersConfig) {
            $lastYearNumbersConfig = new Config();
            $lastYearNumbersConfig->key = 'last_year_numbers';
            $lastYearNumbersConfig->value = '5';
            $lastYearNumbersConfig->save();
        }
    }
}
