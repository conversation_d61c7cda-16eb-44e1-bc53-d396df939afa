<?php

namespace Database\Seeders;

use App\Enums\LanguageEnum;
use App\Enums\PermissionEnum;
use App\Enums\RoleEnum;
use App\Models\Language;
use App\Models\Role;
use App\Models\RolePermission;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'id' => 1,
                'slug' => RoleEnum::MANAGER,
                'name_tr' => 'Yönetici',
                'name_en' => 'Manager',
            ],
            [
                'id' => 2,
                'slug' => RoleEnum::ASSISTANT,
                'name_tr' => 'Yönetici Yardımcısı',
                'name_en' => 'Assistant Manager',
            ],
            [
                'id' => 3,
                'slug' => RoleEnum::DATA,
                'name_tr' => 'Veri Sorumlusu',
                'name_en' => 'Data Controller',
            ],
            [
                'id' => 4,
                'slug' => RoleEnum::EMPLOYEE,
                'name_tr' => 'Çalışan',
                'name_en' => 'Employee',
            ],
        ];
        $permissions = [
            [
                'id' => PermissionEnum::AUTHORIZATION->toInt(),
                'name_tr' => 'Yetkilendirme',
                'name_en' => 'Authorization',
            ],
            [
                'id' => PermissionEnum::EDUCATION->toInt(),
                'name_tr' => 'Eğitim',
                'name_en' => 'Education',
            ],
            [
                'id' => PermissionEnum::FORM->toInt(),
                'name_tr' => 'Analiz',
                'name_en' => 'Analysis',
            ],
            [
                'id' => PermissionEnum::REPORT->toInt(),
                'name_tr' => 'Rapor',
                'name_en' => 'Report',
            ],
            [
                'id' => PermissionEnum::FACILITY_LIST->toInt(),
                'name_tr' => 'Tesis Listeleme',
                'name_en' => 'Facility List',
            ],
            [
                'id' => PermissionEnum::FACILITY_OPERATION->toInt(),
                'name_tr' => 'Tesis Değiştirme',
                'name_en' => 'Facility Operation',
            ],
        ];
        $rolePermissions = [
            ['role_id' => 1, 'permission_id' => PermissionEnum::AUTHORIZATION->toInt()],
            ['role_id' => 1, 'permission_id' => PermissionEnum::EDUCATION->toInt()],
            ['role_id' => 1, 'permission_id' => PermissionEnum::FORM->toInt()],
            ['role_id' => 1, 'permission_id' => PermissionEnum::REPORT->toInt()],
            ['role_id' => 1, 'permission_id' => PermissionEnum::FACILITY_LIST->toInt()],
            ['role_id' => 1, 'permission_id' => PermissionEnum::FACILITY_OPERATION->toInt()],

            ['role_id' => 2, 'permission_id' => PermissionEnum::EDUCATION->toInt()],
            ['role_id' => 2, 'permission_id' => PermissionEnum::FORM->toInt()],
            ['role_id' => 2, 'permission_id' => PermissionEnum::REPORT->toInt()],
            ['role_id' => 2, 'permission_id' => PermissionEnum::FACILITY_LIST->toInt()],
            ['role_id' => 2, 'permission_id' => PermissionEnum::FACILITY_OPERATION->toInt()],

            ['role_id' => 3, 'permission_id' => PermissionEnum::EDUCATION->toInt()],
            ['role_id' => 3, 'permission_id' => PermissionEnum::FORM->toInt()],
            ['role_id' => 3, 'permission_id' => PermissionEnum::FACILITY_LIST->toInt()],

            ['role_id' => 4, 'permission_id' => PermissionEnum::EDUCATION->toInt()],
        ];
        $roleLanguages = [];
        $permissionLanguages = [];
        foreach ($roles as &$r) {
            $r['created_at'] = now();
            $r['updated_at'] = now();
            $roleLanguages[] = Language::createLanguage($r['name_tr'], LanguageEnum::TURKISH, $r['id'], Role::class, 'name');
            $roleLanguages[] = Language::createLanguage($r['name_en'], LanguageEnum::ENGLISH, $r['id'], Role::class, 'name');
            unset($r['name_tr']);
            unset($r['name_en']);
        }
        foreach ($permissions as $p) {
            $permissionLanguages[] = Language::createLanguage($p['name_tr'], LanguageEnum::TURKISH, $p['id'], PermissionEnum::class, 'value');
            $permissionLanguages[] = Language::createLanguage($p['name_en'], LanguageEnum::ENGLISH, $p['id'], PermissionEnum::class, 'value');
        }
        foreach ($rolePermissions as &$rp) {
            $rp['created_at'] = now();
            $rp['updated_at'] = now();
        }

        DB::beginTransaction();
        try {
            if (Role::query()->count() == 0) {
                Role::query()->insert($roles);
                Language::query()->insert($roleLanguages);
            }
            if (RolePermission::query()->count() == 0) {
                RolePermission::query()->insert($rolePermissions);
                Language::query()->insert($permissionLanguages);
            }
            User::query()->where('role_id', '=', null)->update(['role_id' => 1]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Error($e);
        }
    }
}
