<?php

namespace Database\Seeders;

use App\Enums\LanguageEnum;
use App\Models\Language;
use App\Models\Tip;
use App\Models\TipContent;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Tip::query()->count() > 0) {
            return;
        }
        $tips = [
            [
                'id' => 1,
                'action' => 'first-login',
                'contents' => [
                    [
                        'id' => 1,
                        'tip_id' => 1,
                        'front_slug' => 'dashboard',
                        'title_tr' => 'Ana Sayfa İlk Görünüm',
                        'description_tr' => 'Anasayfayı ilk defa görüntüleme açıklaması',
                        'level_tr' => 'Başlangıç Seviye',
                        'title_en' => 'First View of Dashboard',
                        'description_en' => 'First view of dashboard description',
                        'level_en' => 'Basic Level',
                    ],
                    [
                        'id' => 2,
                        'tip_id' => 1,
                        'front_slug' => 'education-topic',
                        'title_tr' => 'Eğitim Başlıkları İlk Görünüm',
                        'description_tr' => 'Eğitim başlıklarını ilk defa görüntüleme açıklaması',
                        'level_tr' => 'Başlangıç Seviye',
                        'title_en' => 'First View of Education Topics',
                        'description_en' => 'First view of education topics description',
                        'level_en' => 'Basic Level',
                    ],
                    [
                        'id' => 3,
                        'tip_id' => 1,
                        'front_slug' => 'education-lesson',
                        'title_tr' => 'Eğitim Derslerini İlk Görünüm',
                        'description_tr' => 'Eğitim derslerini ilk defa görüntüleme açıklaması',
                        'level_tr' => 'Başlangıç Seviye',
                        'title_en' => 'First View of Education Lessons',
                        'description_en' => 'First view of education lessons description',
                        'level_en' => 'Basic Level',
                    ],
                    [
                        'id' => 4,
                        'tip_id' => 1,
                        'front_slug' => 'form-category',
                        'title_tr' => 'Kategoriler İlk Görünüm',
                        'description_tr' => 'Kategorileri ilk defa görüntüleme açıklaması',
                        'level_tr' => 'Başlangıç Seviye',
                        'title_en' => 'First View of Categories',
                        'description_en' => 'First view of categories description',
                        'level_en' => 'Basic Level',
                    ],
                ],
            ],
            [
                'id' => 2,
                'action' => 'report-save',
                'contents' => [
                    [
                        'id' => 5,
                        'tip_id' => 2,
                        'front_slug' => 'report',
                        'title_tr' => 'Rapor Kaydetme',
                        'description_tr' => 'Rapor kaydetme açıklaması',
                        'level_tr' => 'Orta Seviye',
                        'title_en' => 'Save Report',
                        'description_en' => 'Save report description',
                        'level_en' => 'Medium Level',
                    ],
                ],
            ]
        ];

        $tipContents = [];
        $languages = [];
        foreach ($tips as &$t) {
            $t['created_at'] = now();
            $t['updated_at'] = now();
            foreach ($t['contents'] as &$c) {
                $c['created_at'] = now();
                $c['updated_at'] = now();
                $languages = array_merge($languages, $this->createLanguageForTipContent($c, LanguageEnum::TURKISH));
                $languages = array_merge($languages, $this->createLanguageForTipContent($c, LanguageEnum::ENGLISH));
                unset($c['title_tr']);
                unset($c['description_tr']);
                unset($c['level_tr']);
                unset($c['title_en']);
                unset($c['description_en']);
                unset($c['level_en']);
                $tipContents[] = $c;
            }
            unset($t['contents']);
        }

        DB::beginTransaction();
        try {
            Tip::query()->insert($tips);
            TipContent::query()->insert($tipContents);
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Error($e);
        }
    }

    private function createLanguageForTipContent(array $content, LanguageEnum $lang): array
    {
        return [
            [
                'name' => $content['title_' . $lang->toString()],
                'lang_type' => $lang,
                'related_id' => $content['id'],
                'related_model' => TipContent::class,
                'related_property' => 'title',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => $content['description_' . $lang->toString()],
                'lang_type' => $lang,
                'related_id' => $content['id'],
                'related_model' => TipContent::class,
                'related_property' => 'description',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => $content['level_' . $lang->toString()],
                'lang_type' => $lang,
                'related_id' => $content['id'],
                'related_model' => TipContent::class,
                'related_property' => 'level',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
    }
}
