<?php

namespace Database\Seeders;

use App\Models\CompanySector;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CompanySectorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sectors = [
            ['id' => 1, 'name' => 'Hububat, Bakliyat, Yağlı Tohumlar ve Mamulleri'],
            ['id' => 2, 'name' => 'Yaş Meyve ve Sebze'],
            ['id' => 3, 'name' => '<PERSON>yve Sebze Mamulleri'],
            ['id' => 4, 'name' => 'Kuru Meyve ve Mamulleri'],
            ['id' => 5, 'name' => 'Fındık ve Mamulleri'],
            ['id' => 6, 'name' => '<PERSON><PERSON><PERSON> ve Zeytinyağı'],
            ['id' => 7, 'name' => 'Tütün'],
            ['id' => 8, 'name' => 'Süs Bitkileri ve Mamulleri'],
            ['id' => 9, 'name' => '<PERSON> Ürünleri ve Hayvansal Mamuller'],
            ['id' => 10, 'name' => '<PERSON><PERSON><PERSON>, Kağıt ve Orman Ürünleri'],
            ['id' => 11, 'name' => 'Tekstil ve Hammaddeleri'],
            ['id' => 12, 'name' => 'Deri ve Deri Mamulleri'],
            ['id' => 13, 'name' => 'Halı'],
            ['id' => 14, 'name' => 'Kimyevi Maddeler ve Mamulleri'],
            ['id' => 15, 'name' => 'Hazırgiyim ve Konfeksiyon'],
            ['id' => 16, 'name' => 'Otomotiv Endüstrisi'],
            ['id' => 17, 'name' => 'Gemi, Yat ve Hizmetleri'],
            ['id' => 18, 'name' => 'Elektrik ve Elektronik'],
            ['id' => 19, 'name' => 'Makine ve Aksamları'],
            ['id' => 20, 'name' => 'Demir ve Demir Dışı Metaller'],
            ['id' => 21, 'name' => 'Çelik'],
            ['id' => 22, 'name' => 'Çimento Cam Seramik ve Toprak Ürünleri'],
            ['id' => 23, 'name' => 'Mücevher'],
            ['id' => 24, 'name' => 'Savunma ve Havacılık Sanayii'],
            ['id' => 25, 'name' => 'İklimlendirme Sanayii'],
            ['id' => 26, 'name' => 'Madencilik Ürünleri'],
            ['id' => 27, 'name' => 'Diğer'],
        ];
        for ($i = 0; $i < count($sectors); $i++) {
            $sectors[$i]['order'] = $i + 1;
            $sectors[$i]['created_at'] = now();
            $sectors[$i]['updated_at'] = now();
        }
        $dbSectors = CompanySector::all();
        if ($dbSectors->count() == 0) {
            CompanySector::query()->insert($sectors);
        } else if ($dbSectors->count() > 0 && $dbSectors->first()->order == 0) {
            for ($i = 0; $i < $dbSectors->count(); $i++) {
                $dbSectors[$i]->order = $i + 1;
                $dbSectors[$i]->save();
            }
        }
    }
}
