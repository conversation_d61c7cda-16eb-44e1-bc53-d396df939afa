<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_inosuit_application_answers', function (Blueprint $table) {
            $table->unsignedSmallInteger('answer')->nullable()->change();
            $table->string('answer_text', 3000)->nullable()->after('answer');
        });
        Schema::table('mentor_inosuit_applications', function (Blueprint $table) {
            $table->string('organization_type')->nullable()->after('extra_urls');
            $table->string('organization_name')->nullable()->after('organization_type');
            $table->unsignedTinyInteger('experience_year')->nullable()->after('organization_name');
            $table->boolean('is_sustainable')->nullable()->after('experience_year');
            $table->string('sustainable_text', 3000)->nullable()->after('is_sustainable');
            $table->string('iso_certificate_text', 3000)->nullable()->after('sustainable_text');
            $table->string('contact_full_name')->nullable()->change();
            $table->string('contact_email')->nullable()->change();
            $table->string('contact_phone', 30)->nullable()->change();
            $table->string('contact_title')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
