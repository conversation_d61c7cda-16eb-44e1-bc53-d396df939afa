<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_values', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('input_id');
            $table->unsignedBigInteger('group_value_id');
            $table->unsignedBigInteger('report_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('input_id')->references('id')->on('form_inputs');
            $table->foreign('group_value_id')->references('id')->on('form_group_values');
            $table->foreign('report_id')->references('id')->on('reports');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_values');
    }
};
