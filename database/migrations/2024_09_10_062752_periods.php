<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('periods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::create('company_periods', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('period_id');
            $table->unsignedBigInteger('mentor_user_id')->nullable();
            $table->string('full_name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone', 30)->nullable();
            $table->string('title')->nullable();
            $table->string('result_report', 1000)->nullable();
            $table->unsignedTinyInteger('status')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_id')->references('id')->on('companies');
            $table->foreign('period_id')->references('id')->on('periods');
            $table->foreign('mentor_user_id')->references('id')->on('users');
        });
        Schema::create('company_meetings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_period_id');
            $table->string('name')->nullable();
            $table->string('description', 500)->nullable();
            $table->string('location')->nullable();
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_period_id')->references('id')->on('company_periods');
        });
        Schema::create('mentor_periods', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('period_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('period_id')->references('id')->on('periods');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->string('description', 500)->nullable()->after('photo');
            $table->string('address', 500)->nullable()->after('description');
        });
        Schema::table('companies', function (Blueprint $table) {
            $table->string('description', 500)->nullable()->change();
            $table->string('address', 500)->nullable()->change();

            $table->dropForeign('companies_mentor_user_id_foreign');
            $table->dropIndex('companies_mentor_user_id_foreign');
            $table->dropColumn('mentor_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
