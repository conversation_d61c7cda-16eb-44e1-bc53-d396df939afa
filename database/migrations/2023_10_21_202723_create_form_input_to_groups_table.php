<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('form_input_to_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('input_id');
            $table->unsignedBigInteger('group_id');
            $table->unsignedBigInteger('related_group_value_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('input_id')->references('id')->on('form_inputs');
            $table->foreign('group_id')->references('id')->on('form_groups');
            $table->foreign('related_group_value_id')->references('id')->on('form_group_values');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_input_to_groups');
    }
};
