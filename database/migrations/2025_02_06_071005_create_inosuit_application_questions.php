<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_inosuit_application_questions', function (Blueprint $table) {
            $table->id();
            $table->string('name', 1000);
            $table->unsignedTinyInteger('order');
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::create('company_inosuit_application_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('application_id');
            $table->unsignedBigInteger('question_id');
            $table->unsignedTinyInteger('answer');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('application_id')->references('id')->on('company_inosuit_applications');
            $table->foreign('question_id')->references('id')->on('company_inosuit_application_questions');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_inosuit_application_answers');
        Schema::dropIfExists('company_inosuit_application_questions');
    }
};
