<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_uncertainty_pedigrees', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_uncertainty_id');
            $table->unsignedTinyInteger('tab_type');
            $table->unsignedTinyInteger('value');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('report_uncertainty_id')->references('id')->on('report_uncertainties');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_uncertainty_pedigrees');
    }
};
