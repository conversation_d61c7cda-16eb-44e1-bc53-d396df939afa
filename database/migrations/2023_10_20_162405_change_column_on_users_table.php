<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('is_approve', 'is_approved_password');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_approved_password')->default(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('is_approved_password', 'is_approve');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_approve')->default(false)->change();
        });
    }
};
