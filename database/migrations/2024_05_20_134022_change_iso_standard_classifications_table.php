<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('iso_standard_classifications', function (Blueprint $table) {
            $table->renameColumn('gas', 'gasses');
        });
        Schema::table('iso_standard_classifications', function (Blueprint $table) {
            $table->json('gasses')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
