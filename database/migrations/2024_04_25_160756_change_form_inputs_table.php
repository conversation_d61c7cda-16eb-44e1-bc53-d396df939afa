<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_inputs', function (Blueprint $table) {
            $table->unsignedTinyInteger('type_id')
                ->default(\App\Enums\FormInputTypeEnum::DROPDOWN)
                ->after('related_input_id');
            $table->boolean('is_multiple')
                ->default(false)
                ->after('type_id');
            $table->boolean('is_file')
                ->default(false)
                ->after('is_multiple');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
