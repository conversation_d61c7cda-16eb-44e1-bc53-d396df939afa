<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::drop('iso_standard_applications');
        Schema::table('iso_standards', function (Blueprint $table) {
            $table->string('code')->unique()->nullable()->after('company_id');
            $table->boolean('pdf_created')->default(false)->after('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
