<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_privileges', function (Blueprint $table) {
            $table->id();
            $table->string('slug', 50)->unique();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::table('companies', function (Blueprint $table) {
            $table->string('description', 300)->nullable()->after('name');
            $table->unsignedBigInteger('mentor_user_id')->nullable()->after('description');
            $table->boolean('is_active')->default(true)->after('sector_id');
            $table->string('email')->nullable()->after('tax_number');
            $table->string('phone', 30)->nullable()->after('email');
            $table->string('fax', 30)->nullable()->after('phone');
            $table->string('website', 100)->nullable()->after('fax');
            $table->string('logo')->nullable()->after('website');
            $table->string('address', 300)->nullable()->after('logo');

            $table->foreign('mentor_user_id')->references('id')->on('users');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone', 30)->nullable()->change();
            $table->unsignedBigInteger('privilege_id')->nullable()->after('role_id');

            $table->foreign('privilege_id')->references('id')->on('role_privileges');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
