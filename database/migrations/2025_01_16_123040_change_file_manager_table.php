<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('file_manager', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
            $table->unsignedSmallInteger('type')->nullable()->change();
            $table->unsignedInteger('size')->nullable()->change();
            $table->boolean('s3')->default(false)->after('size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
