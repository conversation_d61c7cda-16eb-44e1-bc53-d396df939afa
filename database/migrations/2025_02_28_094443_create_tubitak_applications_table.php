<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tubitak_applications', function (Blueprint $table) {
            $table->id();
            $table->string('company_name');
            $table->string('tax_number', 50);
            $table->string('city');
            $table->string('state');
            $table->double('turnover');
            $table->unsignedInteger('personnel_number');
            $table->unsignedBigInteger('sector_id');
            $table->boolean('is_prodis');
            $table->string('manager_name');
            $table->string('manager_surname');
            $table->string('manager_email');
            $table->string('manager_phone',30);
            $table->string('manager_title');
            $table->string('contact_name');
            $table->string('contact_surname');
            $table->string('contact_email');
            $table->string('contact_phone',30);
            $table->string('contact_title');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('sector_id')->references('id')->on('company_sectors');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tubitak_applications');
    }
};
