<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mentors', function (Blueprint $table) {
            $table->id();
            $table->string('action')->unique();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('mentor_contents', function (Blueprint $table) {
            $table->id();
            $table->string('front_slug');
            $table->unsignedBigInteger('mentor_id');
            $table->timestamps();
            $table->softDeletes();
            $table->foreign('mentor_id')->references('id')->on('mentors');
        });

        Schema::create('mentor_content_users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('content_id');
            $table->unsignedBigInteger('user_id');
            $table->boolean('is_read')->default(false);
            $table->timestamps();
            $table->softDeletes();
            $table->foreign('content_id')->references('id')->on('mentor_contents');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mentor_content_users');
        Schema::dropIfExists('mentor_contents');
        Schema::dropIfExists('mentors');
    }
};
