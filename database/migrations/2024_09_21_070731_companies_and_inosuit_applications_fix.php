<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->unsignedBigInteger('personnel_number_id')->nullable()->change();
        });
        Schema::table('company_inosuit_applications', function (Blueprint $table) {
            $table->tinyInteger('status')->default(0)->after('contact_title');
        });
        Schema::table('mentor_inosuit_applications', function (Blueprint $table) {
            $table->tinyInteger('status')->default(0)->after('contact_title');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
