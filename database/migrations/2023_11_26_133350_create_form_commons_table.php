<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('form_commons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('form_id');
            $table->unsignedBigInteger('input_id');
            $table->unsignedBigInteger('group_value_id')->nullable();
            $table->unsignedBigInteger('common_input_id');
            $table->unsignedBigInteger('common_group_value_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('form_id')->references('id')->on('forms');
            $table->foreign('input_id')->references('id')->on('form_inputs');
            $table->foreign('group_value_id')->references('id')->on('form_group_values');
            $table->foreign('common_input_id')->references('id')->on('form_inputs');
            $table->foreign('common_group_value_id')->references('id')->on('form_group_values');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_commons');
    }
};
