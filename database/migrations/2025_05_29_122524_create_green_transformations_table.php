<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('green_transformations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('code');
            $table->unsignedBigInteger('sector_id');
            $table->string('full_name');
            $table->string('email')->unique();
            $table->string('title');
            $table->string('phone', 30);
            $table->string('company_name');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('sector_id')->references('id')->on('company_sectors');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('green_transformations');
    }
};
