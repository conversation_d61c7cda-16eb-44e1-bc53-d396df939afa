<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropConstrainedForeignId('scope_id');
        });
        Schema::drop('scopes');
        Schema::create('form_categories', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::table('forms', function (Blueprint $table) {
            $table->unsignedBigInteger('category_id')->after('is_locked');
            $table->foreign('category_id')->references('id')->on('form_categories');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropConstrainedForeignId('category_id');
        });
        Schema::dropIfExists('form_categories');
        Schema::create('scopes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description');
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::table('forms', function (Blueprint $table) {
            $table->unsignedBigInteger('scope_id')->after('is_locked');
            $table->foreign('scope_id')->references('id')->on('scopes');
        });
    }
};
