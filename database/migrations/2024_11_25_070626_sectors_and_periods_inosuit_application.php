<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_sectors', function (Blueprint $table) {
            $table->boolean('visible_inosuit_application')->default(false)->after('order');
        });
        Schema::table('periods', function (Blueprint $table) {
            $table->boolean('visible_inosuit_application')->default(false)->after('end_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
