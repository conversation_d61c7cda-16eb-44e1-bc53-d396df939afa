<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE company_meetings MODIFY `status` TINYINT DEFAULT 0");
        DB::statement("ALTER TABLE company_papers MODIFY `status` TINYINT DEFAULT 0");
        DB::statement("ALTER TABLE company_periods MODIFY `status` TINYINT DEFAULT 0");
        DB::statement("ALTER TABLE mentor_papers MODIFY `status` TINYINT DEFAULT 0");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
