<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_inputs', function (Blueprint $table) {
            $table->dropForeign(['related_input_id']);
        });
        Schema::table('form_inputs', function (Blueprint $table) {
            $table->string('related_input_id', 20)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('form_inputs', function (Blueprint $table) {
            //
        });
    }
};
