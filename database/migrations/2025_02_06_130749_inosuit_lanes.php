<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('period_lanes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('period_id');
            $table->unsignedTinyInteger('order');
            $table->boolean('is_preparation')->default(false);
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('period_id')->references('id')->on('periods');
        });
        Schema::table('company_periods', function (Blueprint $table) {
            $table->unsignedBigInteger('lane_id')->nullable()->after('period_id');

            $table->foreign('lane_id')->references('id')->on('period_lanes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
