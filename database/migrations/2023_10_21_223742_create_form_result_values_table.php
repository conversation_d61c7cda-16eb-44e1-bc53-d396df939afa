<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('form_result_values', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_value_id');
            $table->unsignedBigInteger('result_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('group_value_id')->references('id')->on('form_group_values');
            $table->foreign('result_id')->references('id')->on('form_results');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_result_values');
    }
};
