<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->dropColumn('info');
        });
        Schema::table('form_inputs', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->dropColumn('info');
        });
        Schema::table('form_group_values', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->dropColumn('info');
        });
        Schema::table('form_groups', function (Blueprint $table) {
            $table->string('name')->unique()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->string('name')->after('id');
            $table->string('info')->nullable()->after('slug');
        });
        Schema::table('form_inputs', function (Blueprint $table) {
            $table->string('name')->after('id');
            $table->string('info')->nullable()->after('name');
        });
        Schema::table('form_group_values', function (Blueprint $table) {
            $table->string('name')->after('id');
            $table->string('info')->nullable()->after('name');
        });
        Schema::table('form_groups', function (Blueprint $table) {
            $table->string('name')->change();
        });
    }
};
