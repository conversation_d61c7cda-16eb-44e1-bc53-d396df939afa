<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('iso_standards', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('company_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('company_id')->references('id')->on('companies');
        });

        Schema::create('iso_standard_values', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('iso_standard_id');
            $table->string('key');
            $table->json('value');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('iso_standard_id')->references('id')->on('iso_standards');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('iso_standard_values');
        Schema::dropIfExists('iso_standards');
    }
};
