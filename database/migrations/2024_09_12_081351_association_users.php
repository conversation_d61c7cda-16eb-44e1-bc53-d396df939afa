<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mentors', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('address');
        });
        Schema::table('associations', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('name');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('association_id')->nullable()->after('company_id');
            $table->foreign('association_id')->references('id')->on('associations');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
