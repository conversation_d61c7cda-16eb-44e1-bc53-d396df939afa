<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('fax');
            $table->string('linkedin_url')->nullable()->after('website');
            $table->string('twitter_url')->nullable()->after('linkedin_url');
        });
        Schema::table('company_inosuit_applications', function (Blueprint $table) {
            $table->boolean('is_arge')->default(false)->after('is_kosgeb');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
