<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_uncertainty_electricity', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_uncertainty_id');
            $table->unsignedTinyInteger('meter_type');
            $table->unsignedTinyInteger('class_type');
            $table->unsignedTinyInteger('temperature_type');
            $table->double('min_current')->nullable();
            $table->double('transfer_current');
            $table->double('max_current');
            $table->double('expected_current');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('report_uncertainty_id')->references('id')->on('report_uncertainties');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
