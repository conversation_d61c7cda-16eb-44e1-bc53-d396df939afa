<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::drop('report_values');

        Schema::table('reports', function (Blueprint $table) {
            $table->unsignedBigInteger('result_id')->after('facility_id');
            $table->foreign('result_id')->references('id')->on('form_results');
        });

        Schema::table('form_result_values', function (Blueprint $table) {
            $table->unsignedBigInteger('input_id')->after('id');
            $table->foreign('input_id')->references('id')->on('form_inputs');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('form_result_values', function (Blueprint $table) {
            $table->dropConstrainedForeignId('input_id');
            $table->dropColumn('input_id');
        });

        Schema::table('reports', function (Blueprint $table) {
            $table->dropConstrainedForeignId('result_id');
            $table->dropColumn('result_id');
        });

        Schema::create('report_values', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('input_id');
            $table->unsignedBigInteger('group_value_id');
            $table->unsignedBigInteger('report_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('input_id')->references('id')->on('form_inputs');
            $table->foreign('group_value_id')->references('id')->on('form_group_values');
            $table->foreign('report_id')->references('id')->on('reports');
        });
    }
};
