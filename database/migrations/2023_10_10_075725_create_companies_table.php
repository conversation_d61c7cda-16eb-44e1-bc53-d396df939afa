<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('personnel_number_id');
            $table->unsignedBigInteger('sector_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('personnel_number_id')->references('id')->on('company_personnel_numbers');
            $table->foreign('sector_id')->references('id')->on('company_sectors');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
