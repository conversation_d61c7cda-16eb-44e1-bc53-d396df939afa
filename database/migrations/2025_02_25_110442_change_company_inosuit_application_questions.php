<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_inosuit_application_questions', function (Blueprint $table) {
            $table->unsignedTinyInteger('input_type')
                ->default(\App\Enums\FormInputTypeEnum::DROPDOWN->value)
                ->after('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
