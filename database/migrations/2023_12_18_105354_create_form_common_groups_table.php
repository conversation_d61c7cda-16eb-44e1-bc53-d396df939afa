<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_commons', function (Blueprint $table) {
            $table->dropConstrainedForeignId('input_id');
            $table->dropConstrainedForeignId('group_value_id');
            $table->dropConstrainedForeignId('common_input_id');
            $table->dropConstrainedForeignId('common_group_value_id');

            $table->unsignedBigInteger('common_form_id')->after('form_id');
            $table->foreign('common_form_id')->references('id')->on('forms');
        });
        Schema::create('form_common_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('common_id');
            $table->unsignedBigInteger('input_id');
            $table->unsignedBigInteger('group_value_id')->nullable();
            $table->unsignedBigInteger('common_input_id');
            $table->unsignedBigInteger('common_group_value_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('common_id')->references('id')->on('form_commons');
            $table->foreign('input_id')->references('id')->on('form_inputs');
            $table->foreign('group_value_id')->references('id')->on('form_group_values');
            $table->foreign('common_input_id')->references('id')->on('form_inputs');
            $table->foreign('common_group_value_id')->references('id')->on('form_group_values');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('form_common_groups');
    }
};
