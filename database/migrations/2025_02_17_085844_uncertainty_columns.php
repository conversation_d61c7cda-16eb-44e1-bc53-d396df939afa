<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->unsignedTinyInteger('uncertainty_type')->nullable()->after('has_gwp');
        });
        Schema::table('form_inputs', function (Blueprint $table) {
            $table->boolean('is_uncertainty')->default(false)->after('is_file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
