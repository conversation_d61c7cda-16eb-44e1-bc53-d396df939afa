<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('nace_codes', function (Blueprint $table) {
            $table->id();
            $table->string('sector_code', 20);
            $table->string('sector_description');
            $table->string('occupation_code', 20);
            $table->string('occupation_description');
            $table->string('nace_code', 20);
            $table->string('nace_description', 1000);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('company_nace_codes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('nace_code_id')->nullable();
            $table->string('nace_code', 20)->nullable();
            $table->string('nace_description', 1000)->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_id')->references('id')->on('companies');
            $table->foreign('nace_code_id')->references('id')->on('nace_codes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('nace_codes');
    }
};
