<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mentor_content_users', function (Blueprint $table) {
            $table->dropForeign('mentor_content_users_content_id_foreign');
            $table->dropIndex('mentor_content_users_content_id_foreign');

            $table->dropForeign('mentor_content_users_user_id_foreign');
            $table->dropIndex('mentor_content_users_user_id_foreign');
        });
        Schema::table('mentor_contents', function (Blueprint $table) {
            $table->dropForeign('mentor_contents_mentor_id_foreign');
            $table->dropIndex('mentor_contents_mentor_id_foreign');
        });
        Schema::rename('mentors', 'tips');
        Schema::rename('mentor_contents', 'tip_contents');
        Schema::rename('mentor_content_users', 'tip_content_users');
        Schema::table('tip_contents', function (Blueprint $table) {
            $table->renameColumn('mentor_id', 'tip_id');
            $table->foreign('tip_id')->references('id')->on('tips');
        });
        Schema::table('tip_content_users', function (Blueprint $table) {
            $table->foreign('content_id')->references('id')->on('tip_contents');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
