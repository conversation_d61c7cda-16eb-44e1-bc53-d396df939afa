<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_categories', function (Blueprint $table) {
            $table->tinyInteger('order')->default(0)->after('id');
        });

        Schema::table('forms', function (Blueprint $table) {
            $table->tinyInteger('order')->default(0)->after('category_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropColumn('order');
        });

        Schema::table('form_categories', function (Blueprint $table) {
            $table->dropColumn('order');
        });
    }
};
