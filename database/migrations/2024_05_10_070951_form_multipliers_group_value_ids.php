<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('form_multipliers', function (Blueprint $table) {
            $table->dropForeign(['group_value_id']);
            $table->dropIndex('form_multipliers_group_value_id_foreign');
        });
        Schema::table('form_multipliers', function (Blueprint $table) {
            $table->json('group_value_id')->change();
        });
        Schema::table('form_multipliers', function (Blueprint $table) {
            $table->renameColumn('group_value_id', 'group_value_ids');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
