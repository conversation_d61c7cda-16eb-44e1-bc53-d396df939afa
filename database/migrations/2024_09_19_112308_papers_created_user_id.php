<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_papers', function (Blueprint $table) {
            $table->unsignedBigInteger('created_user_id')->after('id');
            $table->foreign('created_user_id')->references('id')->on('users');
        });
        Schema::table('mentor_papers', function (Blueprint $table) {
            $table->unsignedBigInteger('created_user_id')->after('id');
            $table->foreign('created_user_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
