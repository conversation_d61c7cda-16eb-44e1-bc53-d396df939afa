<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->string('info')->nullable()->after('slug');
            $table->boolean('is_locked')->default(false)->after('info');
            $table->unsignedBigInteger('scope_id')->after('is_locked');

            $table->foreign('scope_id')->references('id')->on('scopes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forms', function (Blueprint $table) {
            $table->dropConstrainedForeignId('scope_id');
            $table->dropColumn(['scope_id', 'is_locked', 'info']);
        });
    }
};
