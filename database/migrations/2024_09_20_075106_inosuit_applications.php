<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_inosuit_applications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sector_id');
            $table->unsignedBigInteger('association_id');
            $table->string('name');
            $table->string('tax_number', 50);
            $table->string('email');
            $table->string('phone', 30);
            $table->string('website', 100);
            $table->string('address', 500);
            $table->string('activity', 500);
            $table->date('found_date');
            $table->unsignedInteger('white_collar_count');
            $table->unsignedInteger('blue_collar_count');
            $table->string('annual_sale');
            $table->string('balance_sheet');
            $table->boolean('is_kosgeb')->default(false);
            $table->string('manager_full_name');
            $table->string('manager_email');
            $table->string('manager_phone', 30);
            $table->string('manager_title');
            $table->string('contact_full_name');
            $table->string('contact_email');
            $table->string('contact_phone', 30);
            $table->string('contact_title');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('sector_id')->references('id')->on('company_sectors');
            $table->foreign('association_id')->references('id')->on('associations');
        });

        Schema::create('mentor_inosuit_applications', function (Blueprint $table) {
            $table->id();
            $table->string('full_name');
            $table->string('email');
            $table->string('phone', 30);
            $table->string('title');
            $table->string('linkedin_url');
            $table->string('cv_url');
            $table->string('image_url')->nullable();
            $table->unsignedBigInteger('university_id');
            $table->unsignedBigInteger('faculty_id');
            $table->unsignedBigInteger('department_id');
            $table->string('contact_full_name');
            $table->string('contact_email');
            $table->string('contact_phone', 30);
            $table->string('contact_title');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('university_id')->references('id')->on('edu_universities');
            $table->foreign('faculty_id')->references('id')->on('edu_faculties');
            $table->foreign('department_id')->references('id')->on('edu_departments');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
