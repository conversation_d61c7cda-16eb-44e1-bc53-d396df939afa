<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('calendars', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('goal')->nullable();
            $table->text('advice')->nullable();
            $table->text('term')->nullable();
            $table->text('tool')->nullable();
            $table->text('source')->nullable();
            $table->unsignedTinyInteger('order');
            $table->unsignedBigInteger('period_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('period_id')->references('id')->on('periods');
        });
        Schema::table('company_periods', function (Blueprint $table) {
            $table->unsignedBigInteger('calendar_id')->nullable()->after('mentor_user_id');

            $table->foreign('calendar_id')->references('id')->on('calendars');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
