<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('mentor_inosuit_applications', function (Blueprint $table) {
            $table->json('extra_urls')->nullable()->after('image_url');
        });
        Schema::table('mentor_papers', function (Blueprint $table) {
            $table->json('extra_urls')->nullable()->after('observation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
