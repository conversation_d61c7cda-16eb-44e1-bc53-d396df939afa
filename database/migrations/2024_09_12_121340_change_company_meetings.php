<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_meetings', function (Blueprint $table) {
            $table->renameColumn('status', 'company_status');
        });
        Schema::table('company_meetings', function (Blueprint $table) {
            $table->string('company_note', 500)->nullable()->after('company_status');
            $table->tinyInteger('mentor_status')->default(0)->after('company_note');
            $table->string('mentor_note', 500)->nullable()->after('mentor_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
