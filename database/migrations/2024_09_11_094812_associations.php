<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('associations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('company_periods', function (Blueprint $table) {
            $table->unsignedBigInteger('association_id')->nullable()->after('period_id');
            $table->foreign('association_id')->references('id')->on('associations');
        });
        Schema::create('company_papers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_period_id');
            $table->string('name');
            $table->string('observation', 500)->nullable();
            $table->unsignedTinyInteger('status')->default(0);
            $table->date('term_date');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_period_id')->references('id')->on('company_periods');
        });
        Schema::create('company_paper_meetings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_paper_id');
            $table->unsignedBigInteger('meeting_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_paper_id')->references('id')->on('company_papers');
            $table->foreign('meeting_id')->references('id')->on('company_meetings');
        });
        Schema::create('company_paper_questions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });
        Schema::create('company_paper_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_paper_id');
            $table->unsignedBigInteger('question_id');
            $table->unsignedTinyInteger('answer_type');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_paper_id')->references('id')->on('company_papers');
            $table->foreign('question_id')->references('id')->on('company_paper_questions');
        });

        Schema::create('mentor_papers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_period_id');
            $table->string('name');
            $table->string('activity_output', 500)->nullable();
            $table->string('unrealised_output', 500)->nullable();
            $table->string('observation', 500)->nullable();
            $table->unsignedTinyInteger('status')->default(0);
            $table->date('term_date');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_period_id')->references('id')->on('company_periods');
        });
        Schema::create('mentor_paper_meetings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('mentor_paper_id');
            $table->unsignedBigInteger('meeting_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mentor_paper_id')->references('id')->on('mentor_papers');
            $table->foreign('meeting_id')->references('id')->on('company_meetings');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
