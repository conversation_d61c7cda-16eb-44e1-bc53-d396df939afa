<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_papers', function (Blueprint $table) {
            $table->string('observation', 5000)->nullable()->change();
        });
        Schema::table('mentor_papers', function (Blueprint $table) {
            $table->string('activity_output', 5000)->nullable()->change();
            $table->string('unrealised_output', 5000)->nullable()->change();
            $table->string('observation', 5000)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
