<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('report_uncertainties', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_id');
            $table->unsignedDouble('uncertainty_value');
            $table->unsignedDouble('error_margin')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('report_id')->references('id')->on('reports');
        });
        Schema::table('reports', function (Blueprint $table) {
            $table->dropColumn('uncertainty_value');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_uncertainties');
    }
};
