<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('kvkk_approved')->default(false)->after('role_id');
            $table->boolean('clarification_text')->default(false)->after('kvkk_approved');
            $table->boolean('privacy_policy')->default(false)->after('clarification_text');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->removeColumn('privacy_policy');
            $table->removeColumn('clarification_text');
            $table->removeColumn('kvkk_approved');
        });
    }
};
