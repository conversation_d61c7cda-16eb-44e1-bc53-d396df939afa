<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reports', function (Blueprint $table) {
            $table->renameColumn('greenhouse_gas', 'carbon_dioxide');
        });
        Schema::table('reports', function (Blueprint $table) {
            $table->unsignedDouble('nitrous_oxide')->nullable()->after('carbon_dioxide');
            $table->unsignedDouble('methane')->nullable()->after('nitrous_oxide');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
