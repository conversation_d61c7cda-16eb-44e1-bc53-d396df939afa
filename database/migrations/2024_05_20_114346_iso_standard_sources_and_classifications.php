<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('iso_standard_sources', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('form_id');
            $table->string('name');
            $table->json('sources');
            $table->json('urls');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('form_id')->references('id')->on('forms');
        });
        Schema::create('iso_standard_classifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_value_id');
            $table->string('gas');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('group_value_id')->references('id')->on('form_group_values');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('iso_standard_sources');
    }
};
