<?php

namespace App\Utils;

use App\Enums\FrontProjectEnum;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\LanguageEnum;
use App\Enums\PermissionEnum;
use App\Http\Requests\GeneralRequest;
use Carbon\Carbon;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class GeneralUtil
{
    public static string $inosuitPeriodKey = 'x-inosuit-period-id';

    public static function getAppLanguage(): string
    {
        $language = request()->header('Accept-Language');
        if (!in_array($language, LanguageEnum::values())) {
            $language = app()->getLocale();
        }
        return $language;
    }

    /**
     * @return int[]
     */
    public static function getYearsAsArray(): array
    {
        /*
        $lastYearNumbers = (int)Config::query()->where('key', 'last_year_numbers')->firstOrFail()->value;
        $currentYear = now()->year;
        $years = [];
        for ($i = 0; $i < $lastYearNumbers; $i++) {
            $years[] = $currentYear--;
        }
        return $years;
        */
        $currentYear = now()->year;
        $years = [];
        for (; 2019 <= $currentYear; $currentYear--) {
            $years[] = $currentYear;
        }
        return $years;
    }

    public static function can(PermissionEnum ...$permissionEnums): string
    {
        $gates = [];
        foreach ($permissionEnums as $p) {
            $gates[] = $p->toInt();
        }
        return 'permission:' . implode(',', $gates);
    }

    public static function privileges(RolePrivilegeEnum ...$privilegeEnums): string
    {
        $gates = [];
        foreach ($privilegeEnums as $p) {
            $gates[] = $p->value;
        }
        return 'privilege:' . implode(',', $gates);
    }

    public static function numberFormat(float $number, int $decimal = 4): string
    {
        return number_format($number, $decimal, ',', '.');
    }

    public static function paginationValidations(GeneralRequest $request): array
    {
        if (!$request->page) {
            $request->merge(['page' => 1]);
        }
        if (!$request->limit) {
            $request->merge(['limit' => 10]);
        }
        return [
            'page' => 'integer|min:1',
            'limit' => 'integer|min:1|max:20',
        ];
    }

    public static function getFrontProject(): FrontProjectEnum
    {
        if (request()->headers->get('x-project') == FrontProjectEnum::ECOTIM->value) {
            return FrontProjectEnum::ECOTIM;
        } else if (request()->headers->get('x-project') == FrontProjectEnum::INOSUIT->value) {
            return FrontProjectEnum::INOSUIT;
        }
        return FrontProjectEnum::GREENTIM;
    }

    public static function getInosuitPeriodId(): int
    {
        $periodId = request()->headers->get(self::$inosuitPeriodKey);
        if (!$periodId) {
            throw new BadRequestHttpException('Dönem bilgisi bulunamadı.');
        }
        return (int)$periodId;
    }

    public static function formatDate($date): string
    {
        return Carbon::parse($date)->setTimezone('Europe/Istanbul')->format('d.m.Y H:i:s');
    }
}
