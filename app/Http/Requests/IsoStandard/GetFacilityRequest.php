<?php

namespace App\Http\Requests\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/iso-standards/facilities",
 *   description="Get facilities",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="year",
 *     in="query",
 *     description="Year",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get facilities"
 *   ),
 * )
 */
class GetFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'year' => 'required|integer',
        ];
    }
}
