<?php

namespace App\Http\Requests\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/iso-standards/years",
 *   description="Get years",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get years"
 *   ),
 * )
 */
class GetYearRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
