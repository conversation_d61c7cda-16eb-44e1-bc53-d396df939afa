<?php

namespace App\Http\Requests\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/iso-standards",
 *   description="Get iso standards",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get iso standards"
 *   ),
 * )
 */
class GetIsoStandardRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
