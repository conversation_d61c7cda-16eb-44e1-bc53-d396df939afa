<?php

namespace App\Http\Requests\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/iso-standards/access-token",
 *   description="Create access token from code",
 *   tags={"iso-standards"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create access token from code",
 *     @OA\JsonContent(
 *       required={"code"},
 *       @OA\Property(property="code", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create access token from code",
 *   ),
 * )
 */
class CreateAccessTokenRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'code' => 'required|string',
        ];
    }
}
