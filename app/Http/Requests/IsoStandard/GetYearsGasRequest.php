<?php

namespace App\Http\Requests\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/iso-standards/years-gases",
 *   description="Get years gases",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="years",
 *     in="query",
 *     description="Years",
 *     required=true,
 *     example="2020,2021",
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="facilityIds",
 *     in="query",
 *     description="Facility ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Parameter(
 *     name="formIds",
 *     in="query",
 *     description="Form ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get years gases"
 *   ),
 * )
 */
class GetYearsGasRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'years' => 'required|string|max:100',
            'facilityIds' => 'string|max:255',
            'formIds' => 'string|max:255',
        ];
    }
}
