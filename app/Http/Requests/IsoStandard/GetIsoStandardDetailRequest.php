<?php

namespace App\Http\Requests\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/iso-standards/detail",
 *   description="Get iso standard detail",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="query",
 *     description="Iso standard id",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get iso standard detail"
 *   ),
 * )
 */
class GetIsoStandardDetailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|exists:iso_standards,id',
        ];
    }
}
