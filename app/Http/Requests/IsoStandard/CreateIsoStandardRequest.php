<?php

namespace App\Http\Requests\IsoStandard;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/iso-standards",
 *   description="Create iso standard",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create iso standard",
 *     @OA\JsonContent(
 *       @OA\Property(property="id", type="integer"),
 *       @OA\Property(
 *         property="values",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"key","value"},
 *           properties={
 *             @OA\Property(property="key", type="string"),
 *             @OA\Property(property="value", type="object"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create iso standard"
 *   ),
 * )
 */
class CreateIsoStandardRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'integer|exists:iso_standards,id',
            'values' => 'array',
            'values.*.key' => 'required|string|max:255',
        ];
    }
}
