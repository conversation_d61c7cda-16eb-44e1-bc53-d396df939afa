<?php

namespace App\Http\Requests\Feedback;

use App\Enums\FeedbackSubjectEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/feedbacks",
 *   description="Create feedbacks",
 *   tags={"feedbacks"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create feedbacks",
 *     @OA\JsonContent(
 *       required={"subjectEnumId","message"},
 *       @OA\Property(property="subjectEnumId", type="integer"),
 *       @OA\Property(property="message", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create feedbacks"
 *   ),
 * )
 */
class CreateFeedbackRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'subjectEnumId' => ['required', 'integer', new Enum(FeedbackSubjectEnum::class)],
            'message' => 'required|string|max:1000',
        ];
    }
}
