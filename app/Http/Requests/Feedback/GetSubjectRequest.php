<?php

namespace App\Http\Requests\Feedback;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/feedbacks/subjects",
 *   description="Get feedback subjects",
 *   tags={"feedbacks"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get feedback subjects"
 *   ),
 * )
 */
class GetSubjectRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
