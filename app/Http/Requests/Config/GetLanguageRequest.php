<?php

namespace App\Http\Requests\Config;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/configs/languages",
 *   description="Get config languages",
 *   tags={"configs"},
 *   @OA\Response(
 *     response=200,
 *     description="Get config languages"
 *   ),
 * )
 */
class GetLanguageRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
