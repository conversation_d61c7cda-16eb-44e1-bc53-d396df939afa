<?php

namespace App\Http\Requests\Config;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/configs",
 *   description="Get configs",
 *   tags={"configs"},
 *   @OA\Response(
 *     response=200,
 *     description="Get configs"
 *   ),
 * )
 */
class GetConfigRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
