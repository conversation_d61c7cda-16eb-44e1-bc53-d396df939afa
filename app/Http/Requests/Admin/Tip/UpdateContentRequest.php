<?php

namespace App\Http\Requests\Admin\Tip;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Put(
 *   path="/api/admin/tips/contents/{id}",
 *   description="Update contents",
 *   tags={"admin/tips"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Content id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update contents",
 *     @OA\JsonContent(
 *       required={"frontSlug","tipId","titles","descriptions"},
 *       @OA\Property(property="frontSlug", type="string", example="/important-information/:param"),
 *       @OA\Property(property="tipId", type="integer"),
 *       @OA\Property(
 *         property="titles",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="descriptions",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update contents"
 *   ),
 * )
 */
class UpdateContentRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'frontSlug' => 'required|string|max:255',
            'tipId' => 'required|integer|exists:tips,id',
            'titles' => 'required|array|max:25',
            'titles.*.text' => 'required|string|max:3000',
            'titles.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'descriptions' => 'array|max:25',
            'descriptions.*.text' => 'required|string|max:3000',
            'descriptions.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
        ];
    }
}
