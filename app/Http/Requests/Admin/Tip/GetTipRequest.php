<?php

namespace App\Http\Requests\Admin\Tip;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/tips",
 *   description="Get tips",
 *   tags={"admin/tips"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get tips"
 *   ),
 * )
 */
class GetTipRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
