<?php

namespace App\Http\Requests\Admin\Export;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/exports/excel",
 *   summary="Excel Export (Legacy)",
 *   description="Excel export with privilege check (legacy version)",
 *   tags={"Admin Exports"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Export excel"
 *   ),
 * )
 */
class ExcelExportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
