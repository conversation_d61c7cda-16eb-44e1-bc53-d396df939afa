<?php

namespace App\Http\Requests\Admin\Export;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/exports/excel-esg",
 *   summary="Excel Export for ESG",
 *   description="Excel export for ESG with company ID check (fixed version with all data)",
 *   tags={"Admin Exports"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Export excel"
 *   ),
 * )
 */
class ExcelExportEsgRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
