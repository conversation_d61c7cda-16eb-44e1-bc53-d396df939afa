<?php

namespace App\Http\Requests\Admin\Uncertainty;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/admin/uncertainties",
 *   description="Create uncertainties",
 *   tags={"admin/uncertainties"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create uncertainties",
 *     @OA\JsonContent(
 *       required={"formId", "groupValueId", "uncertainty"},
 *       @OA\Property(property="formId", type="integer"),
 *       @OA\Property(property="groupValueId", type="integer"),
 *       @OA\Property(property="uncertainty", type="object"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create uncertainties"
 *   ),
 * )
 */
class CreateUncertaintyRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer|exists:forms,id',
            'groupValueId' => 'required|integer|exists:form_group_values,id',
            'uncertainty' => 'required',
            'uncertainty.*' => ['required', 'numeric', 'min:0'],
        ];
    }
}
