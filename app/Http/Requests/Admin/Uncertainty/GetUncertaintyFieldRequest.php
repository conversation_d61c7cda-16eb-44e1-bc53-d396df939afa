<?php

namespace App\Http\Requests\Admin\Uncertainty;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/uncertainties/fields",
 *   description="Get uncertainty fields",
 *   tags={"admin/uncertainties"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get uncertainty fields"
 *   ),
 * )
 */
class GetUncertaintyFieldRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer|exists:forms,id'
        ];
    }
}
