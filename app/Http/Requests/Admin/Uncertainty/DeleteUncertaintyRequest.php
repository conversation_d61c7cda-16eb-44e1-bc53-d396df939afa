<?php

namespace App\Http\Requests\Admin\Uncertainty;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/admin/uncertainties",
 *   description="Delete uncertainties",
 *   tags={"admin/uncertainties"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Delete uncertainties",
 *     @OA\JsonContent(
 *       required={"id"},
 *       @OA\Property(property="id", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete uncertainties"
 *   ),
 * )
 */
class DeleteUncertaintyRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer',
        ];
    }
}
