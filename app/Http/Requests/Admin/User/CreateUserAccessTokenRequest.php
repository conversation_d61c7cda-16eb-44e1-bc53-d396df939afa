<?php

namespace App\Http\Requests\Admin\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/admin/users/access-token",
 *   description="Create user access token",
 *   tags={"admin/users"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create user access token",
 *     @OA\JsonContent(
 *       required={"email"},
 *       @OA\Property(property="email", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create user access token"
 *   ),
 * )
 */
class CreateUserAccessTokenRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email|exists:users,email',
        ];
    }
}
