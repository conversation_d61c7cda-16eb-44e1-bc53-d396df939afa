<?php

namespace App\Http\Requests\Admin\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/users",
 *   description="Get users",
 *   tags={"admin/users"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get users"
 *   ),
 * )
 */
class GetUserRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
