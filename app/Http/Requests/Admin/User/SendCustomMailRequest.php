<?php

namespace App\Http\Requests\Admin\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/admin/users/send-custom-mail",
 *   description="Send custom mail",
 *   tags={"admin/users"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Send custom mail",
 *     @OA\MediaType(
 *       mediaType="multipart/form-data",
 *       @OA\Schema(
 *         required={"subject", "text", "excel"},
 *         @OA\Property(
 *           property="subject",
 *           type="string",
 *           description="Email subject"
 *         ),
 *         @OA\Property(
 *           property="text",
 *           type="string",
 *           description="Html text"
 *         ),
 *         @OA\Property(
 *           property="excel",
 *           type="string",
 *           format="binary",
 *           description="Excel file containing email addresses"
 *         )
 *       )
 *     )
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Send custom mail"
 *   ),
 * )
 */
class SendCustomMailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'subject' => 'required|string|min:1',
            'text' => 'required|string|min:1',
            'excel' => "required|file|mimes:xlsx|max:10240", // 10 MB
        ];
    }
}
