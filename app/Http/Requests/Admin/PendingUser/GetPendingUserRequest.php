<?php

namespace App\Http\Requests\Admin\PendingUser;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/pending-users",
 *   description="Get pending users",
 *   tags={"admin/pending-users"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get pending users"
 *   ),
 * )
 */
class GetPendingUserRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
