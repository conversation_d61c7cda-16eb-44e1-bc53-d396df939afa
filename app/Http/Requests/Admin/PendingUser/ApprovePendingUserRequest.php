<?php

namespace App\Http\Requests\Admin\PendingUser;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/admin/pending-users/approve",
 *   description="Approve pending users",
 *   tags={"admin/pending-users"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Approve pending users",
 *     @OA\JsonContent(
 *       required={"companyId"},
 *       @OA\Property(property="companyId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Approve pending users"
 *   ),
 * )
 */
class ApprovePendingUserRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyId' => 'required|integer|exists:companies,id',
        ];
    }
}
