<?php

namespace App\Http\Requests\Admin\PendingUser;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/admin/pending-users/deny",
 *   description="Deny pending users",
 *   tags={"admin/pending-users"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Deny pending users",
 *     @OA\JsonContent(
 *       required={"companyId"},
 *       @OA\Property(property="companyId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Deny pending users"
 *   ),
 * )
 */
class DenyPendingUserRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyId' => 'required|integer|exists:companies,id',
        ];
    }
}
