<?php

namespace App\Http\Requests\Admin\Config;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/admin/configs",
 *   description="Create or update configs",
 *   tags={"admin/configs"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create or update configs",
 *     @OA\JsonContent(
 *       required={"key","value"},
 *       @OA\Property(property="key", type="string"),
 *       @OA\Property(property="value", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create or update configs"
 *   ),
 * )
 */
class CreateOrUpdateConfigRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'key' => 'required|string|max:255',
            'value' => 'required|string|max:255',
        ];
    }
}
