<?php

namespace App\Http\Requests\Admin\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/admin/iso-standards/classifications",
 *   description="Delete classifications",
 *   tags={"admin/iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="query",
 *     description="classification id",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete classifications"
 *   ),
 * )
 */
class DeleteClassificationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|exists:iso_standard_classifications,id',
        ];
    }
}
