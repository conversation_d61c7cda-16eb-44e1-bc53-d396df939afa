<?php

namespace App\Http\Requests\Admin\IsoStandard;

use App\Enums\ClassificationGassesEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/admin/iso-standards/classifications",
 *   description="Create classifications",
 *   tags={"admin/iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create iso standard",
 *     @OA\JsonContent(
 *       required={"groupValueIds", "gasses"},
 *       @OA\Property(property="groupValueIds", type="array", @OA\Items(type="integer")),
 *       @OA\Property(property="gasses", type="array", @OA\Items(type="string")),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create classifications"
 *   ),
 * )
 */
class CreateClassificationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'groupValueIds' => 'required|array|min:1',
            'groupValueIds.*' => 'required|integer|exists:form_group_values,id',
            'gasses' => 'required|array|min:1',
            'gasses.*' => ['required', 'string', new Enum(ClassificationGassesEnum::class)],
        ];
    }
}
