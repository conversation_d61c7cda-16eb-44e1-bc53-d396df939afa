<?php

namespace App\Http\Requests\Admin\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/iso-standards/classifications",
 *   description="Get classifications",
 *   tags={"admin/iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get classifications"
 *   ),
 * )
 */
class GetClassificationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
