<?php

namespace App\Http\Requests\Admin\IsoStandard;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/iso-standards/group-values",
 *   description="Get group values",
 *   tags={"admin/iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="search",
 *     in="query",
 *     description="search",
 *     required=true,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get group values"
 *   ),
 * )
 */
class GetGroupValueRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'required|string|max:255'
        ];
    }
}
