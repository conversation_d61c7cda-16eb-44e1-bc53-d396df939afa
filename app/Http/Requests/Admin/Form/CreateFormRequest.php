<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/admin/forms",
 *   description="Create forms",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create forms",
 *     @OA\JsonContent(
 *       required={"slug","isLocked","categoryId","names","inputs"},
 *       @OA\Property(property="slug", type="string", example="form-slug"),
 *       @OA\Property(property="isLocked", type="boolean", example="false"),
 *       @OA\Property(property="categoryId", type="integer"),
 *       @OA\Property(
 *         property="names",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="infos",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="inputs",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"names","groups"},
 *           properties={
 *             @OA\Property(property="relatedInputIndexes", type="array", @OA\Items(type="integer")),
 *             @OA\Property(
 *               property="names",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"text","language"},
 *                 properties={
 *                   @OA\Property(property="text", type="string"),
 *                   @OA\Property(property="language", type="string"),
 *                 }
 *               ),
 *             ),
 *             @OA\Property(
 *               property="infos",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"text","language"},
 *                 properties={
 *                   @OA\Property(property="text", type="string"),
 *                   @OA\Property(property="language", type="string"),
 *                 }
 *               ),
 *             ),
 *             @OA\Property(
 *               property="groups",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"groupId"},
 *                 properties={
 *                   @OA\Property(property="groupId", type="integer"),
 *                   @OA\Property(property="relatedGroupValueId", type="integer"),
 *                 }
 *               ),
 *             ),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create forms"
 *   ),
 * )
 */
class CreateFormRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'slug' => 'required|string|max:50|unique:forms,slug',
            'isLocked' => 'required|boolean',
            'categoryId' => 'required|integer|exists:form_categories,id',
            'names' => 'required|array|max:25',
            'names.*.text' => 'required|string|max:500',
            'names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'infos' => 'array|max:25',
            'infos.*.text' => 'required|string|max:500',
            'infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],

            'inputs' => 'required|array|max:50',
            'inputs.*.relatedInputIndexes' => 'array|max:50',
            'inputs.*.relatedInputIndexes.*' => 'integer|min:0|max:50',
            'inputs.*.names' => 'required|array|max:25',
            'inputs.*.names.*.text' => 'required|string|max:500',
            'inputs.*.names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'inputs.*.infos' => 'array|max:25',
            'inputs.*.infos.*.text' => 'required|string|max:500',
            'inputs.*.infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'inputs.*.groups' => 'array|max:500',
            'inputs.*.groups.*.groupId' => 'required|integer|exists:form_groups,id',
            'inputs.*.groups.*.relatedGroupValueId' => 'integer|exists:form_group_values,id',
        ];
    }
}
