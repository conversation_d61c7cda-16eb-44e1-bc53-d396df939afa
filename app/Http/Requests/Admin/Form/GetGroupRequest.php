<?php

namespace App\Http\Requests\Admin\Form;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/forms/groups",
 *   description="Get form groups",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="query",
 *     description="group id",
 *     required=false,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Parameter(
 *     name="name",
 *     in="query",
 *     description="group name",
 *     required=false,
 *     @OA\Schema(
 *       type="string"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get form groups"
 *   ),
 * )
 */
class GetGroupRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'exists:form_groups,id',
            'name' => 'string|max:255',
        ];
    }
}
