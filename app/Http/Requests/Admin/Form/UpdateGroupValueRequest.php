<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\FormGroupValue;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Put(
 *   path="/api/admin/forms/groups/values/{id}",
 *   description="Update group values",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Group value id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update group values",
 *     @OA\JsonContent(
 *       required={"names","infos"},
 *       @OA\Property(
 *         property="names",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="infos",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update group values"
 *   ),
 * )
 */
class UpdateGroupValueRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        FormGroupValue::query()->findOrFail($this->route('id'));
        return [
            'names' => 'required|array|max:25',
            'names.*.text' => 'required|string|max:500',
            'names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'infos' => 'array|max:25',
            'infos.*.text' => 'required|string|max:500',
            'infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
        ];
    }
}
