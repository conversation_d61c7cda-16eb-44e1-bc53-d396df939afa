<?php

namespace App\Http\Requests\Admin\Form;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/forms/inputs",
 *   description="Get form inputs",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="query",
 *     description="input id",
 *     required=false,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get form inputs"
 *   ),
 * )
 */
class GetInputRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'exists:form_inputs,id'
        ];
    }
}
