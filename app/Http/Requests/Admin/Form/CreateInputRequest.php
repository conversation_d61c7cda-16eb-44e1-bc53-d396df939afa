<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/admin/forms/inputs",
 *   description="Create forms",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create forms",
 *     @OA\JsonContent(
 *       required={"formId","relatedInputId","names","groups"},
 *       @OA\Property(property="formId", type="integer"),
 *       @OA\Property(property="relatedInputId", type="integer"),
 *       @OA\Property(
 *         property="names",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="infos",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="groups",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"groupId"},
 *           properties={
 *             @OA\Property(property="groupId", type="integer"),
 *             @OA\Property(property="relatedGroupValueId", type="integer"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create forms"
 *   ),
 * )
 */
class CreateInputRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer|exists:forms,id',
            'relatedInputId' => 'integer|exists:form_inputs,id,form_id,' . $this->formId,
            'names' => 'required|array|max:25',
            'names.*.text' => 'required|string|max:500',
            'names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'infos' => 'array|max:25',
            'infos.*.text' => 'required|string|max:500',
            'infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'groups' => 'required|array|max:500',
            'groups.*.groupId' => 'required|integer|exists:form_groups,id',
            'groups.*.relatedGroupValueId' => 'integer|exists:form_group_values,id',
        ];
    }
}
