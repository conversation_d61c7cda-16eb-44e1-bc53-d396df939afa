<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/admin/forms/groups",
 *   description="Create groups",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create groups",
 *     @OA\JsonContent(
 *       required={"name","values"},
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(
 *         property="values",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"names"},
 *           properties={
 *             @OA\Property(
 *               property="names",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"text","language"},
 *                 properties={
 *                   @OA\Property(property="text", type="string"),
 *                   @OA\Property(property="language", type="string"),
 *                 }
 *               ),
 *             ),
 *             @OA\Property(
 *               property="infos",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"text","language"},
 *                 properties={
 *                   @OA\Property(property="text", type="string"),
 *                   @OA\Property(property="language", type="string"),
 *                 }
 *               ),
 *             ),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create groups"
 *   ),
 * )
 */
class CreateGroupRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:form_groups,name',
            'values' => 'required|array|max:250',
            'values.*.names' => 'required|array|max:25',
            'values.*.names.*.text' => 'required|string|max:500',
            'values.*.names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'values.*.infos' => 'array|max:25',
            'values.*.infos.*.text' => 'required|string|max:500',
            'values.*.infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
        ];
    }
}
