<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Put(
 *   path="/api/admin/forms/{id}",
 *   description="Update forms",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Form id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update forms",
 *     @OA\JsonContent(
 *       required={"slug","isLocked","categoryId"},
 *       @OA\Property(property="slug", type="string", example="form-slug"),
 *       @OA\Property(property="isLocked", type="boolean", example="false"),
 *       @OA\Property(property="categoryId", type="integer"),
 *       @OA\Property(
 *         property="names",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="infos",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="descriptions",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update forms"
 *   ),
 * )
 */
class UpdateFormRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'slug' => 'required|string|max:50|unique:forms,slug,' . $this->route('id'),
            'isLocked' => 'required|boolean',
            'categoryId' => 'required|integer|exists:form_categories,id',
            'names' => 'array|max:25',
            'names.*.text' => 'required|string|max:500',
            'names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'infos' => 'array|max:25',
            'infos.*.text' => 'required|string|max:500',
            'infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'descriptions' => 'array|max:25',
            'descriptions.*.text' => 'required|string|max:500',
            'descriptions.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
        ];
    }
}
