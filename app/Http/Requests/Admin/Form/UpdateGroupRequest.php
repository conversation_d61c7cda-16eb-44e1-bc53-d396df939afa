<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Put(
 *   path="/api/admin/forms/groups/{id}",
 *   description="Update groups",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Group id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update groups",
 *     @OA\JsonContent(
 *       required={"name","values"},
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(
 *         property="values",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"names"},
 *           properties={
 *             @OA\Property(property="id", type="integer"),
 *             @OA\Property(
 *               property="names",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"text","language"},
 *                 properties={
 *                   @OA\Property(property="text", type="string"),
 *                   @OA\Property(property="language", type="string"),
 *                 }
 *               ),
 *             ),
 *             @OA\Property(
 *               property="infos",
 *               type="array",
 *               @OA\Items(
 *                 type="object",
 *                 required={"text","language"},
 *                 properties={
 *                   @OA\Property(property="text", type="string"),
 *                   @OA\Property(property="language", type="string"),
 *                 }
 *               ),
 *             ),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update groups"
 *   ),
 * )
 */
class UpdateGroupRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:form_groups,name,' . $this->route('id'),
            'values' => 'required|array|max:250',
            'values.*.id' => 'integer|exists:form_group_values,id',
            'values.*.names' => 'required|array|max:25',
            'values.*.names.*.text' => 'required|string|max:500',
            'values.*.names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'values.*.infos' => 'array|max:25',
            'values.*.infos.*.text' => 'required|string|max:500',
            'values.*.infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
        ];
    }
}
