<?php

namespace App\Http\Requests\Admin\Form;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/admin/forms/results/{id}",
 *   description="Update form results",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Form id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update form results",
 *     @OA\JsonContent(
 *       required={"carbonDioxide","nitrousOxide","methane","groupValueIds"},
 *       @OA\Property(property="carbonDioxide", type="number", format="double"),
 *       @OA\Property(property="nitrousOxide", type="number", format="double"),
 *       @OA\Property(property="methane", type="number", format="double"),
 *       @OA\Property(
 *         property="values",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"inputId","groupValueId"},
 *           properties={
 *             @OA\Property(property="inputId", type="integer"),
 *             @OA\Property(property="groupValueId", type="integer"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update form results"
 *   ),
 * )
 */
class UpdateFormResultRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'carbonDioxide' => 'required|numeric|min:0',
            'nitrousOxide' => 'required|numeric|min:0',
            'methane' => 'required|numeric|min:0',
            'values' => 'required|array|max:50',
            'values.*.inputId' => 'required|integer|exists:form_inputs,id,form_id,' . $this->route('id'),
            'values.*.groupValueId' => 'required|integer|exists:form_group_values,id',
        ];
    }
}
