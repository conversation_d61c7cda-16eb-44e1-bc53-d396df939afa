<?php

namespace App\Http\Requests\Admin\Form;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/admin/forms/groups/{id}",
 *   description="Delete form groups",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Group id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete form groups"
 *   ),
 * )
 */
class DeleteGroupRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
