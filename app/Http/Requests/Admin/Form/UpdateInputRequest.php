<?php

namespace App\Http\Requests\Admin\Form;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\FormInput;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Put(
 *   path="/api/admin/forms/inputs/{id}",
 *   description="Update form inputs",
 *   tags={"admin/forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Form input id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update form inputs",
 *     @OA\JsonContent(
 *       required={"names","infos","amountNames"},
 *       @OA\Property(
 *         property="names",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="infos",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="amountNames",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"text","language"},
 *           properties={
 *             @OA\Property(property="text", type="string"),
 *             @OA\Property(property="language", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update form inputs"
 *   ),
 * )
 */
class UpdateInputRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        FormInput::query()->findOrFail($this->route('id'));
        return [
            'names' => 'required|array|max:25',
            'names.*.text' => 'required|string|max:500',
            'names.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'infos' => 'array|max:25',
            'infos.*.text' => 'required|string|max:500',
            'infos.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
            'amountNames' => 'array|max:25',
            'amountNames.*.text' => 'required|string|max:500',
            'amountNames.*.language' => ['required', 'string', new Enum(LanguageEnum::class)],
        ];
    }
}
