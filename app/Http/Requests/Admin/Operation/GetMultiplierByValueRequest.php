<?php

namespace App\Http\Requests\Admin\Operation;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/operations/multiplier-by-values",
 *   description="Get multiplier by values",
 *   tags={"admin/operations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     description="form id",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="groupValueIds",
 *     in="query",
 *     description="group value ids",
 *     required=true,
 *     example="1,2,3",
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get multiplier by values"
 *   ),
 * )
 */
class GetMultiplierByValueRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer|exists:forms,id',
            'groupValueIds' => 'required|string|min:1|max:100',
        ];
    }
}
