<?php

namespace App\Http\Requests\Admin\Operation;

use App\Enums\LanguageEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

/**
 * @OA\Post(
 *   path="/api/admin/operations/multipliers",
 *   description="Create multipliers",
 *   tags={"admin/operations"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create multipliers",
 *     @OA\JsonContent(
 *       required={"formId","isUnit","groupValueIds","multiplier"},
 *       @OA\Property(property="formId", type="integer"),
 *       @OA\Property(property="isUnit", type="boolean"),
 *       @OA\Property(property="groupValueIds", type="array", @OA\Items(type="integer")),
 *       @OA\Property(property="multiplier", type="object"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create multipliers"
 *   ),
 * )
 */
class CreateMultiplierRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer|exists:forms,id',
            'isUnit' => 'required|boolean',
            'groupValueIds' => 'required|array|min:1',
            'groupValueIds.*' => 'required|integer|exists:form_group_values,id',
            'multiplier' => 'required',
            'multiplier.unitValue' => [Rule::requiredIf(!!request()->isUnit)],
            'multiplier.*' => ['numeric', 'min:0'],
        ];
    }
}
