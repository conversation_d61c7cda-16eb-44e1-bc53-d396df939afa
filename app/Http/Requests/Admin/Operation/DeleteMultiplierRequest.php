<?php

namespace App\Http\Requests\Admin\Operation;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/admin/operations/multipliers/{id}",
 *   description="Delete multipliers",
 *   tags={"admin/operations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Multiplier id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete multipliers"
 *   ),
 * )
 */
class DeleteMultiplierRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
