<?php

namespace App\Http\Requests\Admin\Feedback;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/admin/feedbacks",
 *   description="Get feedbacks",
 *   tags={"admin/feedbacks"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get feedbacks"
 *   ),
 * )
 */
class GetFeedbackRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
