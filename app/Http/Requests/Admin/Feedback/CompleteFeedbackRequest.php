<?php

namespace App\Http\Requests\Admin\Feedback;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/admin/feedbacks/complete/{id}",
 *   description="Complete feedbacks",
 *   tags={"admin/feedbacks"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Feedback id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Complete feedbacks",
 *     @OA\JsonContent(
 *       required={"isComplete"},
 *       @OA\Property(property="isComplete", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Complete feedbacks"
 *   ),
 * )
 */
class CompleteFeedbackRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'isComplete' => 'required|boolean',
        ];
    }
}
