<?php

namespace App\Http\Requests\File;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/files/url",
 *   description="Create file url",
 *   tags={"files"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create file url",
 *     @OA\JsonContent(
 *       required={"url"},
 *       @OA\Property(property="reportId", type="integer"),
 *       @OA\Property(property="url", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create file url",
 *   ),
 * )
 */
class CreateFileUrlRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'reportId' => 'integer|nullable|exists:reports,id,company_id,' . auth()->user()->company_id,
            'url' => 'required|url|max:255',
        ];
    }
}
