<?php

namespace App\Http\Requests\File;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/files",
 *   description="Delete file",
 *   tags={"files"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Delete file",
 *     @OA\JsonContent(
 *       required={"fileId"},
 *       @OA\Property(property="fileId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete file"
 *   ),
 * )
 */
class DeleteFileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'fileId' => 'required|integer',
        ];
    }
}
