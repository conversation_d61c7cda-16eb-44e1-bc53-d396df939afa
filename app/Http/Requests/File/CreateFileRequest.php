<?php

namespace App\Http\Requests\File;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/files",
 *   description="Create file",
 *   tags={"files"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create file",
 *     @OA\MediaType(
 *       mediaType="multipart/form-data",
 *       @OA\Schema(
 *         required={"path","file"},
 *         @OA\Property(property="path", type="string"),
 *         @OA\Property(property="name", type="string"),
 *         @OA\Property(property="file", type="string", format="binary"),
 *         @OA\Property(property="reportId", type="integer"),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create file"
 *   ),
 * )
 */
class CreateFileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'path' => 'required|string|min:1|max:20',
            'name' => 'nullable|string|max:255',
            'file' => 'required|file|max:10240', // 10 MB
            'reportId' => 'integer|nullable|exists:reports,id,company_id,' . auth()->user()->company_id,
        ];
    }
}
