<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/companies/sectors",
 *   description="Company sectors",
 *   tags={"companies"},
 *   @OA\Parameter(
 *     name="visibleInsouitApplication",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="boolean"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Company sectors"
 *   ),
 * )
 */
class GetSectorRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        if ($this->has('visibleInsouitApplication')) {
            $this->merge([
                'visibleInsouitApplication' => $this->visibleInsouitApplication === 'true',
            ]);
        }
        return [
            'visibleInsouitApplication' => 'boolean',
        ];
    }
}
