<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/companies/personnel-numbers",
 *   description="Company personnel numbers",
 *   tags={"companies"},
 *   @OA\Response(
 *     response=200,
 *     description="Company personnel numbers"
 *   ),
 * )
 */
class GetPersonnelNumberRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
