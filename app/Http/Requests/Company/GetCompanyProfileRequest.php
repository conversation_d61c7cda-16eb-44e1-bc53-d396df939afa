<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/companies/profile",
 *   description="Company profile",
 *   tags={"companies"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Company profile"
 *   ),
 * )
 */
class GetCompanyProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
