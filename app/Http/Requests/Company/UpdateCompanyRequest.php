<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/companies",
 *   description="Update company",
 *   tags={"companies"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update company",
 *     @OA\JsonContent(
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="taxNumber", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="linkedinUrl", type="string"),
 *       @OA\Property(property="twitterUrl", type="string"),
 *       @OA\Property(property="website", type="string"),
 *       @OA\Property(property="logo", type="string"),
 *       @OA\Property(property="address", type="string"),
 *       @OA\Property(
 *         property="naceCodes",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"code"},
 *           properties={
 *             @OA\Property(property="code", type="string"),
 *             @OA\Property(property="description", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update company"
 *   ),
 * )
 */
class UpdateCompanyRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'string|min:1|max:255',
            'description' => 'nullable|string|min:1|max:500',
            'taxNumber' => 'nullable|string|min:1|max:50',
            'email' => 'nullable|email|min:1|max:255',
            'phone' => 'nullable|string|min:1|max:30',
            'linkedinUrl' => 'nullable|min:1|max:255',
            'twitterUrl' => 'nullable|min:1|max:255',
            'website' => 'nullable|min:1|max:255',
            'logo' => 'nullable|url|min:1|max:255',
            'address' => 'nullable|string|min:1|max:300',

            'naceCodes' => 'nullable|array',
            'naceCodes.*.code' => 'required_with:naceCodes|string|min:1|max:20',
            'naceCodes.*.description' => 'nullable|string|min:1|max:1000',
        ];
    }
}
