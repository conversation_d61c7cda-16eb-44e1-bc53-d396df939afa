<?php

namespace App\Http\Requests\Artificial;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/artificial/questions",
 *   description="Create artificial questions",
 *   tags={"artificial"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create artificial questions",
 *     @OA\JsonContent(
 *       required={"question", "answer"},
 *       @OA\Property(property="question", type="string"),
 *       @OA\Property(property="answer", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create artificial questions",
 *   ),
 * )
 */
class CreateQuestionRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'question' => 'required|string|min:1|max:65535',
            'answer' => 'required|string|min:1|max:65535',
        ];
    }
}
