<?php

namespace App\Http\Requests\Artificial;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/artificial/questions",
 *   description="Update artificial questions",
 *   tags={"artificial"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update artificial questions",
 *     @OA\JsonContent(
 *       required={"id"},
 *       @OA\Property(property="id", type="integer"),
 *       @OA\Property(property="isLike", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update artificial questions",
 *   ),
 * )
 */
class UpdateQuestionRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer',
            'isLike' => 'nullable|boolean',
        ];
    }
}
