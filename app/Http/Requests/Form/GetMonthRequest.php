<?php

namespace App\Http\Requests\Form;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/forms/months",
 *   description="Get months",
 *   tags={"forms"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get months"
 *   ),
 * )
 */
class GetMonthRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
