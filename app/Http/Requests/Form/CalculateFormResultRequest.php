<?php

namespace App\Http\Requests\Form;

use App\Enums\UncertaintyElectricityClassTypeEnum;
use App\Enums\UncertaintyElectricityMeterTypeEnum;
use App\Enums\UncertaintyElectricityTemperatureTypeEnum;
use App\Enums\UncertaintyPedigreeTabEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rules\Enum;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/forms/calculate/results",
 *   description="Form calculate results",
 *   tags={"forms"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Form calculate results",
 *     @OA\JsonContent(
 *       required={"formId","amount","values"},
 *       @OA\Property(property="formId",type="integer"),
 *       @OA\Property(property="amount",type="double"),
 *       @OA\Property(property="errorMargin",type="double"),
 *       @OA\Property(property="isCalibration",type="boolean"),
 *       @OA\Property(property="uncertaintyValue",type="double"),
 *       @OA\Property(
 *         property="pedigreeTabs",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"tabType", "value"},
 *           properties={
 *             @OA\Property(property="tabType", type="integer"),
 *             @OA\Property(property="value", type="integer"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="electricity",
 *         type="object",
 *         required={"meterType","classType","temperatureType","transferCurrent","maxCurrent","expectedCurrent"},
 *         properties={
 *           @OA\Property(property="meterType", type="integer"),
 *           @OA\Property(property="classType", type="integer"),
 *           @OA\Property(property="temperatureType", type="integer"),
 *           @OA\Property(property="minCurrent", type="double"),
 *           @OA\Property(property="transferCurrent", type="double"),
 *           @OA\Property(property="maxCurrent", type="double"),
 *           @OA\Property(property="expectedCurrent", type="double"),
 *         }
 *       ),
 *       @OA\Property(
 *         property="values",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"inputId"},
 *           properties={
 *             @OA\Property(property="inputId", type="integer"),
 *             @OA\Property(property="groupValueId", type="integer"),
 *             @OA\Property(property="numericValue", type="double"),
 *             @OA\Property(property="textValue", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Form calculate results"
 *   ),
 * )
 */
class CalculateFormResultRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        foreach (request()->values as $value) {
            if (!isset($value['groupValueId']) && !isset($value['numericValue']) && !isset($value['textValue'])) {
                throw new BadRequestHttpException('either groupValueId or numericValue or textValue is required');
            }
        }
        return [
            'formId' => 'required|integer|exists:forms,id',
            'amount' => 'required|numeric|min:0.0001',
            'errorMargin' => 'nullable|numeric|min:0.0001',
            'isCalibration' => 'nullable|boolean',
            'uncertaintyValue' => 'nullable|numeric|min:0.0001',

            'pedigreeTabs' => 'nullable|array|size:5',
            'pedigreeTabs.*.tabType' => ['required_with:pedigreeTabs', 'integer', 'distinct', new Enum(UncertaintyPedigreeTabEnum::class)],
            'pedigreeTabs.*.value' => 'required_with:pedigreeTabs|integer|min:1|max:5',

            'electricity' => 'nullable|array',
            'electricity.meterType' => ['required_with:electricity', 'integer', new Enum(UncertaintyElectricityMeterTypeEnum::class)],
            'electricity.classType' => ['required_with:electricity', 'integer', new Enum(UncertaintyElectricityClassTypeEnum::class)],
            'electricity.temperatureType' => ['required_with:electricity', 'integer', new Enum(UncertaintyElectricityTemperatureTypeEnum::class)],
            'electricity.minCurrent' => 'nullable|numeric|min:0.0001',
            'electricity.transferCurrent' => 'required_with:electricity|numeric|min:0.0001',
            'electricity.maxCurrent' => 'required_with:electricity|numeric|min:0.0001',
            'electricity.expectedCurrent' => 'required_with:electricity|numeric|min:0.0001',

            'values' => 'required|array|min:1|max:50',
            'values.*.inputId' => 'required|integer|exists:form_inputs,id,form_id,' . $this->request->get('formId'),
            'values.*.groupValueId' => 'integer|exists:form_group_values,id',
            'values.*.numericValue' => 'numeric|min:0.0001',
            'values.*.textValue' => 'string|min:1|max:255',
        ];
    }
}
