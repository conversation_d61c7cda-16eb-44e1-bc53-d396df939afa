<?php

namespace App\Http\Requests\Form;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/forms/detail/{slug}",
 *   description="Get forms by slug",
 *   tags={"forms"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="slug",
 *     in="path",
 *     description="Form slug",
 *     required=true,
 *     @OA\Schema(
 *       type="string"
 *     ),
 *   ),
 *   @OA\Parameter(
 *     name="inputIds",
 *     in="query",
 *     description="Input ids",
 *     required=false,
 *     example="1,2,3",
 *     @OA\Schema(
 *       type="string"
 *     ),
 *   ),
 *   @OA\Parameter(
 *     name="groupValueId",
 *     in="query",
 *     description="Group value id",
 *     required=false,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get forms by slug"
 *   ),
 * )
 */
class GetFormBySlugRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'inputIds' => 'string|max:50',
            'groupValueId' => 'integer|exists:form_group_values,id',
        ];
    }
}
