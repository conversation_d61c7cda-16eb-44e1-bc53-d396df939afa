<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/eco-mentor/forms/detail",
 *   description="Get form detail",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     description="Form id",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="inputIds",
 *     in="query",
 *     description="1,2,3",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="groupValueId",
 *     in="query",
 *     description="Group value id",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get form detail"
 *   ),
 * )
 */
class GetFormDetailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer',
            'inputIds' => 'string|max:50',
            'groupValueId' => 'integer|exists:form_group_values,id',
        ];
    }
}
