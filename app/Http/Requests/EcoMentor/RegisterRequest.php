<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/eco-mentor/register",
 *   description="Register user",
 *   tags={"eco-mentor"},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Register user",
 *     @OA\JsonContent(
 *       required={"fullName","email","companyName","kvkkApproved","clarificationText","privacyPolicy"},
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="companyName", type="string"),
 *       @OA\Property(property="companySectorId", type="integer"),
 *       @OA\Property(property="companyTaxNumber", type="string"),
 *       @OA\Property(property="kvkkApproved", type="boolean"),
 *       @OA\Property(property="clarificationText", type="boolean"),
 *       @OA\Property(property="privacyPolicy", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Register user"
 *   ),
 * )
 */
class RegisterRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'fullName' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'companyName' => 'required|string|max:255',
            'companySectorId' => 'required|integer|exists:company_sectors,id',
            'companyTaxNumber' => 'required|string|min:1|max:50',
            'kvkkApproved' => 'required|boolean|in:1',
            'clarificationText' => 'required|boolean|in:1',
            'privacyPolicy' => 'required|boolean|in:1',
        ];
    }
}
