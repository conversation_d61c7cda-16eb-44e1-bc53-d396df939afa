<?php

namespace App\Http\Requests\EcoMentor;

use App\Enums\MonthEnum;
use App\Enums\UncertaintyPedigreeTabEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\FileManager;
use App\Models\Form;
use App\Utils\GeneralUtil;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/eco-mentor/reports",
 *   description="Create report",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create report",
 *     @OA\JsonContent(
 *       required={"formId","facilityId","year","monthIds","amount"},
 *       @OA\Property(property="formId", type="integer"),
 *       @OA\Property(property="facilityId", type="integer"),
 *       @OA\Property(property="resultId", type="integer"),
 *       @OA\Property(property="year", type="integer"),
 *       @OA\Property(property="monthIds", type="array", @OA\Items(type="integer")),
 *       @OA\Property(property="amount", type="double"),
 *       @OA\Property(property="fileIds", type="array", @OA\Items(type="integer")),
 *       @OA\Property(
 *         property="payload",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"inputId"},
 *           properties={
 *             @OA\Property(property="inputId", type="integer"),
 *             @OA\Property(property="groupValueId", type="integer"),
 *             @OA\Property(property="numericValue", type="double"),
 *             @OA\Property(property="textValue", type="string"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(property="description", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create report"
 *   ),
 * )
 */
class CreateReportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $years = GeneralUtil::getYearsAsArray();
        $formId = $this->request->get('formId');
        $commonFormIds = Form::with('commons')->findOrFail($formId)->commons->map(fn($c) => $c->common_form_id)->unique()->toArray();
        $formIds = [$formId, ...$commonFormIds];

        if (!request()->resultId && !request()->payload) {
            throw new BadRequestHttpException('either resultId or payload is required');
        } else if (request()->resultId && request()->payload) {
            throw new BadRequestHttpException('cannot both resultId and payload');
        }

        if (request()->payload) {
            foreach (request()->payload as $p) {
                if (!isset($p['groupValueId']) && !isset($p['numericValue']) && !isset($p['textValue'])) {
                    throw new BadRequestHttpException('either groupValueId or numericValue or textValue is required');
                }
            }
        }

        if (request()->fileIds) {
            $fileManagerCount = FileManager::query()
                ->whereIn('id', array_unique(request()->fileIds))
                ->where('user_id', auth()->id())
                ->whereNull('report_id')
                ->count();
            if ($fileManagerCount !== count(request()->fileIds)) {
                throw new BadRequestHttpException('fileIds is invalid');
            }
        }

        return [
            'formId' => 'required|integer|exists:forms,id',
            'facilityId' => 'required|integer|exists:facilities,id,company_id,' . auth()->user()->company_id,
            'resultId' => [Rule::requiredIf(!request()->payload), 'integer', Rule::exists('form_results', 'id')->whereIn('form_id', $formIds)],
            'year' => ['required', 'integer', Rule::in($years)],
            'monthIds' => 'required|min:1|max:12',
            'monthIds.*' => ['required', 'integer', new Enum(MonthEnum::class)],
            'amount' => 'required|numeric|min:0.0001',
            'fileIds' => 'array|min:1|max:20',
            'fileIds.*' => 'required|integer',
            'payload' => [Rule::requiredIf(!request()->resultId), 'array', 'min:1', 'max:50'],
            'payload.*.inputId' => 'required|integer|exists:form_inputs,id,form_id,' . $this->request->get('formId'),
            'payload.*.groupValueId' => 'integer|exists:form_group_values,id',
            'payload.*.numericValue' => 'numeric|min:0.0001',
            'payload.*.textValue' => 'string|min:1|max:255',
            'description' => 'nullable|string|min:1',
        ];
    }
}
