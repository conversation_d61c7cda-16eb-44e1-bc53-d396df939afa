<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/eco-mentor/files/url",
 *   description="Create file url",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create file url",
 *     @OA\JsonContent(
 *       required={"url"},
 *       @OA\Property(property="url", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create file url",
 *   ),
 * )
 */
class CreateFileUrlRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'url' => 'required|url|max:255',
        ];
    }
}
