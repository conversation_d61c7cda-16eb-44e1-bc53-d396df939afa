<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/eco-mentor/forms/calculate",
 *   description="Form calculate",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Form calculate",
 *     @OA\JsonContent(
 *       required={"formId","amount","values"},
 *       @OA\Property(property="formId",type="integer"),
 *       @OA\Property(property="amount",type="double"),
 *       @OA\Property(
 *         property="values",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"inputId"},
 *           properties={
 *             @OA\Property(property="inputId", type="integer"),
 *             @OA\Property(property="groupValueId", type="integer"),
 *             @OA\Property(property="numericValue", type="double"),
 *             @OA\Property(property="textValue", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Form calculate"
 *   ),
 * )
 */
class CalculateFormRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        foreach (request()->values as $value) {
            if (!isset($value['groupValueId']) && !isset($value['numericValue']) && !isset($value['textValue'])) {
                throw new BadRequestHttpException('either groupValueId or numericValue or textValue is required');
            }
        }
        return [
            'formId' => 'required|integer|exists:forms,id',
            'amount' => 'required|numeric|min:0.0001',
            'values' => 'required|array|min:1|max:50',
            'values.*.inputId' => 'required|integer|exists:form_inputs,id,form_id,' . $this->request->get('formId'),
            'values.*.groupValueId' => 'integer|exists:form_group_values,id',
            'values.*.numericValue' => 'numeric|min:0.0001',
            'values.*.textValue' => 'string|min:1|max:255',
        ];
    }
}
