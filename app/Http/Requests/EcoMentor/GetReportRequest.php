<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/eco-mentor/reports",
 *   description="Get reports",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     description="Form id",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="facilityIds",
 *     in="query",
 *     description="1,2,3",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="years",
 *     in="query",
 *     description="2020,2021",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="monthIds",
 *     in="query",
 *     description="1,2,3",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get reports"
 *   ),
 * )
 */
class GetReportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'integer|exists:forms,id',
            'facilityIds' => 'string|max:255',
            'years' => 'string|max:255',
            'monthIds' => 'string|max:255',
        ];
    }
}
