<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/eco-mentor/login",
 *   description="Login user",
 *   tags={"eco-mentor"},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Login user",
 *     @OA\JsonContent(
 *       required={"email"},
 *       @OA\Property(property="email", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Login user"
 *   ),
 * )
 */
class LoginRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|string|email|max:255',
        ];
    }
}
