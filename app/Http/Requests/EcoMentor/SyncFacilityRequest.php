<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/eco-mentor/facilities/sync",
 *   description="Sync facilities",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Sync facilities",
 *     @OA\JsonContent(
 *       type="array",
 *       @OA\Items(
 *         type="object",
 *         required={"name","address","specieId"},
 *         properties={
 *           @OA\Property(property="id", type="integer"),
 *           @OA\Property(property="name", type="string"),
 *           @OA\Property(property="address", type="string"),
 *           @OA\Property(property="specieId", type="integer"),
 *         }
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Sync facilities"
 *   ),
 * )
 */
class SyncFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            '*.id' => 'nullable|integer',
            '*.name' => 'required|string|max:255',
            '*.address' => 'required|string|max:1000',
            '*.specieId' => 'required|integer|exists:facility_species,id',
        ];
    }
}
