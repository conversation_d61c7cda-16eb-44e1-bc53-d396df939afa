<?php

namespace App\Http\Requests\EcoMentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/eco-mentor/forms/categories",
 *   description="Get form categories",
 *   tags={"eco-mentor"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="ECO-MENTOR-API-KEY",
 *     in="header",
 *     required=true,
 *     example="ECO_MENTOR_API_KEY",
 *     @OA\Schema( type="string")
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get form categories"
 *   ),
 * )
 */
class GetCategoryRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
