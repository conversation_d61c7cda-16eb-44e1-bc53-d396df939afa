<?php

namespace App\Http\Requests\Tubitak;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/tubitak/applications",
 *   description="Create application",
 *   tags={"tubitak"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create application",
 *     @OA\JsonContent(
 *       required={"companyName", "taxNumber", "city", "state", "turnover", "personnelNumber", "sectorId", "isProdis",
 *         "managerName", "managerSurname", "managerEmail", "managerPhone", "managerTitle", "contactName", "contactSurname",
 *         "contactEmail", "contactPhone", "contactTitle"},
 *       @OA\Property(property="companyName", type="string"),
 *       @OA\Property(property="taxNumber", type="string"),
 *       @OA\Property(property="city", type="string"),
 *       @OA\Property(property="state", type="string"),
 *       @OA\Property(property="turnover", type="string"),
 *       @OA\Property(property="personnelNumber", type="integer"),
 *       @OA\Property(property="sectorId", type="integer"),
 *       @OA\Property(property="isProdis", type="boolean"),
 *       @OA\Property(property="managerName", type="string"),
 *       @OA\Property(property="managerSurname", type="string"),
 *       @OA\Property(property="managerEmail", type="string"),
 *       @OA\Property(property="managerPhone", type="string"),
 *       @OA\Property(property="managerTitle", type="string"),
 *       @OA\Property(property="contactName", type="string"),
 *       @OA\Property(property="contactSurname", type="string"),
 *       @OA\Property(property="contactEmail", type="string"),
 *       @OA\Property(property="contactPhone", type="string"),
 *       @OA\Property(property="contactTitle", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create application"
 *   ),
 * )
 */
class CreateApplicationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyName' => 'required|string|max:255',
            'taxNumber' => 'required|string|max:50',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'turnover' => 'required|string|max:50',
            'personnelNumber' => 'required|integer|min:0',
            'sectorId' => 'required|integer|exists:company_sectors,id',
            'isProdis' => 'required|boolean',
            'managerName' => 'required|string|max:255',
            'managerSurname' => 'required|string|max:255',
            'managerEmail' => 'required|email|max:255',
            'managerPhone' => 'required|string|max:30',
            'managerTitle' => 'required|string|max:255',
            'contactName' => 'required|string|max:255',
            'contactSurname' => 'required|string|max:255',
            'contactEmail' => 'required|email|max:255',
            'contactPhone' => 'required|string|max:30',
            'contactTitle' => 'required|string|max:255',
        ];
    }
}
