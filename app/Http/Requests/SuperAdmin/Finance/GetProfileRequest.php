<?php

namespace App\Http\Requests\SuperAdmin\Finance;

use App\Http\Requests\GeneralRequest;

class GetProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'jti' => 'required|string',
        ];
    }
}
