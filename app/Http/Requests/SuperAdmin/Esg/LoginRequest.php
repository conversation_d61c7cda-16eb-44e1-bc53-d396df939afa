<?php

namespace App\Http\Requests\SuperAdmin\Esg;

use App\Http\Requests\GeneralRequest;

class LoginRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'jwtId' => 'required|string',
            'expiredAt' => 'required|date',
            'email' => 'required|email|max:255',
            'fullName' => 'required|string|max:255',
            'companyName' => 'required|string|max:255',
            'companyId' => 'integer|exists:companies,id',
        ];
    }
}
