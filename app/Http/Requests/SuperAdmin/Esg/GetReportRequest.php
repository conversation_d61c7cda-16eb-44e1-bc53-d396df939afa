<?php

namespace App\Http\Requests\SuperAdmin\Esg;

use App\Http\Requests\GeneralRequest;

class GetReportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyIds' => 'required|string',
            'year' => 'integer',
        ];
    }
}
