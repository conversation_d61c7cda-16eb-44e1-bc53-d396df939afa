<?php

namespace App\Http\Requests\SuperAdmin\Tip;

use App\Http\Requests\GeneralRequest;

class AddTipRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'userId' => 'required|integer|exists:users,id',
            'action' => 'required|string|max:255|exists:tips,action',
        ];
    }
}
