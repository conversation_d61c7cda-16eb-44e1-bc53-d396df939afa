<?php

namespace App\Http\Requests\Inosuit\Inosuit;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/inosuit/files",
 *   description="Create file",
 *   tags={"inosuit"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create file",
 *     @OA\MediaType(
 *       mediaType="multipart/form-data",
 *       @OA\Schema(
 *         required={"path","file"},
 *         @OA\Property(property="path", type="string"),
 *         @OA\Property(property="file", type="string", format="binary"),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create file"
 *   ),
 * )
 */
class CreateFileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'path' => 'required|string|min:1|max:20',
            'file' => 'required|file|max:10240', // 10 MB
        ];
    }
}
