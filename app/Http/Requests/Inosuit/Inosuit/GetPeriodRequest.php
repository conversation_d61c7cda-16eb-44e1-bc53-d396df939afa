<?php

namespace App\Http\Requests\Inosuit\Inosuit;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/periods",
 *   description="Get periods",
 *   tags={"inosuit"},
 *   @OA\Parameter(
 *     name="visibleInsouitApplication",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="boolean"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get periods"
 *   ),
 * )
 */
class GetPeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        if ($this->has('visibleInsouitApplication')) {
            $this->merge([
                'visibleInsouitApplication' => $this->visibleInsouitApplication === 'true',
            ]);
        }
        return [
            'visibleInsouitApplication' => 'boolean',
        ];
    }
}
