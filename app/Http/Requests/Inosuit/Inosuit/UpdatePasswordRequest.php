<?php

namespace App\Http\Requests\Inosuit\Inosuit;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/users/password",
 *   description="Update user password",
 *   tags={"inosuit"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update user password",
 *     @OA\JsonContent(
 *       required={"oldPassword","newPassword"},
 *       @OA\Property(property="oldPassword", type="string"),
 *       @OA\Property(property="newPassword", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update user password"
 *   ),
 * )
 */
class UpdatePasswordRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'oldPassword' => 'required|string|min:6|max:32',
            'newPassword' => 'required|string|min:6|max:32',
        ];
    }
}
