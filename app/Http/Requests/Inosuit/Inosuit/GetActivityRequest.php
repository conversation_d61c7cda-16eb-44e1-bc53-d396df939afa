<?php

namespace App\Http\Requests\Inosuit\Inosuit;

use App\Http\Requests\GeneralRequest;
use App\Utils\GeneralUtil;

/**
 * @OA\Get(
 *   path="/api/inosuit/activities",
 *   description="Get activities",
 *   tags={"inosuit"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="page",
 *     in="query",
 *     description="default: 1",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="limit",
 *     in="query",
 *     description="default: 10",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="statuses",
 *     in="query",
 *     description="-1,0,1",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get activities"
 *   ),
 * )
 */
class GetActivityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            ...GeneralUtil::paginationValidations($this),
            'statuses' => 'string',
        ];
    }
}
