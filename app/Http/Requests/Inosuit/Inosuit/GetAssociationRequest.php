<?php

namespace App\Http\Requests\Inosuit\Inosuit;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/associations",
 *   description="Get associations",
 *   tags={"inosuit"},
 *   @OA\Response(
 *     response=200,
 *     description="Get associations"
 *   ),
 * )
 */
class GetAssociationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
