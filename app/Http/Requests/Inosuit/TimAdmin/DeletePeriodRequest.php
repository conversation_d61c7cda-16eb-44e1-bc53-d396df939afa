<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/inosuit/tim-admin/periods",
 *   description="Delete period",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Delete period",
 *     @OA\JsonContent(
 *       required={"periodId"},
 *       @OA\Property(property="periodId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete period"
 *   ),
 * )
 */
class DeletePeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'periodId' => 'required|integer|exists:periods,id',
        ];
    }
}
