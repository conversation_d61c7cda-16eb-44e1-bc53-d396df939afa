<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/exports/period-reports",
 *   description="Export period reports with companies and mentors data",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Excel file containing period reports",
 *     @OA\Header(
 *       header="Content-Disposition",
 *       description="Attachment header for file download",
 *       @OA\Schema(type="string", example="attachment; filename=period_reports.xlsx")
 *     ),
 *     @OA\MediaType(
 *       mediaType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
 *       @OA\Schema(type="string", format="binary")
 *     )
 *   ),
 *   @OA\Response(
 *     response=401,
 *     description="Unauthorized"
 *   ),
 *   @OA\Response(
 *     response=403,
 *     description="Forbidden - Tim Admin privilege required"
 *   ),
 * )
 */
class GetPeriodReportExportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
