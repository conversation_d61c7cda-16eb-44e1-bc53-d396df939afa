<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/mentors/applications/detail",
 *   description="Get mentor application detail",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="applicationId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get mentor application detail"
 *   ),
 * )
 */
class GetMentorApplicationDetailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'applicationId' => 'required|integer|exists:mentor_inosuit_applications,id',
        ];
    }
}
