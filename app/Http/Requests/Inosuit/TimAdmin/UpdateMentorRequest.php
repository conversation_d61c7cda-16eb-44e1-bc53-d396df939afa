<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/mentors",
 *   description="Update mentor",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update mentor",
 *     @OA\JsonContent(
 *       required={"userId"},
 *       @OA\Property(property="userId", type="integer"),
 *       @OA\Property(property="isActive", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update mentor"
 *   ),
 * )
 */
class UpdateMentorRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'userId' => 'required|integer|exists:mentors,user_id',
            'isActive' => 'boolean',
        ];
    }
}
