<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/associations",
 *   description="Update association",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update association",
 *     @OA\JsonContent(
 *       required={"associationId"},
 *       @OA\Property(property="associationId", type="integer"),
 *       @OA\Property(property="isActive", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update association"
 *   ),
 * )
 */
class UpdateAssociationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'associationId' => 'required|integer|exists:associations,id',
            'isActive' => 'boolean',
        ];
    }
}
