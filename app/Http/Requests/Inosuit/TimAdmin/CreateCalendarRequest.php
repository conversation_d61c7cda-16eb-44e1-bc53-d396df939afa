<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/inosuit/tim-admin/calendars",
 *   description="Create calendar",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create calendar",
 *     @OA\JsonContent(
 *       required={"periodId", "name"},
 *       @OA\Property(property="periodId", type="integer"),
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="goal", type="string"),
 *       @OA\Property(property="advice", type="string"),
 *       @OA\Property(property="term", type="string"),
 *       @OA\Property(property="tool", type="string"),
 *       @OA\Property(property="source", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create calendar"
 *   ),
 * )
 */
class CreateCalendarRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'periodId' => 'required|integer|exists:periods,id',
            'name' => 'required|string|min:1|max:255',
            'description' => 'string|min:1|max:65535',
            'goal' => 'string|min:1|max:65535',
            'advice' => 'string|min:1|max:65535',
            'term' => 'string|min:1|max:65535',
            'tool' => 'string|min:1|max:65535',
            'source' => 'string|min:1|max:65535',
        ];
    }
}
