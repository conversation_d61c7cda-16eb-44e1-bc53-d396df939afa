<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/companies",
 *   description="Update company",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update company",
 *     @OA\JsonContent(
 *       required={"companyPeriodId"},
 *       @OA\Property(property="companyPeriodId", type="integer"),
 *       @OA\Property(property="status", type="boolean"),
 *       @OA\Property(property="laneId", type="integer"),
 *       @OA\Property(property="associationId", type="integer"),
 *       @OA\Property(property="mentorUserId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update company"
 *   ),
 * )
 */
class UpdateCompanyRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyPeriodId' => 'required|integer|exists:company_periods,id',
            'status' => 'boolean',
            'laneId' => 'integer|exists:period_lanes,id',
            'associationId' => 'integer|exists:associations,id',
            'mentorUserId' => 'integer|exists:mentors,user_id',
        ];
    }
}
