<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/companies/profile",
 *   description="Get company profile",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="companyId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get company profile"
 *   ),
 * )
 */
class GetCompanyProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyId' => 'required|integer|exists:companies,id',
        ];
    }
}
