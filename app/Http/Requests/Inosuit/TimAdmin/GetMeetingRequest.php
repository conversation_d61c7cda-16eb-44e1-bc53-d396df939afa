<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;
use App\Utils\GeneralUtil;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/meetings",
 *   description="Get meetings",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="page",
 *     in="query",
 *     description="default: 1",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="limit",
 *     in="query",
 *     description="default: 10",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="companyIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="periodIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="mentorUserIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get meetings"
 *   ),
 * )
 */
class GetMeetingRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            ...GeneralUtil::paginationValidations($this),
            'companyIds' => 'string|min:1|max:255',
            'periodIds' => 'string|min:1|max:255',
            'mentorUserIds' => 'string|min:1|max:255',
        ];
    }
}
