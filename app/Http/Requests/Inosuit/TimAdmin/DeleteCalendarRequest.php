<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/inosuit/tim-admin/calendars",
 *   description="Delete calendar",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Delete calendar",
 *     @OA\JsonContent(
 *       required={"calendarId"},
 *       @OA\Property(property="calendarId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete calendar"
 *   ),
 * )
 */
class DeleteCalendarRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'calendarId' => 'required|integer|exists:calendars,id',
        ];
    }
}
