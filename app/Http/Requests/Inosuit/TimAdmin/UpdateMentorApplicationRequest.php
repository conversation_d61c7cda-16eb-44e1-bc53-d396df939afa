<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/mentors/applications",
 *   description="Update mentor application",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update mentor application",
 *     @OA\JsonContent(
 *       required={"applicationId"},
 *       @OA\Property(property="applicationId", type="integer"),
 *       @OA\Property(property="isApprove", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update mentor application"
 *   ),
 * )
 */
class UpdateMentorApplicationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'applicationId' => 'required|integer|exists:mentor_inosuit_applications,id',
            'isApprove' => 'required|boolean',
        ];
    }
}
