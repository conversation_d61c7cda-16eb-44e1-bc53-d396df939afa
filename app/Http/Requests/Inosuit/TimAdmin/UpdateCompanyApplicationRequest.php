<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/companies/applications",
 *   description="Update company application",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update company application",
 *     @OA\JsonContent(
 *       required={"applicationId","isApprove"},
 *       @OA\Property(property="applicationId", type="integer"),
 *       @OA\Property(property="isApprove", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update company application"
 *   ),
 * )
 */
class UpdateCompanyApplicationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'applicationId' => 'required|integer|exists:company_inosuit_applications,id',
            'isApprove' => 'required|boolean',
        ];
    }
}
