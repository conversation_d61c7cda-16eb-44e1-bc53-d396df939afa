<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Enums\Inosuit\PaperStatusEnum;
use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/mentors/papers",
 *   description="Update mentor paper",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update mentor paper",
 *     @OA\JsonContent(
 *       required={"paperId"},
 *       @OA\Property(property="paperId", type="integer"),
 *       @OA\Property(property="status", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update mentor paper"
 *   ),
 * )
 */
class UpdateMentorPaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'paperId' => 'required|integer|exists:mentor_papers,id',
            'status' => ['integer', Rule::in(PaperStatusEnum::DECLINED->value, PaperStatusEnum::APPROVED->value)],
        ];
    }
}
