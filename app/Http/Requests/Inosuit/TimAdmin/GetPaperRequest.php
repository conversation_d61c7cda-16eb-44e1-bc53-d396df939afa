<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/papers",
 *   description="Get papers",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="search",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="companyIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="periodIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="associationIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="mentorUserIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="laneIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get papers"
 *   ),
 * )
 */
class GetPaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'string|min:1|max:255',
            'companyIds' => 'string|min:1|max:255',
            'periodIds' => 'string|min:1|max:255',
            'associationIds' => 'string|min:1|max:255',
            'mentorUserIds' => 'string|min:1|max:255',
            'laneIds' => 'string|min:1|max:255',
        ];
    }
}
