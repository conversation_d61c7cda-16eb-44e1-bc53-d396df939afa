<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/calendars",
 *   description="Update calendar",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update calendar",
 *     @OA\JsonContent(
 *       required={"calendarId", "name"},
 *       @OA\Property(property="calendarId", type="integer"),
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="goal", type="string"),
 *       @OA\Property(property="advice", type="string"),
 *       @OA\Property(property="term", type="string"),
 *       @OA\Property(property="tool", type="string"),
 *       @OA\Property(property="source", type="string"),
 *       @OA\Property(property="order", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update calendar"
 *   ),
 * )
 */
class UpdateCalendarRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'calendarId' => 'required|integer|exists:calendars,id',
            'name' => 'required|string|min:1|max:255',
            'description' => 'nullable|string|max:65535',
            'goal' => 'nullable|string|max:65535',
            'advice' => 'nullable|string|max:65535',
            'term' => 'nullable|string|max:65535',
            'tool' => 'nullable|string|max:65535',
            'source' => 'nullable|string|max:65535',
            'order' => 'integer|min:1',
        ];
    }
}
