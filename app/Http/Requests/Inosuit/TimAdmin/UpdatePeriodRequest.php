<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/tim-admin/periods",
 *   description="Update period",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update period",
 *     @OA\JsonContent(
 *       required={"periodId"},
 *       @OA\Property(property="periodId", type="integer"),
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="startDate", type="datetime"),
 *       @OA\Property(property="endDate", type="datetime"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update period"
 *   ),
 * )
 */
class UpdatePeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'periodId' => 'required|integer|exists:periods,id',
            'name' => 'string|min:1|max:255',
            'startDate' => 'date',
            'endDate' => 'date',
        ];
    }
}
