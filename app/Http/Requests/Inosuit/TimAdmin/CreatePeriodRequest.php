<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/inosuit/tim-admin/periods",
 *   description="Create period",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create period",
 *     @OA\JsonContent(
 *       required={"name", "startDate", "endDate"},
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="startDate", type="datetime"),
 *       @OA\Property(property="endDate", type="datetime"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create period"
 *   ),
 * )
 */
class CreatePeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|min:1|max:255',
            'startDate' => 'required|date',
            'endDate' => 'required|date',
        ];
    }
}
