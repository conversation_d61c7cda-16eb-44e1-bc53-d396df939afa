<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/companies/papers/detail",
 *   description="Get company paper detail",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="paperId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get company paper detail"
 *   ),
 * )
 */
class GetCompanyPaperDetailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'paperId' => 'required|integer',
        ];
    }
}
