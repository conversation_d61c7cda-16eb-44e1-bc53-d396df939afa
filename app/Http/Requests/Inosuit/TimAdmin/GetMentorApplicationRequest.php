<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;
use App\Utils\GeneralUtil;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/mentors/applications",
 *   description="Get mentor applications",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="page",
 *     in="query",
 *     description="default: 1",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="limit",
 *     in="query",
 *     description="default: 10",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="universityIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="facultyIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="departmentIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="statuses",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get mentor applications"
 *   ),
 * )
 */
class GetMentorApplicationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            ...GeneralUtil::paginationValidations($this),
            'universityIds' => 'string|min:1|max:255',
            'facultyIds' => 'string|min:1|max:255',
            'departmentIds' => 'string|min:1|max:255',
            'statuses' => 'string|min:1|max:255',
        ];
    }
}
