<?php

namespace App\Http\Requests\Inosuit\TimAdmin;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/tim-admin/mentors",
 *   description="Get mentors",
 *   tags={"inosuit/tim-admin"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="universityIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="facultyIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="departmentIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="isActiveStatuses",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="companyIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get mentors"
 *   ),
 * )
 */
class GetMentorRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'universityIds' => 'string|min:1|max:255',
            'facultyIds' => 'string|min:1|max:255',
            'departmentIds' => 'string|min:1|max:255',
            'isActiveStatuses' => 'string|min:1|max:255',
            'companyIds' => 'string|min:1|max:255',
        ];
    }
}
