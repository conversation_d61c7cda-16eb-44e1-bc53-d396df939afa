<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;
use App\Utils\GeneralUtil;

/**
 * @OA\Get(
 *   path="/api/inosuit/mentors/companies",
 *   description="Get companies",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="page",
 *     in="query",
 *     description="default: 1",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="limit",
 *     in="query",
 *     description="default: 10",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get companies"
 *   ),
 * )
 */
class GetCompanyRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return GeneralUtil::paginationValidations($this);
    }
}
