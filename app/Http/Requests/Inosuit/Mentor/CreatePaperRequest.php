<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;
use App\Models\CompanyMeeting;
use App\Models\Mentor\Mentor;
use App\Models\Mentor\MentorPaper;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/inosuit/mentors/papers",
 *   description="Create paper",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create paper",
 *     @OA\JsonContent(
 *       required={"companyId","termDate","meetingIds"},
 *       @OA\Property(property="companyId", type="integer"),
 *       @OA\Property(property="activityOutput", type="string"),
 *       @OA\Property(property="unrealisedOutput", type="string"),
 *       @OA\Property(property="observation", type="string"),
 *       @OA\Property(property="extraUrls", type="array", @OA\Items(type="string")),
 *       @OA\Property(property="termDate", type="string", example="2024-10-01"),
 *       @OA\Property(
 *         property="meetingIds",
 *         type="array",
 *         @OA\Items(type="integer"),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create paper"
 *   ),
 * )
 */
class CreatePaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $companies = Mentor::getCurrentCompanies();
        $company = $companies->where('id', $this->companyId)->first();
        if (!$this->companyId || !$company) {
            throw new BadRequestHttpException('Geçersiz firma.');
        }
        $this->merge(['company' => $company]);

        $meetings = CompanyMeeting::query()
            ->whereIn('id', $this->meetingIds)
            ->where('company_period_id', $company->currentPeriod->id)
            ->get();
        if ($meetings->count() !== count($this->meetingIds)) {
            throw new BadRequestHttpException('Geçersiz toplantılar.');
        }

        $paperCount = MentorPaper::query()
            ->where('company_period_id', $company->currentPeriod->id)
            ->where('term_date', $this->termDate)
            ->count();
        if ($paperCount > 0) {
            throw new BadRequestHttpException('İlgili dönemde bu firmaya ait raporunuz bulunmaktadır.');
        }

        return [
            'companyId' => 'required|integer',
            'activityOutput' => 'string|min:1|max:5000',
            'unrealisedOutput' => 'string|min:1|max:5000',
            'observation' => 'string|min:1|max:5000',
            'extraUrls' => 'nullable|array|max:20',
            'extraUrls.*' => 'required|url|min:1|max:255',
            'termDate' => 'required|date',
            'meetingIds' => 'required|array',
            'meetingIds.*' => 'required|integer|distinct',
        ];
    }
}
