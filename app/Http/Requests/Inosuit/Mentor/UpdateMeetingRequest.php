<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Enums\Inosuit\CompanyMeetingStatusEnum;
use App\Enums\Inosuit\CompanyMeetingTypeEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\CompanyMeeting;
use App\Models\Mentor\Mentor;
use Illuminate\Validation\Rule;

/**
 * @OA\Put(
 *   path="/api/inosuit/mentors/meetings",
 *   description="Update meeting",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update meeting",
 *     @OA\JsonContent(
 *       required={"meetingId"},
 *       @OA\Property(property="meetingId", type="integer"),
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="location", type="string"),
 *       @OA\Property(property="locationType", type="integer"),
 *       @OA\Property(property="mentorStatus", type="integer"),
 *       @OA\Property(property="mentorNote", type="string"),
 *       @OA\Property(property="startDate", type="string", example="2024-10-25 10:00:00"),
 *       @OA\Property(property="endDate", type="string", example="2024-10-25 12:00:00"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update meeting"
 *   ),
 * )
 */
class UpdateMeetingRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        CompanyMeeting::query()
            ->where('id', $this->meetingId)
            ->whereIn('company_period_id', Mentor::getCompanyPeriodIds())
            ->firstOrFail();
        return [
            'meetingId' => 'required|integer',
            'name' => 'string|nullable|min:1|max:255',
            'description' => 'string|nullable|min:1|max:1000',
            'location' => 'string|nullable|min:1|max:255',
            'locationType' => 'integer|in:' . implode(',', CompanyMeetingTypeEnum::values()),
            'mentorStatus' => ['integer', Rule::in(CompanyMeetingStatusEnum::DECLINED->value, CompanyMeetingStatusEnum::APPROVED->value)],
            'mentorNote' => 'string|nullable|min:1|max:1000',
            'startDate' => 'date',
            'endDate' => 'date',
        ];
    }
}
