<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Enums\Inosuit\PaperStatusEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\CompanyMeeting;
use App\Models\Mentor\Mentor;
use App\Models\Mentor\MentorPaper;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Put(
 *   path="/api/inosuit/mentors/papers",
 *   description="Update paper",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update paper",
 *     @OA\JsonContent(
 *       required={"paperId"},
 *       @OA\Property(property="paperId", type="integer"),
 *       @OA\Property(property="activityOutput", type="string"),
 *       @OA\Property(property="unrealisedOutput", type="string"),
 *       @OA\Property(property="observation", type="string"),
 *       @OA\Property(property="extraUrls", type="array", @OA\Items(type="string")),
 *       @OA\Property(property="termDate", type="string", example="2024-10-01"),
 *       @OA\Property(
 *         property="meetingIds",
 *         type="array",
 *         @OA\Items(type="integer"),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update paper"
 *   ),
 * )
 */
class UpdatePaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $companyPeriodIds = Mentor::getCompanyPeriodIds();
        if ($this->meetingIds) {
            $meetings = CompanyMeeting::query()
                ->whereIn('id', $this->meetingIds)
                ->whereIn('company_period_id', $companyPeriodIds)
                ->get();
            if ($meetings->count() !== count($this->meetingIds)) {
                throw new BadRequestHttpException('Geçersiz toplantılar.');
            }
        }
        MentorPaper::query()
            ->whereIn('company_period_id', $companyPeriodIds)
            ->whereIn('status', [PaperStatusEnum::DECLINED, PaperStatusEnum::PENDING])
            ->findOrFail($this->paperId);

        return [
            'paperId' => 'required|integer',
            'activityOutput' => 'string|min:1|max:5000',
            'unrealisedOutput' => 'string|min:1|max:5000',
            'observation' => 'string|min:1|max:5000',
            'extraUrls' => 'nullable|array|max:20',
            'extraUrls.*' => 'required|url|min:1|max:255',
            'termDate' => 'date',
            'meetingIds' => 'array',
            'meetingIds.*' => 'required|integer|distinct',
        ];
    }
}
