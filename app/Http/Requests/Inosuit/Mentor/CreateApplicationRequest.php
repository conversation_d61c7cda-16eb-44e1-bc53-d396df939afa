<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;
use App\Models\User;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/inosuit/mentors/applications",
 *   description="Create application",
 *   tags={"inosuit/mentors"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create application",
 *     @OA\JsonContent(
 *       required={"fullName", "email", "phone", "title", "linkedinUrl", "cvUrl"},
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="title", type="string"),
 *       @OA\Property(property="linkedinUrl", type="string"),
 *       @OA\Property(property="cvUrl", type="string"),
 *       @OA\Property(property="imageUrl", type="string"),
 *       @OA\Property(property="extraUrls", type="array", @OA\Items(type="string")),
 *       @OA\Property(property="organizationType", type="string"),
 *       @OA\Property(property="organizationName", type="string"),
 *       @OA\Property(property="experienceYear", type="integer"),
 *       @OA\Property(property="isSustainable", type="boolean"),
 *       @OA\Property(property="sustainableText", type="string"),
 *       @OA\Property(property="isoCertificateText", type="string"),
 *       @OA\Property(property="universityId", type="integer"),
 *       @OA\Property(property="facultyId", type="integer"),
 *       @OA\Property(property="departmentId", type="integer"),
 *       @OA\Property(
 *         property="contact",
 *         type="object",
 *         properties={
 *           @OA\Property(property="fullName", type="string"),
 *           @OA\Property(property="email", type="string"),
 *           @OA\Property(property="phone", type="string"),
 *           @OA\Property(property="title", type="string"),
 *         }
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create application"
 *   ),
 * )
 */
class CreateApplicationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $user = User::with('mentor')
            ->where('email', $this->email)
            ->first();
        if ($user?->mentor) {
            throw new BadRequestHttpException('Mentor kaydınız zaten mevcuttur.');
        }

        return [
            'fullName' => 'required|string|min:1|max:255',
            'email' => 'required|email|min:1|max:255',
            'phone' => 'required|string|min:1|max:30',
            'title' => 'required|string|min:1|max:255',
            'linkedinUrl' => 'required|min:1|max:255',
            'cvUrl' => 'required|url|min:1|max:255',
            'imageUrl' => 'url|min:1|max:255',
            'extraUrls' => 'nullable|array|min:1|max:20',
            'extraUrls.*' => 'required|url|min:1|max:255',
            'organizationType' => 'string|max:255',
            'organizationName' => 'string|max:255',
            'experienceYear' => 'integer|min:0|max:255',
            'isSustainable' => 'boolean',
            'sustainableText' => 'string|max:3000',
            'isoCertificateText' => 'string|max:3000',
            'universityId' => 'integer|exists:edu_universities,id',
            'facultyId' => 'integer|exists:edu_faculties,id',
            'departmentId' => 'integer|exists:edu_departments,id',
            'contact' => 'array',
            'contact.fullName' => 'string|min:1|max:255',
            'contact.email' => 'email|min:1|max:255',
            'contact.phone' => 'string|min:1|max:30',
            'contact.title' => 'string|min:1|max:255',
        ];
    }

    public function attributes(): array
    {
        return [
            'fullName' => 'Ad soyad',
            'email' => 'E-posta',
            'phone' => 'Telefon',
            'title' => 'Unvan',
            'linkedinUrl' => 'Linkedin adres',
            'cvUrl' => 'Özgeçmiş',
            'imageUrl' => 'Profil foroğrafı',
            'extraUrls' => 'Destekleyici belgeler',
            'extraUrls.*' => 'Destekleyici belgeler',
            'organizationType' => 'Bağlı olduğunuz kurum',
            'organizationName' => 'Kurum tam adı',
            'experienceYear' => 'Profesyonel iş deneyimi',
            'isSustainable' => 'Sürdürülebilirlik mevcut mu sorusu',
            'sustainableText' => 'Sürdürülebilirlik faaliyet alanı çalışmaları',
            'isoCertificateText' => 'ISO/TSE onaylı sertifikalar',
            'universityId' => 'Üniversite',
            'facultyId' => 'Fakülte',
            'departmentId' => 'Bölüm',
            'contact' => 'Üniversite iletişim',
            'contact.fullName' => 'Üniversite iletişim sorumlusu ad soyad',
            'contact.email' => 'Üniversite iletişim sorumlusu e-posta',
            'contact.phone' => 'Üniversite iletişim sorumlusu telefon',
            'contact.title' => 'Üniversite iletişim sorumlusu unvan',
        ];
    }
}
