<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;
use App\Models\CompanyPeriod;

/**
 * @OA\Put(
 *   path="/api/inosuit/mentors/profile",
 *   description="Update profile",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update profile",
 *     @OA\JsonContent(
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="title", type="string"),
 *       @OA\Property(property="photo", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="address", type="string"),
 *       @OA\Property(property="cvUrl", type="string"),
 *       @OA\Property(property="universityId", type="integer"),
 *       @OA\Property(property="facultyId", type="integer"),
 *       @OA\Property(property="departmentId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update profile"
 *   ),
 * )
 */
class UpdateProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'fullName' => 'string|min:1|max:255',
            'phone' => 'string|min:1|max:30',
            'title' => 'string|min:1|max:255',
            'photo' => 'url|min:1|max:255',
            'description' => 'string|nullable|max:500',
            'address' => 'string|nullable|max:500',
            'cvUrl' => 'string|url|max:255',
            'universityId' => 'integer|exists:edu_universities,id',
            'facultyId' => 'integer|exists:edu_faculties,id',
            'departmentId' => 'integer|exists:edu_departments,id',
        ];
    }
}
