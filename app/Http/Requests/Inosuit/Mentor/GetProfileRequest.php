<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/mentors/profile",
 *   description="Get mentor profile",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get mentor profile"
 *   ),
 * )
 */
class GetProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
