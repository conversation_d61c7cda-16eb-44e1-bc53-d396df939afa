<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/mentors/periods",
 *   description="Get periods",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="companyId",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get periods"
 *   ),
 * )
 */
class GetPeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyId' => 'integer',
        ];
    }
}
