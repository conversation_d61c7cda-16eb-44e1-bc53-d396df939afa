<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;
use App\Models\Mentor\Mentor;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Get(
 *   path="/api/inosuit/mentors/papers/terms",
 *   description="Get paper terms",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="companyId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get paper terms"
 *   ),
 * )
 */
class GetPaperTermRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $companies = Mentor::getCurrentCompanies();
        $company = $companies->where('id', $this->companyId)->first();
        if (!$this->companyId || !$company) {
            throw new BadRequestHttpException('Geçersiz firma.');
        }
        $this->merge(['company' => $company]);

        return [
            'companyId' => 'required|integer',
        ];
    }
}
