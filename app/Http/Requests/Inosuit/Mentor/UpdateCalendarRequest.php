<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/mentors/calendars",
 *   description="Update calendar",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update calendar",
 *     @OA\JsonContent(
 *       required={"companyId", "calendarId"},
 *       @OA\Property(property="companyId", type="integer"),
 *       @OA\Property(property="calendarId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update calendar"
 *   ),
 * )
 */
class UpdateCalendarRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyId' => 'required|integer',
            'calendarId' => 'required|integer|exists:calendars,id',
        ];
    }
}
