<?php

namespace App\Http\Requests\Inosuit\Mentor;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/inosuit/mentors/meetings",
 *   description="Delete meeting",
 *   tags={"inosuit/mentors"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Delete meeting",
 *     @OA\JsonContent(
 *       required={"meetingId"},
 *       @OA\Property(property="meetingId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete meeting"
 *   ),
 * )
 */
class DeleteMeetingRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'meetingId' => 'required|integer',
        ];
    }
}
