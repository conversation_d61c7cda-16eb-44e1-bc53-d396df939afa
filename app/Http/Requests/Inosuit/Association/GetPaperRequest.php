<?php

namespace App\Http\Requests\Inosuit\Association;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/associations/papers",
 *   description="Get papers",
 *   tags={"inosuit/associations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="search",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="companyIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="periodIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="mentorUserIds",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get papers"
 *   ),
 * )
 */
class GetPaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'string|min:1|max:255',
            'companyIds' => 'string|min:1|max:255',
            'periodIds' => 'string|min:1|max:255',
            'mentorUserIds' => 'string|min:1|max:255',
        ];
    }
}
