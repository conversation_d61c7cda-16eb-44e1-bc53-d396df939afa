<?php

namespace App\Http\Requests\Inosuit\Association;

use App\Http\Requests\GeneralRequest;
use App\Utils\GeneralUtil;

/**
 * @OA\Get(
 *   path="/api/inosuit/associations/mentors/profile",
 *   description="Get mentor profile",
 *   tags={"inosuit/associations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="mentorUserId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get mentor profile"
 *   ),
 * )
 */
class GetMentorProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'mentorUserId' => 'required|integer|exists:mentors,user_id',
        ];
    }
}
