<?php

namespace App\Http\Requests\Inosuit\Association;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/associations/companies/profile",
 *   description="Get company profile",
 *   tags={"inosuit/associations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="companyId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get company profile"
 *   ),
 * )
 */
class GetCompanyProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'companyId' => 'required|integer|exists:companies,id',
        ];
    }
}
