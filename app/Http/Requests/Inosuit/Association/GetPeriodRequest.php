<?php

namespace App\Http\Requests\Inosuit\Association;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/associations/periods",
 *   description="Get periods",
 *   tags={"inosuit/associations"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get periods"
 *   ),
 * )
 */
class GetPeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
