<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Enums\Inosuit\CompanyPaperAnswerEnum;
use App\Enums\Inosuit\PaperStatusEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\CompanyPeriod;

/**
 * @OA\Put(
 *   path="/api/inosuit/companies/papers",
 *   description="Update paper",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update paper",
 *     @OA\JsonContent(
 *       required={"paperId"},
 *       @OA\Property(property="paperId", type="integer"),
 *       @OA\Property(property="observation", type="string"),
 *       @OA\Property(property="termDate", type="string", example="2024-10-01"),
 *       @OA\Property(
 *         property="meetingIds",
 *         type="array",
 *         @OA\Items(type="integer"),
 *       ),
 *       @OA\Property(
 *         property="answers",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"questionId","answerType"},
 *           properties={
 *             @OA\Property(property="questionId", type="integer"),
 *             @OA\Property(property="answerType", type="integer"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update paper"
 *   ),
 * )
 */
class UpdatePaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();

        return [
            'paperId' => 'required|integer|exists:company_papers,id,company_period_id,' . $currentPeriod->id,
            'observation' => 'string|min:1|max:5000',
            'termDate' => 'date',
            'meetingIds' => 'array',
            'meetingIds.*' => 'required|integer|distinct|exists:company_meetings,id,company_period_id,' . $currentPeriod->id,
            'answers' => 'array|size:4',
            'answers.*.questionId' => 'required|integer|distinct|exists:company_paper_questions,id',
            'answers.*.answerType' => 'required|integer|in:' . implode(',', CompanyPaperAnswerEnum::values()),
        ];
    }
}
