<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/companies/papers/questions",
 *   description="Get paper questions",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get paper questions"
 *   ),
 * )
 */
class GetPaperQuestionRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
