<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;
use App\Utils\GeneralUtil;
use Illuminate\Validation\Rule;

/**
 * @OA\Get(
 *   path="/api/inosuit/companies/meetings",
 *   description="Get meetings",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="page",
 *     in="query",
 *     description="default: 1",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="limit",
 *     in="query",
 *     description="default: 10",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="startDate",
 *     in="query",
 *     example="2024-09-01T00.00.00",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="endDate",
 *     in="query",
 *     example="2024-09-30T23.59.59",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get meetings"
 *   ),
 * )
 */
class GetMeetingRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            ...GeneralUtil::paginationValidations($this),
            'startDate' => ['date', Rule::requiredIf($this->has('endDate'))],
            'endDate' => ['date', Rule::requiredIf($this->has('startDate'))],
        ];
    }
}
