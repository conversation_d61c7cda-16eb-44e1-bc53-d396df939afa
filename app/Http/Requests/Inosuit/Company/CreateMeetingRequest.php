<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Enums\Inosuit\CompanyMeetingTypeEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\CompanyMeeting;
use App\Models\CompanyPeriod;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/inosuit/companies/meetings",
 *   description="Create meeting",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create meeting",
 *     @OA\JsonContent(
 *       required={"locationType","startDate","endDate"},
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="location", type="string"),
 *       @OA\Property(property="locationType", type="integer"),
 *       @OA\Property(property="note", type="string"),
 *       @OA\Property(property="startDate", type="string", example="2024-10-25 10:00:00"),
 *       @OA\Property(property="endDate", type="string", example="2024-10-25 12:00:00"),
 *       @OA\Property(property="checkSameDay", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create meeting"
 *   ),
 * )
 */
class CreateMeetingRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        if ($this->checkSameDay) {
            $currentPeriod = CompanyPeriod::getCurrentPeriod();
            $meetingCount = CompanyMeeting::query()
                ->where('company_period_id', $currentPeriod->id)
                ->whereNot(function ($q) {
                    if (!request()->startDate || !request()->endDate) {
                        return $q;
                    }
                    return $q
                        ->where('start_date', '>', request()->startDate)
                        ->where('start_date', '>', request()->endDate)
                        ->orWhere('end_date', '<', request()->startDate)
                        ->where('end_date', '<', request()->endDate);
                })
                ->count();
            if ($meetingCount > 0) {
                throw new BadRequestHttpException('Aynı aralıkta farklı bir ziyaret mevcut. Yine de eklemek istediğinize emin misiniz?');
            }
        }
        return [
            'name' => 'string|nullable|min:1|max:255',
            'description' => 'string|nullable|min:1|max:1000',
            'location' => 'string|nullable|min:1|max:255',
            'locationType' => 'required|integer|in:' . implode(',', CompanyMeetingTypeEnum::values()),
            'note' => 'string|nullable|min:1|max:1000',
            'startDate' => 'required|date',
            'endDate' => 'required|date',
            'checkSameDay' => 'boolean',
        ];
    }
}
