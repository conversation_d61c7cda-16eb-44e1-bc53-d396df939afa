<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;
use App\Models\CompanyInosuitApplicationQuestion;
use App\Models\User;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/inosuit/companies/applications",
 *   description="Create application",
 *   tags={"inosuit/companies"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create application",
 *     @OA\JsonContent(
 *       required={
 *         "sectorId", "associationId", "periodId", "name", "taxNumber", "email", "phone", "website", "address",
 *         "activity", "foundDate", "whiteCollarCount", "blueCollarCount", "annualSale", "balanceSheet", "isKosgeb",
 *         "manager", "contact", "answers"
 *       },
 *       @OA\Property(property="sectorId", type="integer"),
 *       @OA\Property(property="associationId", type="integer"),
 *       @OA\Property(property="periodId", type="integer"),
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="taxNumber", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="website", type="string"),
 *       @OA\Property(property="address", type="string"),
 *       @OA\Property(property="activity", type="string"),
 *       @OA\Property(property="foundDate", type="date"),
 *       @OA\Property(property="whiteCollarCount", type="integer"),
 *       @OA\Property(property="blueCollarCount", type="integer"),
 *       @OA\Property(property="annualSale", type="string"),
 *       @OA\Property(property="balanceSheet", type="string"),
 *       @OA\Property(property="isKosgeb", type="boolean"),
 *       @OA\Property(property="isArge", type="boolean"),
 *       @OA\Property(
 *         property="manager",
 *         type="object",
 *         required={"fullName","email","phone","title"},
 *         properties={
 *           @OA\Property(property="fullName", type="string"),
 *           @OA\Property(property="email", type="string"),
 *           @OA\Property(property="phone", type="string"),
 *           @OA\Property(property="title", type="string"),
 *         }
 *       ),
 *       @OA\Property(
 *         property="contact",
 *         type="object",
 *         required={"fullName","email","phone","title"},
 *         properties={
 *           @OA\Property(property="fullName", type="string"),
 *           @OA\Property(property="email", type="string"),
 *           @OA\Property(property="phone", type="string"),
 *           @OA\Property(property="title", type="string"),
 *         }
 *       ),
 *       @OA\Property(
 *         property="answers",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"questionId"},
 *           properties={
 *             @OA\Property(property="questionId", type="integer"),
 *             @OA\Property(property="answer", type="integer"),
 *             @OA\Property(property="answerText", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create application"
 *   ),
 * )
 */
class CreateApplicationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $managerUser = User::with('company.companyPeriods')
            ->where('email', $this->manager['email'])
            ->first();
        $contactUser = User::with('company.companyPeriods')
            ->where('email', $this->contact['email'])
            ->first();
        if ($managerUser && $contactUser && $managerUser->company_id != $contactUser->company_id) {
            throw new BadRequestHttpException('Firma yöneticisi ve iletişim yetkilisi kullanıcıları sistemde mevcut ve farklı şirketlerde görünmektedir.');
        }

        $company = null;
        if ($managerUser) {
            $company = $managerUser->company;
        } else if ($contactUser) {
            $company = $contactUser->company;
        }

        if ($company) {
            $companyPeriod = $company->companyPeriods->where('period_id', $this->periodId)->first();
            if ($companyPeriod) {
                throw new BadRequestHttpException('Firmanızın bu dönem için kaydı zaten mevcut.');
            }
        }
        $answerSize = CompanyInosuitApplicationQuestion::all()->count();

        return [
            'sectorId' => 'required|integer|exists:company_sectors,id',
            'associationId' => 'required|integer|exists:associations,id',
            'periodId' => 'required|integer|exists:periods,id',
            'name' => 'required|string|min:1|max:255',
            'taxNumber' => 'required|string|min:1|max:50',
            'email' => 'required|email|min:1|max:255',
            'phone' => 'required|string|min:1|max:30',
            'website' => 'required|min:1|max:100',
            'address' => 'required|string|min:1|max:1000',
            'activity' => 'required|string|min:1|max:3000',
            'foundDate' => 'required|date',
            'whiteCollarCount' => 'required|integer|min:0',
            'blueCollarCount' => 'required|integer|min:0',
            'annualSale' => 'required|string|min:1|max:255',
            'balanceSheet' => 'required|string|min:1|max:255',
            'isKosgeb' => 'required|boolean',
            'isArge' => 'boolean',
            'manager' => 'required|array',
            'manager.fullName' => 'required|string|min:1|max:255',
            'manager.email' => 'required|email|min:1|max:255',
            'manager.phone' => 'required|string|min:1|max:30',
            'manager.title' => 'required|string|min:1|max:255',
            'contact' => 'required|array',
            'contact.fullName' => 'required|string|min:1|max:255',
            'contact.email' => 'required|email|min:1|max:255',
            'contact.phone' => 'required|string|min:1|max:30',
            'contact.title' => 'required|string|min:1|max:255',
            'answers' => ['required', 'array', "size:$answerSize", function ($attribute, $answers, $fail) {
                foreach ($answers as $a) {
                    if (!isset($a['answer']) && !isset($a['answerText'])) {
                        $fail('Cevaplar girilmelidir.');
                    }
                }
            }],
            'answers.*.questionId' => 'required|distinct|exists:company_inosuit_application_questions,id',
            'answers.*.answer' => 'nullable|integer|min:1|max:5',
            'answers.*.answerText' => 'nullable|string|max:3000',
        ];
    }

    public function attributes(): array
    {
        return [
            'sectorId' => 'Firmanızın sektörü',
            'associationId' => 'Destek almak istediğiniz ihracatçı birliği',
            'periodId' => 'Dönem',
            'name' => 'Firma Unvan',
            'taxNumber' => 'Vergi Numarası',
            'email' => 'Firma E-posta',
            'phone' => 'Firma Telefon',
            'website' => 'Firma Website',
            'address' => 'Firma adres',
            'activity' => 'Firma faaliyet alanları',
            'foundDate' => 'Firma kuruluş tarihi',
            'whiteCollarCount' => 'Beyaz yaka çalışan sayısı',
            'blueCollarCount' => 'Mavi yaka çalışan sayısı',
            'annualSale' => 'Yıllık net satış hasılatı',
            'balanceSheet' => 'Yıllık bilanço toplamı',
            'isKosgeb' => 'Kosgeb üyesi',
            'isArge' => 'Arge merkezi',

            'manager' => 'Yetkili bilgileri',
            'manager.fullName' => 'Yetkili Ad soyad',
            'manager.email' => 'Yetkili E-posta',
            'manager.phone' => 'Yetkili Telefon',
            'manager.title' => 'Yetkili Unvan',

            'contact' => 'İletişim sorumlusu bilgileri',
            'contact.fullName' => 'İletişim sorumlusu ad soyad',
            'contact.email' => 'İletişim sorumlusu e-posta',
            'contact.phone' => 'İletişim sorumlusu telefon',
            'contact.title' => 'İletişim sorumlusu unvan',

            'answers' => 'Değerlendirme soruları',
            'answers.*.questionId' => 'Değerlendirme sorusu',
            'answers.*.answer' => 'Değerlendirme cevabı',
            'answers.*.answerText' => 'Değerlendirme metin cevabı',
        ];
    }
}
