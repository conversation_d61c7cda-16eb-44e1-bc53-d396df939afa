<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/inosuit/companies/current-period",
 *   description="Update company current period",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update company current period",
 *     @OA\JsonContent(
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="title", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update company current period"
 *   ),
 * )
 */
class UpdateCurrentPeriodRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'fullName' => 'string|min:1|max:255',
            'email' => 'email|min:1|max:255',
            'phone' => 'string|min:1|max:30',
            'title' => 'string|min:1|max:255',
        ];
    }
}
