<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/companies/papers/detail",
 *   description="Get paper detail",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="paperId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get paper detail"
 *   ),
 * )
 */
class GetPaperDetailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'paperId' => 'required|integer',
        ];
    }
}
