<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Enums\Inosuit\CompanyPaperAnswerEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\CompanyPaper;
use App\Models\CompanyPeriod;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/inosuit/companies/papers",
 *   description="Create paper",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create paper",
 *     @OA\JsonContent(
 *       required={"termDate","meetingIds","answers"},
 *       @OA\Property(property="observation", type="string"),
 *       @OA\Property(property="termDate", type="string", example="2024-10-01"),
 *       @OA\Property(
 *         property="meetingIds",
 *         type="array",
 *         @OA\Items(type="integer"),
 *       ),
 *       @OA\Property(
 *         property="answers",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"questionId","answerType"},
 *           properties={
 *             @OA\Property(property="questionId", type="integer"),
 *             @OA\Property(property="answerType", type="integer"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create paper"
 *   ),
 * )
 */
class CreatePaperRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();
        $this->merge(['currentPeriod' => $currentPeriod]);

        $paperCount = CompanyPaper::query()
            ->where('company_period_id', $currentPeriod->id)
            ->where('term_date', $this->termDate)
            ->count();
        if ($paperCount > 0) {
            throw new BadRequestHttpException('İlgili dönemde raporunuz bulunmaktadır.');
        }

        return [
            'observation' => 'string|min:1|max:5000',
            'termDate' => 'required|date',
            'meetingIds' => 'required|array',
            'meetingIds.*' => 'required|integer|distinct|exists:company_meetings,id,company_period_id,' . $currentPeriod->id . ',deleted_at,NULL',
            'answers' => 'required|array|size:4',
            'answers.*.questionId' => 'required|integer|distinct|exists:company_paper_questions,id',
            'answers.*.answerType' => 'required|integer|in:' . implode(',', CompanyPaperAnswerEnum::values()),
        ];
    }
}
