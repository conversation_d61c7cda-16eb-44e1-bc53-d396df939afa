<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Enums\Inosuit\CompanyMeetingStatusEnum;
use App\Enums\Inosuit\CompanyMeetingTypeEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\CompanyMeeting;
use App\Models\CompanyPeriod;
use Illuminate\Validation\Rule;

/**
 * @OA\Put(
 *   path="/api/inosuit/companies/meetings",
 *   description="Update meeting",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update meeting",
 *     @OA\JsonContent(
 *       required={"meetingId"},
 *       @OA\Property(property="meetingId", type="integer"),
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="location", type="string"),
 *       @OA\Property(property="locationType", type="integer"),
 *       @OA\Property(property="companyStatus", type="integer"),
 *       @OA\Property(property="companyNote", type="string"),
 *       @OA\Property(property="startDate", type="string", example="2024-10-25 10:00:00"),
 *       @OA\Property(property="endDate", type="string", example="2024-10-25 12:00:00"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update meeting"
 *   ),
 * )
 */
class UpdateMeetingRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        CompanyMeeting::query()
            ->where('id', $this->meetingId)
            ->where('company_period_id', CompanyPeriod::getCurrentPeriod()->id)
            ->firstOrFail();
        return [
            'meetingId' => 'required|integer',
            'name' => 'string|nullable|min:1|max:255',
            'description' => 'string|nullable|min:1|max:1000',
            'location' => 'string|nullable|min:1|max:255',
            'locationType' => 'integer|in:' . implode(',', CompanyMeetingTypeEnum::values()),
            'companyStatus' => ['integer', Rule::in(CompanyMeetingStatusEnum::DECLINED->value, CompanyMeetingStatusEnum::APPROVED->value)],
            'companyNote' => 'string|nullable|min:1|max:1000',
            'startDate' => 'date',
            'endDate' => 'date',
        ];
    }
}
