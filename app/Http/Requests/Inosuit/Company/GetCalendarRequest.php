<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/companies/calendars",
 *   description="Get calendars",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="periodId",
 *     in="query",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get calendars"
 *   ),
 * )
 */
class GetCalendarRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'periodId' => 'integer|exists:periods,id',
        ];
    }
}
