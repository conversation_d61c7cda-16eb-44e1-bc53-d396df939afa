<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/companies/profile",
 *   description="Get company profile",
 *   tags={"inosuit/companies"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get company profile"
 *   ),
 * )
 */
class GetProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
