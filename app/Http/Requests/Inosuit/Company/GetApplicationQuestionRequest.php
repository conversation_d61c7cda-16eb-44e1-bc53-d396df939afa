<?php

namespace App\Http\Requests\Inosuit\Company;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/inosuit/companies/applications/questions",
 *   description="Get application questions",
 *   tags={"inosuit/companies"},
 *   @OA\Response(
 *     response=200,
 *     description="Get application questions"
 *   ),
 * )
 */
class GetApplicationQuestionRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
