<?php

namespace App\Http\Requests\Authorization;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/authorizations/employees",
 *   description="Get employees",
 *   tags={"authorizations"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get employees"
 *   ),
 * )
 */
class GetEmployeeRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
