<?php

namespace App\Http\Requests\Authorization;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/authorizations/employees/upload",
 *   description="Upload employees excel",
 *   tags={"authorizations"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Upload employees excel",
 *     @OA\MediaType(
 *       mediaType="multipart/form-data",
 *       @OA\Schema(
 *         required={"excel"},
 *         @OA\Property(
 *           property="excel",
 *           type="string",
 *           format="binary"
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Upload employees excel"
 *   ),
 * )
 */
class UploadEmployeeExcelRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'excel' => "required|file|mimes:xlsx|max:10240", // 10 MB
        ];
    }
}
