<?php

namespace App\Http\Requests\Authorization;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/authorizations/employees/{id}",
 *   description="Delete employees",
 *   tags={"authorizations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Employee id (user_id)",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete employees"
 *   ),
 * )
 */
class DeleteEmployeeRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
