<?php

namespace App\Http\Requests\Authorization;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/authorizations/roles",
 *   description="Get roles",
 *   tags={"authorizations"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get roles"
 *   ),
 * )
 */
class GetRoleRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
