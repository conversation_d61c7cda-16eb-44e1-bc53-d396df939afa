<?php

namespace App\Http\Requests\Authorization;

use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/authorizations/employees",
 *   description="Create employees",
 *   tags={"authorizations"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create employees",
 *     @OA\JsonContent(
 *       required={"fullName","email","title","roleId"},
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="title", type="string"),
 *       @OA\Property(property="roleId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create employees"
 *   ),
 * )
 */
class CreateEmployeeRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        if (strstr(auth()->user()->email, '@') != strstr($this->email, '@')) {
            throw new BadRequestHttpException('Sadece kendi şirketinize ait e-posta adresi ekleyebilirsiniz.');
        }
        return [
            'fullName' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users', 'email'), Rule::unique('user_pendings', 'email')->whereNull('deleted_at')],
            'title' => 'required|string|max:255',
            'roleId' => 'required|integer|exists:roles,id',
        ];
    }
}
