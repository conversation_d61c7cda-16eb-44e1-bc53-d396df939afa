<?php

namespace App\Http\Requests\Authorization;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/authorizations/employees/{id}",
 *   description="Update employees",
 *   tags={"authorizations"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Employee id (user_id)",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update employees",
 *     @OA\JsonContent(
 *       required={"fullName","title","roleId"},
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="title", type="string"),
 *       @OA\Property(property="roleId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update employees"
 *   ),
 * )
 */
class UpdateEmployeeRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'fullName' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'roleId' => 'required|integer|exists:roles,id',
        ];
    }
}
