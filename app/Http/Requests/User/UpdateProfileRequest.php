<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;

/**
 * @OA\Put(
 *   path="/api/users/profile",
 *   description="Update user profile",
 *   tags={"users"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update user profile",
 *     @OA\JsonContent(
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="title", type="string"),
 *       @OA\Property(property="photo", type="string"),
 *       @OA\Property(property="kvkkApproved", type="boolean"),
 *       @OA\Property(property="clarificationText", type="boolean"),
 *       @OA\Property(property="privacyPolicy", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update user profile"
 *   ),
 * )
 */
class UpdateProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'fullName' => 'string|max:255',
            'phone' => 'nullable|string|max:20',
            'title' => 'string|max:255',
            'photo' => 'nullable|url|max:255',
            'kvkkApproved' => 'boolean',
            'clarificationText' => 'boolean',
            'privacyPolicy' => 'boolean',
        ];
    }
}
