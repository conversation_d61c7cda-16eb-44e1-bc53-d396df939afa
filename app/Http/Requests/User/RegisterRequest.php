<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;
use App\Enums\RegisterEnum;
use App\Models\Config;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/users/register",
 *   description="Register user",
 *   tags={"users"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Register user",
 *     @OA\JsonContent(
 *       required={"fullName","email","phone","title","password","companyName","companyPersonnelCount","companySectorId","companyTaxNumber","registerTypeId"},
 *       @OA\Property(property="fullName", type="string"),
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="phone", type="string"),
 *       @OA\Property(property="title", type="string"),
 *       @OA\Property(property="password", type="string"),
 *       @OA\Property(property="companyName", type="string"),
 *       @OA\Property(property="companyPersonnelNumberId", type="integer"),
 *       @OA\Property(property="companySectorId", type="integer"),
 *       @OA\Property(property="companyTaxNumber", type="string"),
 *       @OA\Property(property="registerTypeId", type="integer"),
 *       @OA\Property(property="kvkkApproved", type="boolean"),
 *       @OA\Property(property="clarificationText", type="boolean"),
 *       @OA\Property(property="privacyPolicy", type="boolean"),
 *       @OA\Property(property="secondUser", type="boolean"),
 *       @OA\Property(property="fullName2", type="string"),
 *       @OA\Property(property="email2", type="string"),
 *       @OA\Property(property="phone2", type="string"),
 *       @OA\Property(property="title2", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Register user"
 *   ),
 * )
 */
class RegisterRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $registerConfig = Config::query()->where('key', '=', 'register_type_id')->firstOrFail();
        if ($registerConfig->value != $this->request->get('registerTypeId')) {
            throw new BadRequestHttpException('Invalid register type');
        }
        $applicationRegister = RegisterEnum::APPLICATION == RegisterEnum::tryFrom($this->request->get('registerTypeId'));
        $secondUserIsRequired = $applicationRegister && $this->request->get('secondUser');

        return [
            'fullName' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users', 'email'), Rule::unique('user_pendings', 'email')->whereNull('deleted_at')],
            'phone' => 'required|string|max:20',
            'title' => 'required|string|max:255',
            'password' => [Rule::requiredIf(!$applicationRegister), 'string', 'min:6', 'max:32'],
            'companyName' => 'required|string|max:255',
            'companyPersonnelNumberId' => 'required|integer|exists:company_personnel_numbers,id',
            'companySectorId' => 'required|integer|exists:company_sectors,id',
            'companyTaxNumber' => 'required|string|min:1|max:50',
            'registerTypeId' => ['required', 'integer', new Enum(RegisterEnum::class)],
            'kvkkApproved' => 'required|boolean|in:1',
            'clarificationText' => 'required|boolean|in:1',
            'privacyPolicy' => 'required|boolean|in:1',

            'secondUser' => 'boolean',
            'fullName2' => [Rule::requiredIf($secondUserIsRequired), 'string', 'max:255'],
            'email2' => [
                Rule::requiredIf($secondUserIsRequired),
                Rule::notIn($this->request->get('email')), 'string', 'email', 'max:255',
                Rule::unique('users', 'email'),
                Rule::unique('user_pendings', 'email')->whereNull('deleted_at')
            ],
            'phone2' => [Rule::requiredIf($secondUserIsRequired), 'string', 'max:20'],
            'title2' => [Rule::requiredIf($secondUserIsRequired), 'string', 'max:255'],
        ];
    }

    public function messages()
    {
        return [
            'email2.not_in' => 'Ana yetkili email adresinden farklı bir email giriniz',
        ];
    }
}
