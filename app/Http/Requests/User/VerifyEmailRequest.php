<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/users/verify-email",
 *   description="Verify email",
 *   tags={"users"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Verify email",
 *     @OA\JsonContent(
 *       required={"token"},
 *       @OA\Property(property="token", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Verify email"
 *   ),
 * )
 */
class VerifyEmailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'token' => 'required|string|max:255',
        ];
    }
}
