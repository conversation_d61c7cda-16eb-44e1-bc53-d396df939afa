<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/users/profile/photo",
 *   description="Delete user profile photo",
 *   tags={"users"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Delete user profile photo"
 *   ),
 * )
 */
class DeleteProfilePhotoRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
