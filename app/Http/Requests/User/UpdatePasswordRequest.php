<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;
use Illuminate\Validation\Rule;

/**
 * @OA\Put(
 *   path="/api/users/password",
 *   description="Update user password",
 *   tags={"users"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update user password",
 *     @OA\JsonContent(
 *       required={"oldPassword","newPassword"},
 *       @OA\Property(property="oldPassword", type="string"),
 *       @OA\Property(property="newPassword", type="string"),
 *       @OA\Property(property="kvkkApproved", type="boolean"),
 *       @OA\Property(property="clarificationText", type="boolean"),
 *       @OA\Property(property="privacyPolicy", type="boolean"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update user password"
 *   ),
 * )
 */
class UpdatePasswordRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $user = auth()->user();
        return [
            'oldPassword' => [Rule::requiredIf($user->is_approved_password), 'string', 'min:6', 'max:32'],
            'newPassword' => 'required|string|min:6|max:32',
            'kvkkApproved' => [Rule::requiredIf(!$user->kvkk_approved), 'boolean', 'in:1'],
            'clarificationText' => [Rule::requiredIf(!$user->clarification_text), 'boolean', 'in:1'],
            'privacyPolicy' => [Rule::requiredIf(!$user->privacy_policy), 'boolean', 'in:1'],
        ];
    }
}
