<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/users/forgot-password/reset-password",
 *   description="Forgot password - reset password",
 *   tags={"users"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Forgot password - reset password",
 *     @OA\JsonContent(
 *       required={"token","password"},
 *       @OA\Property(property="token", type="string"),
 *       @OA\Property(property="password", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Forgot password - reset password"
 *   ),
 * )
 */
class ResetPasswordRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'token' => 'required|string|max:255',
            'password' => 'required|string|max:255',
        ];
    }
}
