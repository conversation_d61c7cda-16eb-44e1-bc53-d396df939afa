<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/users/profile",
 *   description="Get user profile",
 *   tags={"users"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get user profile"
 *   ),
 * )
 */
class GetProfileRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
