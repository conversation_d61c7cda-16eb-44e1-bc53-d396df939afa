<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/users/logout",
 *   description="Logout user",
 *   tags={"users"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Logout user"
 *   ),
 * )
 */
class LogoutRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
