<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/users/login",
 *   description="Login user",
 *   tags={"users"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Login user",
 *     @OA\JsonContent(
 *       required={"email","password"},
 *       @OA\Property(property="email", type="string"),
 *       @OA\Property(property="password", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Login user"
 *   ),
 * )
 */
class LoginRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|max:255',
        ];
    }
}
