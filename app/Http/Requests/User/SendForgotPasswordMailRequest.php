<?php

namespace App\Http\Requests\User;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/users/forgot-password/send-mail",
 *   description="Forgot password - send mail",
 *   tags={"users"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Forgot password - send mail",
 *     @OA\JsonContent(
 *       required={"email"},
 *       @OA\Property(property="email", type="string"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Forgot password - send mail"
 *   ),
 * )
 */
class SendForgotPasswordMailRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|string|email|max:255',
        ];
    }
}
