<?php

namespace App\Http\Requests\Facility;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/facilities/{id}",
 *   description="Delete facilities",
 *   tags={"facilities"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Facility id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete facilities"
 *   ),
 * )
 */
class DeleteFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
