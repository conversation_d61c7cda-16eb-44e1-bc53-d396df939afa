<?php

namespace App\Http\Requests\Facility;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/facilities",
 *   description="Create facilities",
 *   tags={"facilities"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create facilities",
 *     @OA\JsonContent(
 *       required={"name","address","specieId"},
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="address", type="string"),
 *       @OA\Property(property="specieId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create facilities"
 *   ),
 * )
 */
class CreateFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:1000',
            'specieId' => 'required|integer|exists:facility_species,id',
        ];
    }
}
