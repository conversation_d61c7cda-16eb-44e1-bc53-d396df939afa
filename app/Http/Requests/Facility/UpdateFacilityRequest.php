<?php

namespace App\Http\Requests\Facility;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Put(
 *   path="/api/facilities/{id}",
 *   description="Update facilities",
 *   tags={"facilities"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Facility id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\RequestBody(
 *     required=true,
 *     description="Update facilities",
 *     @OA\JsonContent(
 *       required={"name","address","specieId"},
 *       @OA\Property(property="name", type="string"),
 *       @OA\Property(property="address", type="string"),
 *       @OA\Property(property="specieId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Update facilities"
 *   ),
 * )
 */
class UpdateFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'specieId' => 'required|integer|exists:facility_species,id',
        ];
    }
}
