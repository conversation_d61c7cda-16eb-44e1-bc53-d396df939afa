<?php

namespace App\Http\Requests\Facility;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/facilities/species",
 *   description="Get facility species",
 *   tags={"facilities"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get facility species"
 *   ),
 * )
 */
class GetSpecieRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
