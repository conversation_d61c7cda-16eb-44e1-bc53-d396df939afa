<?php

namespace App\Http\Requests\Facility;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/facilities",
 *   description="Get facilities",
 *   tags={"facilities"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get facilities"
 *   ),
 * )
 */
class GetFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
