<?php

namespace App\Http\Requests\GreenTransformation;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/green-transformations",
 *   description="Green transformation",
 *   tags={"green-transformations"},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Green transformation",
 *     @OA\JsonContent(
 *       type="array",
 *       @OA\Items(
 *         required={"sectorId","fullName","email","title","phone","companyName"},
 *         @OA\Property(property="sectorId", type="integer"),
 *         @OA\Property(property="fullName", type="string"),
 *         @OA\Property(property="email", type="string"),
 *         @OA\Property(property="title", type="string"),
 *         @OA\Property(property="phone", type="string"),
 *         @OA\Property(property="companyName", type="string")
 *       )
 *     )
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Green transformation",
 *   )
 * )
 */

class CreateGreenTransformationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            '*.sectorId' => 'required|integer|exists:company_sectors,id',
            '*.fullName' => 'required|string|max:255',
            '*.email' => 'required|string|email|max:255',
            '*.title' => 'required|string|max:255',
            '*.phone' => 'required|string|max:30',
            '*.companyName' => 'required|string|max:255',
        ];
    }
}
