<?php

namespace App\Http\Requests;

use App\Http\Responses\GeneralResponse;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

/**
 * @OA\Info(
 *   title="GreenTİM API",
 *   version="1.0.0"
 * )
 *
 * @OAS\SecurityScheme(
 *   securityScheme="bearer_token",
 *   type="http",
 *   scheme="bearer"
 * )
 */
class GeneralRequest extends FormRequest
{
    protected function failedValidation(Validator $validator)
    {
        $response = (new GeneralResponse(false))->setErrors($validator->errors())->toJson();
        throw new HttpResponseException($response);
    }
}
