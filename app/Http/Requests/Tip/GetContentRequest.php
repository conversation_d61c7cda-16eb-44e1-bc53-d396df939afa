<?php

namespace App\Http\Requests\Tip;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/tips/contents",
 *   description="Get tip contents",
 *   tags={"tips"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="frontSlug",
 *     in="query",
 *     description="Frontend slug",
 *     required=true,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="isRead",
 *     in="query",
 *     description="Is read",
 *     required=false,
 *     @OA\Schema(type="boolean"),
 *   ),
 *   @OA\Parameter(
 *     name="isPast",
 *     in="query",
 *     description="Is past",
 *     required=false,
 *     @OA\Schema(type="boolean"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get tip contents"
 *   ),
 * )
 */
class GetContentRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        if ($this->isRead === 'true') {
            $this->merge(['isRead' => true]);
        } else if ($this->isRead === 'false') {
            $this->merge(['isRead' => false]);
        }

        if ($this->isPast === 'true') {
            $this->merge(['isPast' => true]);
        } else if ($this->isPast === 'false') {
            $this->merge(['isPast' => false]);
        }

        return [
            'frontSlug' => 'required|string|max:255|exists:tip_contents,front_slug',
            'isRead' => 'boolean',
            'isPast' => 'boolean',
        ];
    }
}
