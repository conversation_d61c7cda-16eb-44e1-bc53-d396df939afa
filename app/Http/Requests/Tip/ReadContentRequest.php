<?php

namespace App\Http\Requests\Tip;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Post(
 *   path="/api/tips/contents/read",
 *   description="Read tip contents",
 *   tags={"tips"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Read tip contents",
 *     @OA\JsonContent(
 *       required={"contentId"},
 *       @OA\Property(property="contentId", type="integer"),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Read tip contents"
 *   ),
 * )
 */
class ReadContentRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'contentId' => 'required|integer|exists:tip_contents,id',
        ];
    }
}
