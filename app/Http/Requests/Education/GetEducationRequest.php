<?php

namespace App\Http\Requests\Education;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/educations",
 *   description="Get educations",
 *   tags={"educations"},
 *   @OA\Response(
 *     response=200,
 *     description="Get educations"
 *   ),
 * )
 */
class GetEducationRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
