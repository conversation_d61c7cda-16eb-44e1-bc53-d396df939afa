<?php

namespace App\Http\Requests\Uncertainty;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/uncertainties",
 *   description="Get uncertainty",
 *   tags={"uncertainties"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="groupValueId",
 *     in="query",
 *     required=true,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get uncertainty"
 *   ),
 * )
 */
class GetUncertaintyRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'required|integer|exists:forms,id',
            'groupValueId' => 'required|integer|exists:form_group_values,id',
        ];
    }
}
