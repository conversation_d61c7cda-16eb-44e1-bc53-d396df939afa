<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/group-by/facilities/pdf",
 *   description="Returns years and reports with at least 2 facilities for PDF",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Returns years and reports with at least 2 facilities for PDF"
 *   ),
 * )
 */
class GetReportGroupByFacilityForPDFRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
