<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/group-by/years/pdf",
 *   description="Get reports group by facilitiesfor PDF",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get reports group by facilities for PDF"
 *   ),
 * )
 */
class GetReportGroupByYearForPDFRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
