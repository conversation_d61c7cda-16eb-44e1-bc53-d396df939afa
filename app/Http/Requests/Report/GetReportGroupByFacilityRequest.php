<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/group-by/facilities",
 *   description="Returns years and reports with at least 2 facilities",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="year",
 *     in="query",
 *     description="Year",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="formIds",
 *     in="query",
 *     description="Form ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Returns years and reports with at least 2 facilities"
 *   ),
 * )
 */
class GetReportGroupByFacilityRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'year' => 'integer',
            'formIds' => 'string|max:255',
        ];
    }
}
