<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Delete(
 *   path="/api/reports/{id}",
 *   description="Delete report",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="id",
 *     in="path",
 *     description="Report id",
 *     required=true,
 *     @OA\Schema(
 *       type="integer"
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Delete report"
 *   ),
 * )
 */
class DeleteReportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
