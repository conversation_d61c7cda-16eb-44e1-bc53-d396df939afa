<?php

namespace App\Http\Requests\Report;

use App\Enums\MonthEnum;
use App\Enums\UncertaintyElectricityClassTypeEnum;
use App\Enums\UncertaintyElectricityMeterTypeEnum;
use App\Enums\UncertaintyElectricityTemperatureTypeEnum;
use App\Enums\UncertaintyPedigreeTabEnum;
use App\Http\Requests\GeneralRequest;
use App\Models\FileManager;
use App\Models\Form;
use App\Utils\GeneralUtil;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * @OA\Post(
 *   path="/api/reports",
 *   description="Create report",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\RequestBody(
 *     required=true,
 *     description="Create report",
 *     @OA\JsonContent(
 *       required={"formId","facilityId","year","monthIds","amount"},
 *       @OA\Property(property="formId", type="integer"),
 *       @OA\Property(property="facilityId", type="integer"),
 *       @OA\Property(property="resultId", type="integer"),
 *       @OA\Property(property="year", type="integer"),
 *       @OA\Property(property="monthIds", type="array", @OA\Items(type="integer")),
 *       @OA\Property(property="amount", type="double"),
 *       @OA\Property(property="description", type="string"),
 *       @OA\Property(property="fileIds", type="array", @OA\Items(type="integer")),
 *       @OA\Property(property="errorMargin",type="double"),
 *       @OA\Property(property="isCalibration",type="boolean"),
 *       @OA\Property(property="uncertaintyValue",type="double"),
 *       @OA\Property(
 *         property="pedigreeTabs",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"tabType", "value"},
 *           properties={
 *             @OA\Property(property="tabType", type="integer"),
 *             @OA\Property(property="value", type="integer"),
 *           }
 *         ),
 *       ),
 *       @OA\Property(
 *         property="electricity",
 *         type="object",
 *         required={"meterType","classType","temperatureType","transferCurrent","maxCurrent","expectedCurrent"},
 *         properties={
 *           @OA\Property(property="meterType", type="integer"),
 *           @OA\Property(property="classType", type="integer"),
 *           @OA\Property(property="temperatureType", type="integer"),
 *           @OA\Property(property="minCurrent", type="double"),
 *           @OA\Property(property="transferCurrent", type="double"),
 *           @OA\Property(property="maxCurrent", type="double"),
 *           @OA\Property(property="expectedCurrent", type="double"),
 *         }
 *       ),
 *       @OA\Property(
 *         property="payload",
 *         type="array",
 *         @OA\Items(
 *           type="object",
 *           required={"inputId"},
 *           properties={
 *             @OA\Property(property="inputId", type="integer"),
 *             @OA\Property(property="groupValueId", type="integer"),
 *             @OA\Property(property="numericValue", type="double"),
 *             @OA\Property(property="textValue", type="string"),
 *           }
 *         ),
 *       ),
 *     ),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Create report"
 *   ),
 * )
 */
class CreateReportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $years = GeneralUtil::getYearsAsArray();
        $formId = $this->request->get('formId');
        $commonFormIds = Form::with('commons')->findOrFail($formId)->commons->map(fn($c) => $c->common_form_id)->unique()->toArray();
        $formIds = [$formId, ...$commonFormIds];

        if (!request()->resultId && !request()->payload) {
            throw new BadRequestHttpException('either resultId or payload is required');
        } else if (request()->resultId && request()->payload) {
            throw new BadRequestHttpException('cannot both resultId and payload');
        }

        if (request()->payload) {
            foreach (request()->payload as $p) {
                if (!isset($p['groupValueId']) && !isset($p['numericValue']) && !isset($p['textValue'])) {
                    throw new BadRequestHttpException('either groupValueId or numericValue or textValue is required');
                }
            }
        }

        if (request()->fileIds) {
            $fileManagerCount = FileManager::query()
                ->whereIn('id', array_unique(request()->fileIds))
                ->where('user_id', auth()->id())
                ->whereNull('report_id')
                ->count();
            if ($fileManagerCount !== count(request()->fileIds)) {
                throw new BadRequestHttpException('fileIds is invalid');
            }
        }

        return [
            'formId' => 'required|integer|exists:forms,id',
            'facilityId' => 'required|integer|exists:facilities,id,company_id,' . auth()->user()->company_id,
            'resultId' => [Rule::requiredIf(!request()->payload), 'integer', Rule::exists('form_results', 'id')->whereIn('form_id', $formIds)],
            'year' => ['required', 'integer', Rule::in($years)],
            'monthIds' => 'required|min:1|max:12',
            'monthIds.*' => ['required', 'integer', new Enum(MonthEnum::class)],
            'amount' => 'required|numeric|min:0.0001',
            'description' => 'nullable|string|min:1',
            'fileIds' => 'array|min:1|max:20',
            'fileIds.*' => 'required|integer',
            'errorMargin' => 'nullable|numeric|min:0.0001',
            'isCalibration' => 'nullable|boolean',
            'uncertaintyValue' => 'nullable|numeric|min:0.0001',

            'pedigreeTabs' => 'nullable|array|size:5',
            'pedigreeTabs.*.tabType' => ['required_with:pedigreeTabs', 'integer', 'distinct', new Enum(UncertaintyPedigreeTabEnum::class)],
            'pedigreeTabs.*.value' => 'required_with:pedigreeTabs|integer|min:1|max:5',

            'electricity' => 'nullable|array',
            'electricity.meterType' => ['required_with:electricity', 'integer', new Enum(UncertaintyElectricityMeterTypeEnum::class)],
            'electricity.classType' => ['required_with:electricity', 'integer', new Enum(UncertaintyElectricityClassTypeEnum::class)],
            'electricity.temperatureType' => ['required_with:electricity', 'integer', new Enum(UncertaintyElectricityTemperatureTypeEnum::class)],
            'electricity.minCurrent' => 'nullable|numeric|min:0.0001',
            'electricity.transferCurrent' => 'required_with:electricity|numeric|min:0.0001',
            'electricity.maxCurrent' => 'required_with:electricity|numeric|min:0.0001',
            'electricity.expectedCurrent' => 'required_with:electricity|numeric|min:0.0001',

            'payload' => [Rule::requiredIf(!request()->resultId), 'array', 'min:1', 'max:50'],
            'payload.*.inputId' => 'required|integer|exists:form_inputs,id,form_id,' . $this->request->get('formId'),
            'payload.*.groupValueId' => 'integer|exists:form_group_values,id',
            'payload.*.numericValue' => 'numeric|min:0.0001',
            'payload.*.textValue' => 'string|min:1|max:255',
        ];
    }
}
