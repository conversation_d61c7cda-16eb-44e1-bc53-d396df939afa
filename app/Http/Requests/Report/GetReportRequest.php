<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports",
 *   description="Get reports",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     description="Form id",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="formSlug",
 *     in="query",
 *     description="Form slug",
 *     required=false,
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Parameter(
 *     name="facilityIds",
 *     in="query",
 *     description="Facility ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Parameter(
 *     name="years",
 *     in="query",
 *     description="Years",
 *     required=false,
 *     @OA\Schema(type="string", example="2020,2021"),
 *   ),
 *   @OA\Parameter(
 *     name="monthIds",
 *     in="query",
 *     description="Month ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get reports"
 *   ),
 * )
 */
class GetReportRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'integer|exists:forms,id',
            'formSlug' => 'string|exists:forms,slug',
            'facilityIds' => 'string|max:255',
            'years' => 'string|max:255',
            'monthIds' => 'string|max:255',
        ];
    }
}
