<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/categories-gases",
 *   description="Get categories gases",
 *   tags={"iso-standards"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="years",
 *     in="query",
 *     description="Years",
 *     required=false,
 *     @OA\Schema(type="string", example="2020,2021"),
 *   ),
 *   @OA\Parameter(
 *     name="facilityIds",
 *     in="query",
 *     description="Facility ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Parameter(
 *     name="formIds",
 *     in="query",
 *     description="Form ids",
 *     required=false,
 *     @OA\Schema(type="string", example="1,2,3"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get categories gases"
 *   ),
 * )
 */
class GetCategoriesGasRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'years' => 'string|max:255',
            'facilityIds' => 'string|max:255',
            'formIds' => 'string|max:255',
        ];
    }
}
