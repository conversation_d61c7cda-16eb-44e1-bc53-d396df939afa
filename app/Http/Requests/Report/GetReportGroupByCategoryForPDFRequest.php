<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/group-by/categories/pdf",
 *   description="Get reports group by categories for PDF",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Response(
 *     response=200,
 *     description="Get reports group by categories for PDF"
 *   ),
 * )
 */
class GetReportGroupByCategoryForPDFRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [];
    }
}
