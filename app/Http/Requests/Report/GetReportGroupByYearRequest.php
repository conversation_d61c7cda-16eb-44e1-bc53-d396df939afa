<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/group-by/years",
 *   description="Get reports group by facilities",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     description="Form id",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="facilityId",
 *     in="query",
 *     description="Facility id",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get reports group by facilities"
 *   ),
 * )
 */
class GetReportGroupByYearRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'integer|exists:forms,id',
            'facilityId' => 'integer|exists:facilities,id,company_id,' . auth()->user()->company_id,
        ];
    }
}
