<?php

namespace App\Http\Requests\Report;

use App\Http\Requests\GeneralRequest;

/**
 * @OA\Get(
 *   path="/api/reports/years",
 *   description="Get reports years",
 *   tags={"reports"},
 *   security={{"passport":{}}},
 *   @OA\Parameter(
 *     name="formId",
 *     in="query",
 *     description="Form id",
 *     required=false,
 *     @OA\Schema(type="integer"),
 *   ),
 *   @OA\Parameter(
 *     name="facilityIds",
 *     in="query",
 *     description="Facility ids",
 *     required=false,
 *     example="1,2,3",
 *     @OA\Schema(type="string"),
 *   ),
 *   @OA\Response(
 *     response=200,
 *     description="Get reports years"
 *   ),
 * )
 */
class GetReportYearRequest extends GeneralRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'formId' => 'integer|exists:forms,id',
            'facilityIds' => 'string|max:255',
        ];
    }
}
