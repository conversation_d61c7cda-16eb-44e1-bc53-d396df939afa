<?php

namespace App\Http\Responses;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\MessageBag;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class GeneralResponse
{
    public bool $status;
    public int $totalCount;
    public Collection|Model|array $data;
    public MessageBag $errors;
    public array $messages;
    private int $code;

    public function __construct(bool $status = true, int $code = ResponseAlias::HTTP_OK)
    {
        $this->status = $status;
        $this->code = $code;
    }

    public function toJson(): JsonResponse
    {
        return response()->json($this, $this->code);
    }

    public function setTotalCount(int $totalCount): self
    {
        $this->totalCount = $totalCount;
        return $this;
    }

    public function setData(Collection|Model|array $data): self
    {
        $this->data = $data;
        return $this;
    }

    public function setErrors(MessageBag $errors): self
    {
        $this->errors = $errors;
        return $this;
    }

    public function setMessages(...$messages): self
    {
        $this->messages = $messages;
        return $this;
    }
}
