<?php

namespace App\Http\Services;

use App\Enums\ClassificationGassesEnum;
use App\Http\Requests\IsoStandard\GetCategoriesGasRequest;
use App\Models\Form;
use App\Models\FormCategory;
use App\Models\Report;
use App\Utils\GeneralUtil;

class IsoStandardService
{
    public function getCategoriesGases(GetCategoriesGasRequest $request): array
    {
        $extraGasses = ClassificationGassesEnum::getExtraGasses();
        $categories = FormCategory::with('languages', 'forms')->orderBy('order')->get();
        $reports = Report::with('form', 'result.values')
            ->where('company_id', auth()->user()->company_id)
            ->orderBy('year');
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        FormCategory::addLangProperties($categories);
        Form::addLangProperties($categories, 'forms');
        Report::mapResults($reports, true);
        Report::mapExtraGasses($reports);

        $categories->push((object)['id' => 0, 'name' => 'Toplam']);
        $result = [];
        foreach ($categories as $c) {
            $result[$c->id] = [
                'category_id' => $c->id,
                'category_name' => $c->name,
                'greenhouse_gas' => 0,
                'carbon_dioxide' => 0,
                'nitrous_oxide' => 0,
                'methane' => 0,
            ];
            foreach ($extraGasses as $extraGas) {
                $result[$c->id][$extraGas] = 0;
            }
            $forms = [];
            if (isset($c->forms)) {
                foreach ($c->forms as $form) {
                    $formData = [
                        'form_id' => $form->id,
                        'form_name' => $form->name,
                        'greenhouse_gas' => 0,
                        'carbon_dioxide' => 0,
                        'nitrous_oxide' => 0,
                        'methane' => 0,
                    ];
                    foreach ($extraGasses as $extraGas) {
                        $formData[$extraGas] = 0;
                    }
                    $forms[] = $formData;
                }
            }
            $result[$c->id]['forms'] = $forms;
        }
        foreach ($reports->groupBy('form.category_id') as $categoryId => $groupedReports) {
            $groupedReports = collect($groupedReports);
            $greenhouseGas = $groupedReports->sum(fn($r) => $r->result->greenhouse_gas_double);
            $carbonDioxide = $groupedReports->sum(fn($r) => $r->result->carbon_dioxide_double);
            $nitrousOxide = $groupedReports->sum(fn($r) => $r->result->nitrous_oxide_double);
            $methane = $groupedReports->sum(fn($r) => $r->result->methane_double);

            foreach ($extraGasses as $extraGas) {
                $result[$categoryId][$extraGas] = $groupedReports->filter(fn($r) => isset($r->result->$extraGas))->sum(fn($r) => $r->result->$extraGas);
            }
            $result[$categoryId]['greenhouse_gas'] = $greenhouseGas;
            $result[$categoryId]['carbon_dioxide'] = $carbonDioxide;
            $result[$categoryId]['nitrous_oxide'] = $nitrousOxide;
            $result[$categoryId]['methane'] = $methane;

            foreach ($result[$categoryId]['forms'] as &$form) {
                $formReports = $groupedReports->filter(fn($r) => $r->form_id == $form['form_id']);
                $form['greenhouse_gas'] = $formReports->sum(fn($r) => $r->result->greenhouse_gas_double);
                $form['carbon_dioxide'] = $formReports->sum(fn($r) => $r->result->carbon_dioxide_double);
                $form['nitrous_oxide'] = $formReports->sum(fn($r) => $r->result->nitrous_oxide_double);
                $form['methane'] = $formReports->sum(fn($r) => $r->result->methane_double);
                foreach ($extraGasses as $extraGas) {
                    $form[$extraGas] += $formReports->filter(fn($r) => isset($r->result->$extraGas))->sum(fn($r) => $r->result->$extraGas);
                }
            }

            foreach ($extraGasses as $extraGas) {
                $result[0][$extraGas] += $groupedReports->filter(fn($r) => isset($r->result->$extraGas))->sum(fn($r) => $r->result->$extraGas);
            }
            $result[0]['greenhouse_gas'] += $greenhouseGas;
            $result[0]['carbon_dioxide'] += $carbonDioxide;
            $result[0]['nitrous_oxide'] += $nitrousOxide;
            $result[0]['methane'] += $methane;
        }
        $result['divider'] = [
            'type' => 'divider',
            'category_id' => null,
            'category_name' => 'Bölüm',
            'greenhouse_gas' => $result[0]['greenhouse_gas'] / $request->divider,
            'carbon_dioxide' => $result[0]['carbon_dioxide'] / $request->divider,
            'nitrous_oxide' => $result[0]['nitrous_oxide'] / $request->divider,
            'methane' => $result[0]['methane'] / $request->divider,
            'forms' => [],
        ];
        foreach ($extraGasses as $extraGas) {
            $result['divider'][$extraGas] = $result[0][$extraGas] / $request->divider;
        }
        foreach ($result as $i => &$r) {
            if ($r['greenhouse_gas'] == 0) {
                unset($result[$i]);
                continue;
            }
            $r['greenhouse_gas'] = GeneralUtil::numberFormat($r['greenhouse_gas']);
            $r['carbon_dioxide'] = GeneralUtil::numberFormat($r['carbon_dioxide']);
            $r['nitrous_oxide'] = GeneralUtil::numberFormat($r['nitrous_oxide']);
            $r['methane'] = GeneralUtil::numberFormat($r['methane']);
            foreach ($extraGasses as $extraGas) {
                $r[$extraGas] = GeneralUtil::numberFormat($r[$extraGas]);
            }
            foreach ($r['forms'] as $formIndex => &$form) {
                if ($form['greenhouse_gas'] == 0) {
                    unset($r['forms'][$formIndex]);
                    continue;
                }
                $form['greenhouse_gas'] = GeneralUtil::numberFormat($form['greenhouse_gas']);
                $form['carbon_dioxide'] = GeneralUtil::numberFormat($form['carbon_dioxide']);
                $form['nitrous_oxide'] = GeneralUtil::numberFormat($form['nitrous_oxide']);
                $form['methane'] = GeneralUtil::numberFormat($form['methane']);
                foreach ($extraGasses as $extraGas) {
                    $form[$extraGas] = GeneralUtil::numberFormat($form[$extraGas]);
                }
            }
            $r['forms'] = array_values($r['forms']);
        }
        return array_values($result);
    }
}
