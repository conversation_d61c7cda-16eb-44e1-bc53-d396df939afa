<?php

namespace App\Http\Services;

use App\Enums\UncertaintyElectricityMeterTypeEnum;
use App\Enums\UncertaintyPedigreeSourceEnum;
use App\Enums\UncertaintyTypeEnum;
use App\Models\Form;
use App\Models\FormUncertainty;
use App\Models\ReportUncertainty;
use App\Models\ReportUncertaintyElectricity;
use App\Models\ReportUncertaintyPedigree;
use App\Types\UncertaintyElectricityType;
use App\Types\UncertaintyPedigreeIndicatorType;
use App\Types\UncertaintyPedigreeSourceType;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class UncertaintyService
{
    public function upsertUncertaintyReport(int $reportId, Form $form, array $gasses, array $groupValueIds, float $errorMargin = null, bool $isCalibration = null, float $uncertaintyValue = null, array $pedigreeTabs = null, array $electricity = null): void
    {
        $uncertaintyValue = $this->calculateUncertainty($form, $gasses, $groupValueIds, $errorMargin, $isCalibration, $uncertaintyValue, $pedigreeTabs, $electricity);
        if (is_null($uncertaintyValue)) {
            return;
        }
        $reportUncertainty = ReportUncertainty::query()->where('report_id', $reportId)->first();
        if (!$reportUncertainty) {
            $reportUncertainty = new ReportUncertainty();
            $reportUncertainty->report_id = $reportId;
        }
        $reportUncertainty->uncertainty_value = $uncertaintyValue;
        $reportUncertainty->error_margin = $errorMargin;
        $reportUncertainty->is_calibration = $isCalibration;
        $reportUncertainty->save();

        if ($pedigreeTabs) {
            foreach ($pedigreeTabs as $pedigreeTab) {
                $reportUncertaintyPedigree = ReportUncertaintyPedigree::query()
                    ->where('report_uncertainty_id', $reportUncertainty->id)
                    ->where('tab_type', $pedigreeTab['tabType'])
                    ->first();
                if (!$reportUncertaintyPedigree) {
                    $reportUncertaintyPedigree = new ReportUncertaintyPedigree();
                    $reportUncertaintyPedigree->report_uncertainty_id = $reportUncertainty->id;
                }
                $reportUncertaintyPedigree->tab_type = $pedigreeTab['tabType'];
                $reportUncertaintyPedigree->value = $pedigreeTab['value'];
                $reportUncertaintyPedigree->save();
            }
        }
        if ($electricity) {
            $reportUncertaintyElectricity = ReportUncertaintyElectricity::query()
                ->where('report_uncertainty_id', $reportUncertainty->id)
                ->first();
            if (!$reportUncertaintyElectricity) {
                $reportUncertaintyElectricity = new ReportUncertaintyElectricity();
                $reportUncertaintyElectricity->report_uncertainty_id = $reportUncertainty->id;
            }
            $reportUncertaintyElectricity->meter_type = $electricity['meterType'];
            $reportUncertaintyElectricity->class_type = $electricity['classType'];
            $reportUncertaintyElectricity->temperature_type = $electricity['temperatureType'];
            $reportUncertaintyElectricity->min_current = $electricity['minCurrent'];
            $reportUncertaintyElectricity->transfer_current = $electricity['transferCurrent'];
            $reportUncertaintyElectricity->max_current = $electricity['maxCurrent'];
            $reportUncertaintyElectricity->expected_current = $electricity['expectedCurrent'];
            $reportUncertaintyElectricity->save();
        }
    }

    public function calculateUncertainty(Form $form, array $gasses, array $groupValueIds, float $errorMargin = null, bool $isCalibration = null, float $uncertaintyValue = null, array $pedigreeTabs = null, array $electricity = null): float|null
    {
        if (!$form->uncertainty_type) {
            return null;
        }
        if ($form->uncertainty_type == UncertaintyTypeEnum::PEDIGREE_CUSTOM->value && !is_null($isCalibration)) {
            return $this->calculatePedigreeCustom($isCalibration, $uncertaintyValue, $pedigreeTabs);
        }

        $formUncertainty = FormUncertainty::query()
            ->where('form_id', $form->id)
            ->whereIn('group_value_id', $groupValueIds)
            ->first();
        if (!$formUncertainty) {
            return null;
        }
        if (isset($formUncertainty->uncertainty['uncertaintyValue'])) {
            return $formUncertainty->uncertainty['uncertaintyValue'];
        }

        if ($form->uncertainty_type == UncertaintyTypeEnum::QUANTITATIVE->value && $errorMargin) {
            return $this->calculateQuantitative($formUncertainty, $gasses, $errorMargin);
        } else if ($form->uncertainty_type == UncertaintyTypeEnum::PEDIGREE->value && $pedigreeTabs) {
            return $this->calculatePedigree($formUncertainty, $pedigreeTabs);
        } else if ($form->uncertainty_type == UncertaintyTypeEnum::ELECTRICITY->value && $electricity) {
            return $this->calculateElectricity($formUncertainty, $electricity);
        }
        return null;
    }

    private function calculateQuantitative(FormUncertainty $formUncertainty, array $gasses, float $errorMargin): float|null
    {
        if (count($gasses) == 0) {
            return null;
        }
        $uncertainty = $formUncertainty->uncertainty;
        $fv = $errorMargin;

        $u_carbon_dioxide = sqrt(pow($uncertainty['EF_carbon_dioxide'], 2) + pow($uncertainty['GWP_carbon_dioxide'], 2) + pow($fv, 2) + pow($uncertainty['NKD'], 2) + pow($uncertainty['YD'], 2));
        $u_nitrous_oxide = sqrt(pow($uncertainty['EF_nitrous_oxide'], 2) + pow($uncertainty['GWP_nitrous_oxide'], 2) + pow($fv, 2) + pow($uncertainty['NKD'], 2) + pow($uncertainty['YD'], 2));
        $u_methane = sqrt(pow($uncertainty['EF_methane'], 2) + pow($uncertainty['GWP_methane'], 2) + pow($fv, 2) + pow($uncertainty['NKD'], 2) + pow($uncertainty['YD'], 2));

        $u_carbon_dioxide *= $gasses['carbon_dioxide'];
        $u_nitrous_oxide *= $gasses['nitrous_oxide'];
        $u_methane *= $gasses['methane'];

        $co2e = $gasses['carbon_dioxide'] + $gasses['nitrous_oxide'] + $gasses['methane'];
        $u_co2e = sqrt(pow($u_carbon_dioxide, 2) + pow($u_methane, 2) + pow($u_nitrous_oxide, 2)) / $co2e;
        return $u_co2e;
    }

    private function calculatePedigree(FormUncertainty $formUncertainty, array $pedigreeTabs): float
    {
        $sourceEnum = UncertaintyPedigreeSourceEnum::from($formUncertainty->uncertainty['sourceType']);
        $sourceType = UncertaintyPedigreeSourceType::$unitedValues[$sourceEnum->value];
        $sdg = $sourceType['sdg'];
        $unitedValue = $sourceType['united'];

        $indicators = UncertaintyPedigreeIndicatorType::$indicators;
        $totalTabValues = pow(log($sdg), 2);
        foreach ($pedigreeTabs as $pedigreeTab) {
            $value = $indicators[$pedigreeTab['tabType']][$pedigreeTab['value']];
            $totalTabValues += pow(log($value), 2);
        }
        $unitedTab = exp(sqrt($totalTabValues)) - 1;

        $uncertaintyValue = sqrt(pow($unitedValue, 2) + pow($unitedTab, 2));
        return $uncertaintyValue;
    }

    private function calculatePedigreeCustom(bool $isCalibration, float $uncertaintyValue = null, array $pedigreeTabs = null): float|null
    {
        if ($isCalibration) {
            return $uncertaintyValue;
        }
        if (!$pedigreeTabs) {
            return null;
        }

        $sdg = 1.05;
        $indicators = UncertaintyPedigreeIndicatorType::$indicators;
        $totalTabValues = pow(log($sdg), 2);
        foreach ($pedigreeTabs as $pedigreeTab) {
            $value = $indicators[$pedigreeTab['tabType']][$pedigreeTab['value']];
            $totalTabValues += pow(log($value), 2);
        }
        $uncertaintyValue = exp(sqrt($totalTabValues)) - 1;
        return $uncertaintyValue;
    }

    private function calculateElectricity(FormUncertainty $formUncertainty, array &$electricity): float|null
    {
        if ($electricity['meterType'] == UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_SINGLE_LOAD->value) {
            $electricity['minCurrent'] = null;
        } else {
            if (!$electricity['minCurrent']) {
                throw new BadRequestHttpException('Minimum akım değeri giriniz.');
            }
        }

        $sourceEnum = UncertaintyPedigreeSourceEnum::from($formUncertainty->uncertainty['sourceType']);
        $sourceType = UncertaintyPedigreeSourceType::$unitedValues[$sourceEnum->value];
        $unitedValue = $sourceType['united'];

        if ($electricity['minCurrent'] && $electricity['minCurrent'] <= $electricity['expectedCurrent'] && $electricity['expectedCurrent'] < $electricity['transferCurrent']) {
            $range = 'min';
        } else if ($electricity['transferCurrent'] <= $electricity['expectedCurrent'] && $electricity['expectedCurrent'] <= $electricity['maxCurrent']) {
            $range = 'max';
        } else {
            throw new BadRequestHttpException('Geçersiz akım aralığı.');
        }

        $electricityValue = UncertaintyElectricityType::$values[$electricity['classType']][$electricity['meterType']][$electricity['temperatureType']][$range];
        $uncertaintyValue = sqrt(pow($unitedValue, 2) + pow($electricityValue, 2));
        return $uncertaintyValue;
    }

    public function calculateReport(array $reports): float
    {
        $totalUi2 = 0;
        $totalCo2e = 0;
        foreach ($reports as $r) {
            if (!$r['uncertainty_value_double']) {
                continue;
            }

            if (isset($r['result'])) {
                $co2e = $r['result']['greenhouse_gas_double'];
            } else {
                $co2e = $r['greenhouse_gas_double'];
            }
            $ui = $r['uncertainty_value_double'] * $co2e;
            $totalUi2 += pow($ui, 2);
            $totalCo2e += $co2e;
        }
        if ($totalCo2e == 0) {
            return 0;
        }
        return sqrt($totalUi2) / $totalCo2e;
    }
}
