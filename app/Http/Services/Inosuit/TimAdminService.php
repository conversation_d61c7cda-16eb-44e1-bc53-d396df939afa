<?php

namespace App\Http\Services\Inosuit;

use App\Http\Requests\Inosuit\TimAdmin\GetPeriodRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetPeriodReportExportRequest;
use App\Models\Period;
use App\Models\CompanyPeriod;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\Response;

class TimAdminService
{
    public function getPeriods(GetPeriodRequest $request): array
    {
        $currentPeriods = Period::with('lanes')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->orderByDesc('start_date')
            ->get();
        $otherPeriods = Period::with('lanes')
            ->whereNotIn('id', $currentPeriods->pluck('id'))
            ->orderByDesc('start_date')
            ->get();
        return [
            ...$currentPeriods,
            ...$otherPeriods,
        ];
    }

    public function exportPeriodReports(GetPeriodReportExportRequest $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $spreadsheet = new Spreadsheet();

        $periods = Period::query()
            ->orderBy('start_date')
            ->get();

        if ($periods->count() > 0) {
            $spreadsheet->removeSheetByIndex(0);

            foreach ($periods as $period) {
                $this->createPeriodSheet($spreadsheet, $period);
            }
        } else {
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('Dönem Raporları');
            $sheet->setCellValue('A1', 'Henüz hiç dönem bulunmamaktadır.');
        }

        $spreadsheet->setActiveSheetIndex(0);
        $filename = 'period_reports.xlsx';
        $writer = new Xlsx($spreadsheet);
        $temp_file = tempnam(sys_get_temp_dir(), $filename);
        $writer->save($temp_file);

        return Response::download($temp_file, $filename)->deleteFileAfterSend();
    }

    private function createPeriodSheet(Spreadsheet $spreadsheet, Period $period): void
    {
        $companyPeriods = CompanyPeriod::query()
            ->with([
                'company' => fn($q) => $q->select('id', 'name', 'tax_number', 'email', 'phone'),
                'mentor' => fn($q) => $q->select('id', 'full_name', 'email', 'phone'),
                'association' => fn($q) => $q->select('id', 'name'),
                'period' => fn($q) => $q->select('id', 'name'),
                'companyPapers' => fn($q) => $q->select('id', 'company_period_id', 'name', 'status', 'term_date'),
                'mentorPapers' => fn($q) => $q->select('id', 'company_period_id', 'name', 'status', 'term_date'),
            ])
            ->where('period_id', $period->id)
            ->get();

        $startDate = Carbon::parse($period->start_date);
        $endDate = Carbon::parse($period->end_date);
        $periodMonths = [];

        $currentDate = $startDate->copy()->startOfMonth();
        while ($currentDate <= $endDate) {
            $periodMonths[] = [
                'year' => $currentDate->year,
                'month' => $currentDate->month,
                'monthName' => $currentDate->locale('tr')->translatedFormat('F Y'),
                'date' => $currentDate->copy()
            ];
            $currentDate->addMonth();
        }

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle($period->name);

        // Başlıkları ayarla
        $sheet->setCellValue('A1', 'Şirket Bilgileri');
        $sheet->setCellValue('A2', 'Şirket Adı');
        $sheet->setCellValue('B2', 'E-posta');
        $sheet->setCellValue('C2', 'Telefon');
        $sheet->setCellValue('D2', 'İhracatçı Birliği');
        $sheet->setCellValue('E2', 'Durum');

        $sheet->setCellValue('G1', 'Mentor Bilgileri');
        $sheet->setCellValue('G2', 'Mentor Adı');
        $sheet->setCellValue('H2', 'E-posta');
        $sheet->setCellValue('I2', 'Telefon');

        $sheet->setCellValue('K1', 'Şirket Raporları');
        $sheet->setCellValue('K2', 'Rapor Adı');
        $sheet->setCellValue('L2', 'Durum');
        $sheet->setCellValue('M2', 'Rapor Dönemi');

        $sheet->setCellValue('O1', 'Mentor Raporları');
        $sheet->setCellValue('O2', 'Rapor Adı');
        $sheet->setCellValue('P2', 'Durum');
        $sheet->setCellValue('Q2', 'Rapor Dönemi');

        $sheet->getStyle('A1:E1')->getFont()->setBold(true);
        $sheet->getStyle('G1:I1')->getFont()->setBold(true);
        $sheet->getStyle('K1:M1')->getFont()->setBold(true);
        $sheet->getStyle('O1:Q1')->getFont()->setBold(true);
        $sheet->getStyle('A2:Q2')->getFont()->setBold(true);

        $sheet->getStyle('A2:Q2')->getBorders()->getBottom()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK);

        $row = 3;
        if ($companyPeriods->count() > 0) {
            foreach ($companyPeriods as $companyPeriod) {
                $startRow = $row;

                $sheet->setCellValue('A' . $row, $companyPeriod->company->name ?? '');
                $sheet->setCellValue('B' . $row, $companyPeriod->company->email ?? '');
                $sheet->setCellValue('C' . $row, $companyPeriod->company->phone ?? '');
                $sheet->setCellValue('D' . $row, $companyPeriod->association->name ?? '');
                $sheet->setCellValue('E' . $row, $this->getStatusText($companyPeriod->status));

                $sheet->setCellValue('G' . $row, $companyPeriod->mentor->full_name ?? '');
                $sheet->setCellValue('H' . $row, $companyPeriod->mentor->email ?? '');
                $sheet->setCellValue('I' . $row, $companyPeriod->mentor->phone ?? '');

                $companyPaperRow = $row;
                foreach ($periodMonths as $month) {
                    $papers = $companyPeriod->companyPapers->filter(function ($p) use ($month) {
                        $paperDate = Carbon::parse($p->term_date);
                        return $paperDate->year == $month['year'] && $paperDate->month == $month['month'];
                    });

                    if ($papers->count() > 0) {
                        foreach ($papers as $paper) {
                            $sheet->setCellValue('K' . $companyPaperRow, $paper->name);
                            $sheet->setCellValue('L' . $companyPaperRow, $this->getPaperStatusText($paper->status));
                            $sheet->setCellValue('M' . $companyPaperRow, $month['monthName']);
                            $companyPaperRow++;
                        }
                    } else {
                        $sheet->setCellValue('K' . $companyPaperRow, $month['monthName'] . ' Raporu Yok');
                        $sheet->setCellValue('L' . $companyPaperRow, '');
                        $sheet->setCellValue('M' . $companyPaperRow, $month['monthName']);
                        $sheet->getStyle('K' . $companyPaperRow)->getFont()->getColor()->setRGB('FF0000');
                        $companyPaperRow++;
                    }
                }

                $periodStartDate = Carbon::parse($period->start_date);
                $periodEndDate = Carbon::parse($period->end_date);

                $outOfRangePapers = $companyPeriod->companyPapers->filter(function ($p) use ($periodStartDate, $periodEndDate) {
                    if (!$p->term_date) return false;
                    $paperDate = Carbon::parse($p->term_date);
                    return $paperDate < $periodStartDate || $paperDate > $periodEndDate;
                });

                foreach ($outOfRangePapers as $paper) {
                    $paperDate = Carbon::parse($paper->term_date);
                    $monthName = $paperDate->locale('tr')->translatedFormat('F Y');

                    $sheet->setCellValue('K' . $companyPaperRow, $paper->name . ' (Dönem Dışı)');
                    $sheet->setCellValue('L' . $companyPaperRow, $this->getPaperStatusText($paper->status));
                    $sheet->setCellValue('M' . $companyPaperRow, $monthName);
                    $sheet->getStyle('K' . $companyPaperRow)->getFont()->getColor()->setRGB('FF6600'); // Turuncu renk
                    $companyPaperRow++;
                }

                $mentorPaperRow = $row;

                foreach ($periodMonths as $month) {
                    $papers = $companyPeriod->mentorPapers->filter(function ($p) use ($month) {
                        if (!$p->term_date) {
                            return false;
                        }
                        $paperDate = Carbon::parse($p->term_date);
                        return $paperDate->year == $month['year'] && $paperDate->month == $month['month'];
                    });

                    if ($papers->count() > 0) {
                        foreach ($papers as $paper) {
                            $sheet->setCellValue('O' . $mentorPaperRow, $paper->name);
                            $sheet->setCellValue('P' . $mentorPaperRow, $this->getPaperStatusText($paper->status));
                            $sheet->setCellValue('Q' . $mentorPaperRow, $month['monthName']);
                            $mentorPaperRow++;
                        }
                    } else {
                        $sheet->setCellValue('O' . $mentorPaperRow, $month['monthName'] . ' Raporu Yok');
                        $sheet->setCellValue('P' . $mentorPaperRow, '');
                        $sheet->setCellValue('Q' . $mentorPaperRow, $month['monthName']);
                        $sheet->getStyle('O' . $mentorPaperRow)->getFont()->getColor()->setRGB('FF0000');
                        $mentorPaperRow++;
                    }
                }

                $outOfRangeMentorPapers = $companyPeriod->mentorPapers->filter(function ($p) use ($periodStartDate, $periodEndDate) {
                    if (!$p->term_date) return false;
                    $paperDate = Carbon::parse($p->term_date);
                    return $paperDate < $periodStartDate || $paperDate > $periodEndDate;
                });

                foreach ($outOfRangeMentorPapers as $paper) {
                    $paperDate = Carbon::parse($paper->term_date);
                    $monthName = $paperDate->locale('tr')->translatedFormat('F Y');

                    $sheet->setCellValue('O' . $mentorPaperRow, $paper->name . ' (Dönem Dışı)');
                    $sheet->setCellValue('P' . $mentorPaperRow, $this->getPaperStatusText($paper->status));
                    $sheet->setCellValue('Q' . $mentorPaperRow, $monthName);
                    $sheet->getStyle('O' . $mentorPaperRow)->getFont()->getColor()->setRGB('FF6600'); // Turuncu renk
                    $mentorPaperRow++;
                }

                $maxRow = max($companyPaperRow, $mentorPaperRow);
                $endRow = $maxRow > $row ? $maxRow - 1 : $row;

                if ($endRow >= $startRow) {
                    $sheet->mergeCells('A' . $startRow . ':A' . $endRow);
                    $sheet->getStyle('A' . $startRow . ':A' . $endRow)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
                    $sheet->getStyle('A' . $startRow . ':A' . $endRow)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                }

                $sheet->getStyle('A' . $endRow . ':Q' . $endRow)->getBorders()->getBottom()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK);

                $row = $endRow + 1;
            }
        } else {
            $sheet->setCellValue('A' . $row, 'Bu dönemde henüz hiç şirket bulunmamaktadır.');
        }

        foreach (range('A', 'Q') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }
    }

    private function getStatusText($status): string
    {
        return match ($status) {
            1 => 'Onaylandı',
            -1 => 'Reddedildi',
            0 => 'Beklemede',
            default => 'Bilinmiyor'
        };
    }

    private function getPaperStatusText($status): string
    {
        return match ($status) {
            1 => 'Tamamlandı',
            -1 => 'Reddedildi',
            0 => 'Beklemede',
            default => 'Bilinmiyor'
        };
    }
}
