<?php

namespace App\Http\Services\Inosuit;

use App\Http\Requests\Inosuit\Mentor\GetPeriodRequest;
use App\Models\Period;

class MentorService
{
    public function getPeriods(GetPeriodRequest $request): array
    {
        $currentPeriods = Period::query()
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->whereHas('companyPeriods', function ($q) use ($request) {
                if (isset($request->companyId)) {
                    $q->where('company_id', $request->companyId);
                }
                $q->where('mentor_user_id', auth()->id());
            })
            ->orderByDesc('start_date')
            ->get();
        $otherPeriods = Period::query()
            ->whereHas('companyPeriods', function ($q) use ($request) {
                if (isset($request->companyId)) {
                    $q->where('company_id', $request->companyId);
                }
                $q->where('mentor_user_id', auth()->id());
            })
            ->whereNotIn('id', $currentPeriods->pluck('id'))
            ->orderByDesc('start_date')
            ->get();
        return [
            ...$currentPeriods,
            ...$otherPeriods,
        ];
    }
}
