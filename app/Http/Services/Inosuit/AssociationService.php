<?php

namespace App\Http\Services\Inosuit;

use App\Http\Requests\Inosuit\Association\GetPeriodRequest;
use App\Models\Period;

class AssociationService
{
    public function getPeriods(GetPeriodRequest $request): array
    {
        $currentPeriods = Period::query()
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->whereHas('companyPeriods', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->orderByDesc('start_date')
            ->get();
        $otherPeriods = Period::query()
            ->whereHas('companyPeriods', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->whereNotIn('id', $currentPeriods->pluck('id'))
            ->orderByDesc('start_date')
            ->get();
        return [
            ...$currentPeriods,
            ...$otherPeriods,
        ];
    }
}
