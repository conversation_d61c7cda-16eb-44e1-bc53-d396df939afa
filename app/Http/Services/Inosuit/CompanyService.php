<?php

namespace App\Http\Services\Inosuit;

use App\Http\Requests\Inosuit\Company\GetPeriodRequest;
use App\Models\Period;

class CompanyService
{
    public function getPeriods(GetPeriodRequest $request): array
    {
        $currentPeriods = Period::query()
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->whereHas('companyPeriods', fn($q) => $q->where('company_id', auth()->user()->company_id))
            ->orderByDesc('start_date')
            ->get();
        $otherPeriods = Period::query()
            ->whereHas('companyPeriods', fn($q) => $q->where('company_id', auth()->user()->company_id))
            ->whereNotIn('id', $currentPeriods->pluck('id'))
            ->orderByDesc('start_date')
            ->get();
        return [
            ...$currentPeriods,
            ...$otherPeriods,
        ];
    }
}
