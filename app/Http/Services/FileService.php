<?php

namespace App\Http\Services;

use App\Enums\FileTypeEnum;
use App\Http\Requests\File\CreateFileRequest;
use App\Http\Requests\File\CreateFileUrlRequest;
use App\Http\Requests\File\DeleteFileRequest;
use App\Models\FileManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class FileService
{
    function getFileTypeFromMime(string $mimetype): FileTypeEnum
    {
        if ($mimetype == 'image/jpg' || $mimetype == 'image/jpeg') {
            return FileTypeEnum::JPEG;
        } elseif ($mimetype == 'image/png') {
            return FileTypeEnum::PNG;
        } elseif ($mimetype == 'application/pdf') {
            return FileTypeEnum::PDF;
        } elseif (
            $mimetype == 'application/msword' ||
            $mimetype == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ) {
            return FileTypeEnum::WORD;
        } elseif (
            $mimetype == 'application/vnd.ms-excel' ||
            $mimetype == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ) {
            return FileTypeEnum::EXCEL;
        } elseif (
            $mimetype == 'application/vnd.ms-powerpoint' ||
            $mimetype == 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ) {
            return FileTypeEnum::POWER_POINT;
        } else {
            throw new BadRequestHttpException('Beklenmedik dosya türü.');
        }
    }

    public function getFilePath(string $url): string
    {
        $pathArr = explode('/', $url);
        $path = $pathArr[count($pathArr) - 2] . '/' . $pathArr[count($pathArr) - 1];
        return $path;
    }

    public function createFile(CreateFileRequest $request): FileManager
    {
        $file = $request->file('file');
        $extension = strtolower(collect(explode('.', $file->getClientOriginalName()))->pop());
        $fileName = $request->post('path') . '/' . Str::random(rand(30, 50)) . '-' . now()->getTimestamp() . '.' . $extension;
        Storage::disk('s3')->put($fileName, $file->getContent());
        $url = Storage::disk('s3')->url($fileName);

        $fileManager = new FileManager();
        $fileManager->user_id = auth()->id();
        $fileManager->report_id = $request->reportId;
        $fileManager->name = $request->name ?? $file->getClientOriginalName();
        $fileManager->url = $url;
        $fileManager->type = $this->getFileTypeFromMime($file->getMimeType());
        $fileManager->size = round($file->getSize() / 1024);
        $fileManager->s3 = true;
        $fileManager->save();

        return $fileManager;
    }

    public function createFileUrl(CreateFileUrlRequest $request): FileManager
    {
        $fileManager = new FileManager();
        $fileManager->user_id = auth()->id();
        $fileManager->report_id = $request->reportId;
        $fileManager->url = $request->url;
        $fileManager->s3 = false;
        $fileManager->save();

        return $fileManager;
    }

    public function deleteFile(DeleteFileRequest $request): void
    {
        DB::beginTransaction();
        try {
            $fileManager = FileManager::query()
                ->where('id', $request->fileId)
                ->whereHas('user', fn($q) => $q->where('company_id', auth()->user()->company_id))
                ->findOrFail($request->fileId);
            $fileManager->delete();

            if ($fileManager->s3) {
                $path = $this->getFilePath($fileManager->url);
                Storage::disk('s3')->delete($path);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw new BadRequestHttpException(Lang::get('messages.general.unexpected'));
        }
    }
}
