<?php

namespace App\Http\Services;

use App\Enums\PermissionEnum;
use App\Http\Requests\Report\CreateReportRequest;
use App\Http\Requests\Report\GetReportRequest;
use App\Models\FileManager;
use App\Models\Form;
use App\Models\FormInput;
use App\Models\FormResult;
use App\Models\Report;
use App\Models\TipContentUser;
use App\Utils\GeneralUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ReportService
{
    private readonly FormService $formService;
    private readonly UncertaintyService $uncertaintyService;

    public function __construct()
    {
        $this->formService = new FormService();
        $this->uncertaintyService = new UncertaintyService();
    }

    public function getReports(GetReportRequest $request): array
    {
        $user = auth()->user();
        $allFormInputs = FormInput::with('languages')->get();
        $reports = Report::query()
            ->with([
                'form.languages',
                'form.category.languages',
                'form.commons.groups',
                'user' => fn($q) => $q->select('id', 'full_name', 'title', 'photo'),
                'facility',
                'files.user',
                'result.values.groupValueLanguages',
            ])
            ->where('company_id', $user->company_id);
        if ($user->hasPermission(PermissionEnum::REPORT_SELF)) {
            $reports = $reports->where('user_id', $user->id);
        }
        if ($request->formId) {
            $reports = $reports->where('form_id', $request->formId);
        }
        if ($request->formSlug) {
            $reports = $reports->whereHas('form', fn($q) => $q->where('slug', $request->formSlug));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->monthIds) {
            foreach (explode(',', $request->monthIds) as $monthId) {
                $reports = $reports->whereRaw('FIND_IN_SET(?, months)', [$monthId]);
            }
        }
        $reports = $reports->get();
        $reports->makeHidden('form');
        Report::mapCommonInputs($reports);
        Report::mapResults($reports);
        Report::mapMonths($reports);
        Report::mapValueLanguages($reports);

        $reportsWithCategories = [];
        foreach ($reports as $report) {
            $categoryName = $report->form->category->name;
            $formName = $report->form->name;

            if (isset($reportsWithCategories[$categoryName]['greenhouse_gas_double'])) {
                $reportsWithCategories[$categoryName]['greenhouse_gas_double'] += $report->result->greenhouse_gas_double;
            } else {
                $reportsWithCategories[$categoryName]['greenhouse_gas'] = '0';
                $reportsWithCategories[$categoryName]['greenhouse_gas_double'] = $report->result->greenhouse_gas_double;
            }
            $reportsWithCategories[$categoryName]['greenhouse_gas'] = GeneralUtil::numberFormat($reportsWithCategories[$categoryName]['greenhouse_gas_double']);

            if (isset($reportsWithCategories[$categoryName][$formName]['reports'])) {
                $reportsWithCategories[$categoryName][$formName]['greenhouse_gas_double'] += $report->result->greenhouse_gas_double;
                $reportsWithCategories[$categoryName][$formName]['reports'][] = $report;
            } else {
                $formReports = $reports->where('form_id', $report->form_id);

                $inputs = $allFormInputs->where('form_id', $report->form_id)->sortBy('order')->values();
                FormInput::addLangProperties($inputs);
                $reportsWithCategories[$categoryName][$formName] = [
                    'greenhouse_gas' => '0',
                    'greenhouse_gas_double' => $report->result->greenhouse_gas_double,
                    'report_lines' => $report->form->report_lines,
                    'description' => $report->form->description,
                    'inputs' => $inputs,
                    'reports' => [$report],
                ];
            }
            $reportsWithCategories[$categoryName][$formName]['greenhouse_gas'] = GeneralUtil::numberFormat($reportsWithCategories[$categoryName][$formName]['greenhouse_gas_double']);

            Report::addMissingValues([$report], $reportsWithCategories[$categoryName][$formName]['inputs']);
            $report->hasS3Files = !!$report->files->first(fn($fileManager) => $fileManager->s3);
        }
        return $reportsWithCategories;
    }

    public function createReport(CreateReportRequest $request): Report
    {
        $form = Form::query()->findOrFail($request->formId);

        DB::beginTransaction();
        try {
            $gasses = [];
            if ($request->payload) {
                $gasses = $this->formService->calculateFormulaGasses($request->formId, $request->payload);
            }

            $report = new Report();
            $report->form_id = $request->formId;
            $report->user_id = auth()->id();
            $report->company_id = auth()->user()->company_id;
            $report->facility_id = $request->facilityId;
            $report->result_id = $request->resultId;
            $report->months = implode(',', $request->monthIds);
            $report->year = $request->year;
            $report->amount = $request->amount;
            $report->carbon_dioxide = $gasses['carbon_dioxide'] ?? null;
            $report->nitrous_oxide = $gasses['nitrous_oxide'] ?? null;
            $report->methane = $gasses['methane'] ?? null;
            $report->extra_gas = $gasses['extra_gas'] ?? null;
            $report->payload = $request->payload ? json_encode($request->payload) : null;
            $report->description = $request->description;
            $report->save();

            if ($request->resultId) {
                $formResult = FormResult::with('values')->findOrFail($request->resultId);
                $groupValueIds = $formResult->values->pluck('group_value_id')->toArray();
                $gasses = $this->formService->calculateStandardGasses($formResult, $request->amount);
            } else {
                $groupValueIds = collect($request->payload)->filter(fn($v) => isset($v['groupValueId']))->map(fn($v) => $v['groupValueId'])->toArray();
            }
            $this->uncertaintyService->upsertUncertaintyReport(
                $report->id,
                $form,
                $gasses,
                $groupValueIds,
                $request->errorMargin,
                $request->isCalibration,
                $request->uncertaintyValue,
                $request->pedigreeTabs,
                $request->electricity,
            );

            if ($request->fileIds) {
                FileManager::query()
                    ->whereIn('id', $request->fileIds)
                    ->where('user_id', auth()->id())
                    ->whereNull('report_id')
                    ->update(['report_id' => $report->id]);
            }

            TipContentUser::addTip('report-save');
            DB::commit();
            return $report;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            throw new BadRequestHttpException(Lang::get('messages.general.unexpected'));
        }
    }
}
