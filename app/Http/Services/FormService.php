<?php

namespace App\Http\Services;

use App\Enums\FormResultEnum;
use App\Enums\GwpEnum;
use App\Http\Requests\Form\CalculateFormResultRequest;
use App\Http\Requests\Form\GetFormBySlugRequest;
use App\Models\Form;
use App\Models\FormCategory;
use App\Models\FormGroupValue;
use App\Models\FormInput;
use App\Models\FormInputToGroup;
use App\Models\FormResult;
use App\Utils\GeneralUtil;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class FormService
{
    private readonly UncertaintyService $uncertaintyService;

    public function __construct()
    {
        $this->uncertaintyService = new UncertaintyService();
    }

    public function getFormsBySlug(GetFormBySlugRequest $request, string $slug): \Illuminate\Database\Eloquent\Model
    {
        $relatedGroupValueId = $request->groupValueId ?? null;
        $form = Form::with(['languages', 'inputs.languages', 'category.languages', 'commons.groups'])->where('slug', $slug)->firstOrFail();

        if (env('APP_ENV') == 'production') {
            $uncertaintyTestUserIds = explode(',', env('UNCERTAINTY_TEST_USER_IDS'));
            if (!in_array(auth()->id(), $uncertaintyTestUserIds)) {
                $form->uncertainty_type = null;
            }
        }
        if ($form->is_locked && !auth()->user()->is_admin) {
            throw new HttpException(Response::HTTP_FORBIDDEN);
        }

        Form::addLangProperties([$form]);
        FormCategory::addLangProperties([$form->category]);
        if ($request->inputIds) {
            $formInputs = [];
            foreach (explode(',', $request->inputIds) as $inputId) {
                $input = FormInput::with('languages')->where('form_id', $form->id)->findOrFail($inputId);
                FormInput::addLangProperties([$input]);
                FormInput::mapRelatedInputIds([$input]);
                $group = FormInputToGroup::with('values.languages')
                    ->where('input_id', $input->id)
                    ->where('related_group_value_id', $relatedGroupValueId)
                    ->first();
                $input->values = $group ? $group->values : [];
                if (count($input->values) == 0 && $form->commons->count() > 0) {
                    $formCommonIdCacheKey = "form-common-id-" . auth()->id() . "-$slug-" . $request->header('Form-Unique-Key');
                    $this->formCommons($form, $input, $relatedGroupValueId, $formCommonIdCacheKey);
                }
                if (count($input->values) > 0) {
                    FormGroupValue::addLangProperties($input->values);
                }
                if ($group || count($input->values) > 0) {
                    $formInputs[] = $input;
                }
            }
            unset($form->inputs);
            $form->inputs = $formInputs;
        } else {
            FormInput::addLangProperties([$form], 'inputs');
            FormInput::mapRelatedInputIds($form->inputs);
        }
        $form->makeHidden('commons');
        return $form;
    }

    private function formCommons(Form $form, FormInput $input, int $relatedGroupValueId, string $formCommonIdCacheKey): void
    {
        $formCommonId = null;
        foreach ($form->commons as $common) {
            $relatedCommonGroup = $common->groups
                ->whereIn('input_id', $input->related_input_ids)
                ->where('group_value_id', $relatedGroupValueId)
                ->first();
            if ($relatedCommonGroup) {
                $formCommonId = $relatedCommonGroup->common_id;
                $relatedGroupValueId = $relatedCommonGroup->common_group_value_id;
                Cache::put($formCommonIdCacheKey, $formCommonId, now()->addMinutes(20));
                break;
            }
        }
        if (!$formCommonId) {
            $formCommonId = Cache::get($formCommonIdCacheKey);
        }
        if ($formCommonId) {
            $selfCommonGroup = $form->commons->find($formCommonId)->groups->where('input_id', $input->id)->first();
            $group = FormInputToGroup::with('values.languages')
                ->where('input_id', $selfCommonGroup?->common_input_id)
                ->where('related_group_value_id', $relatedGroupValueId)
                ->first();
            $input->values = $group ? $group->values : [];
        }
    }

    public function calculateFormResults(CalculateFormResultRequest $request): array
    {
        $formId = $request->formId;
        $values = $request->values;
        $form = Form::with('commons.groups', 'inputs')->findOrFail($formId);

        $gasses = $this->calculateFormulaGasses($formId, $values);
        if (count($gasses) > 0) {
            $groupValueIds = collect($values)->filter(fn($v) => isset($v['groupValueId']))->map(fn($v) => $v['groupValueId'])->toArray();
            $uncertaintyValue = $this->uncertaintyService->calculateUncertainty(
                $form,
                $gasses,
                $groupValueIds,
                $request->errorMargin,
                $request->isCalibration,
                $request->uncertaintyValue,
                $request->pedigreeTabs,
                $request->electricity
            );
            $greenhouseGas = array_sum(array_values($gasses));
            return [
                'greenhouse_gas' => GeneralUtil::numberFormat($greenhouseGas),
                'greenhouse_gas_double' => $greenhouseGas,
                'type' => FormResultEnum::FORMULA,
                'uncertainty_value' => is_null($uncertaintyValue) ? null : GeneralUtil::numberFormat($uncertaintyValue * 100, 2),
                'uncertainty_value_double' => $uncertaintyValue,
            ];
        }

        $formResult = FormResult::with('values')->where('form_id', $formId);
        foreach ($values as $value) {
            $formResult->whereHas('values', function ($q) use ($value) {
                $q->where('input_id', $value['inputId'])->where('group_value_id', $value['groupValueId']);
            });
        }
        $formResult = $formResult->first();
        if (!$formResult && $form->commons->count() > 0) {
            $formCommonIdCacheKey = "form-common-id-" . auth()->id() . "-$form->slug-" . $request->header('Form-Unique-Key');
            $formResult = $this->calculateCommon($form, $values, $formCommonIdCacheKey);
        }
        if (!$formResult) {
            throw new NotFoundHttpException();
        }
        if (count($formResult->values) != count($values)) {
            throw new BadRequestHttpException(Lang::get('messages.form.values_not_equal'));
        }
        $gasses = $this->calculateStandardGasses($formResult, $request->amount);
        $formResult = FormResult::mapResult($form, $formResult, $request->amount, true);
        $groupValueIds = $formResult->values->map(fn($v) => $v->group_value_id)->values()->toArray();

        $uncertaintyValue = $this->uncertaintyService->calculateUncertainty(
            $form,
            $gasses,
            $groupValueIds,
            $request->errorMargin,
            $request->isCalibration,
            $request->uncertaintyValue,
            $request->pedigreeTabs,
            $request->electricity
        );
        $formResult->uncertainty_value = is_null($uncertaintyValue) ? null : GeneralUtil::numberFormat($uncertaintyValue * 100, 2);
        $formResult->uncertainty_value_double = $uncertaintyValue;
        return $formResult->toArray();
    }

    public function calculateStandardGasses(FormResult $result, float $amount): array
    {
        return [
            'carbon_dioxide' => $amount * $result->carbon_dioxide / 1000,
            'nitrous_oxide' => $amount * $result->nitrous_oxide / 1000,
            'methane' => $amount * $result->methane / 1000,
        ];
    }

    public function calculateFormulaGasses(int $formId, array $values): array
    {
        $groupValueIds = collect($values)->filter(fn($v) => isset($v['groupValueId']))->map(fn($v) => $v['groupValueId']);
        $form = Form::query()
            ->with('operations', function ($q) use ($groupValueIds) {
                $q->where(function ($andQuery) use ($groupValueIds) {
                    $andQuery->whereJsonDoesntContainKey('operation->groupValueIds')->whereJsonDoesntContainKey('operation->andGroupValueIds');
                })
                    ->orWhereJsonContainsKey('operation->andGroupValueIds')
                    ->orWhere(function ($orQuery) use ($groupValueIds) {
                        $groupValueIds->map(fn($vId) => $orQuery->orWhereJsonContains('operation->groupValueIds', $vId));
                    });
            })
            ->with('multipliers', function ($q) use ($groupValueIds) {
                $q->where(fn($orQuery) => $groupValueIds->map(fn($vId) => $orQuery->orWhereJsonContains('group_value_ids', $vId)));
            })
            ->findOrFail($formId);
        $form->operations = $form->operations->filter(function ($o) use ($groupValueIds) {
            $operation = json_decode($o->operation);
            if (isset($operation->andGroupValueIds)) {
                return empty(array_diff($operation->andGroupValueIds, $groupValueIds->toArray()));
            }
            return true;
        });
        if ($form->operations->count() == 0 || $form->multipliers->count() == 0) {
            return [];
        }
        $values = collect(json_decode(json_encode($values)));
        $operation = json_decode($form->operations->first()->operation);
        $form->multipliers = $form->multipliers->filter(function ($m) use ($groupValueIds) {
            $m->group_value_ids = json_decode($m->group_value_ids);
            $m->multiplier = json_decode($m->multiplier);
            return collect($m->group_value_ids)->every(fn($gvId) => $groupValueIds->contains($gvId));
        });
        $multipliers = $form->multipliers->where('is_unit', '=', false);
        $unitMultipliers = $form->multipliers->where('is_unit', '=', true);
        $gasses = [];
        if ($multipliers->count() == 0) {
            return [];
        }
        foreach ($operation->multipliers as $opMultipliers) {
            $mulResult = 0;
            $divResult = 0;
            foreach ($opMultipliers->values as $mulValue) {
                $multiplication = 1;
                foreach ($mulValue as $mV) {
                    if (in_array(gettype($mV), ['integer', 'double'])) {
                        $multiplication *= $mV;
                    } else if (gettype($mV) == 'string') {
                        $multiplication *= $multipliers->first(fn($m) => isset($m->multiplier->$mV))->multiplier->$mV;
                    } else if (gettype($mV) == 'object' && isset($mV->inputIds) && isset($mV->field)) {
                        $selectedValues = $values->filter(fn($v) => in_array($v->inputId, $mV->inputIds));
                        $field = $mV->field;
                        $multiplication *= $multipliers
                            ->first(fn($m) => $selectedValues->every(fn($sV) => in_array($sV->groupValueId, $m->group_value_ids)) && isset($m->multiplier->$field))
                            ->multiplier->$field;
                    } else if (gettype($mV) == 'object' && isset($mV->inputId) && isset($mV->groupValueId)) {
                        $controlValue = $values->first(fn($v) => $v->inputId == $mV->inputId && isset($v->groupValueId) && $v->groupValueId == $mV->groupValueId);
                        if (!$controlValue) {
                            $multiplication = 0;
                            break;
                        }
                    } else if (gettype($mV) == 'object' && isset($mV->inputId)) {
                        $selectedUnit = $values->first(fn($v) => $v->inputId == $mV->inputId);
                        $multiplication *= $selectedUnit->numericValue ?? 1;
                        if (isset($selectedUnit->groupValueId)) {
                            $unitMul = $unitMultipliers
                                ->first(fn($m) => in_array($selectedUnit->groupValueId, $m->group_value_ids))
                                ?->multiplier;
                            $multiplication *= $unitMul ? $unitMul->unitValue : 1;
                        }
                    }
                }
                $mulResult += $multiplication;
            }
            if (isset($opMultipliers->dividers)) {
                foreach ($opMultipliers->dividers as $mulDivider) {
                    $multiplication = 1;
                    foreach ($mulDivider as $mD) {
                        if (in_array(gettype($mD), ['integer', 'double'])) {
                            $multiplication *= $mD;
                        } else if (gettype($mD) == 'string') {
                            $multiplication *= $multipliers->first(fn($m) => isset($m->multiplier->$mD))->multiplier->$mD;
                        } else if (gettype($mD) == 'object' && isset($mD->inputId)) {
                            $selectedUnit = $values->first(fn($v) => $v->inputId == $mD->inputId);
                            $multiplication *= $selectedUnit->numericValue ?? 1;
                            if (isset($selectedUnit->groupValueId)) {
                                $unitMul = $unitMultipliers
                                    ->first(fn($m) => in_array($selectedUnit->groupValueId, $m->group_value_ids))
                                    ?->multiplier;
                                $multiplication *= $unitMul ? $unitMul->unitValue : 1;
                            }
                        }
                    }
                    $divResult += $multiplication;
                }
            }
            $gasses[$opMultipliers->key] = $mulResult / ($divResult == 0 ? 1 : $divResult);
        }
        foreach ($gasses as $key => $gas) {
            if (!$form->has_gwp) {
                if ($key == 'carbon_dioxide') {
                    $gas *= GwpEnum::$CO2;
                } else if ($key == 'nitrous_oxide') {
                    $gas *= GwpEnum::$N2O;
                } else if ($key == 'methane') {
                    $gas *= GwpEnum::$CH4;
                } else if ($key == 'extra_gas') {
                    $gas *= GwpEnum::$extraGas;
                }
            }
            $gasses[$key] = $gas / 1000;
        }
        return $gasses;
    }

    private function calculateCommon(Form $form, array &$values, string $formCommonIdCacheKey): ?FormResult
    {
        $controlIndex = 0;
        $formCommonId = Cache::get($formCommonIdCacheKey);
        if (!$formCommonId) {
            return null;
        }
        $common = $form->commons->find($formCommonId);
        $formId = $common->common_form_id;
        foreach ($values as $index => &$value) {
            $commonRelated = $common->groups->where('input_id', $value['inputId'])->where('group_value_id', $value['groupValueId'])->first();
            if ($commonRelated) {
                $value['groupValueId'] = $commonRelated->common_group_value_id;
                $controlIndex = $index;
            }
            $commonSelf = $common->groups->where('input_id', $value['inputId'])->first();
            if ($commonSelf) {
                $value['inputId'] = $commonSelf->common_input_id;
            }
        }
        for ($i = 0; $i < $controlIndex; $i++) {
            unset($values[$i]);
        }
        $values = array_values($values);

        $formResult = FormResult::with('values')->where('form_id', $formId);
        foreach ($values as $v) {
            $formResult->whereHas('values', function ($q) use ($v) {
                $q->where('input_id', $v['inputId'])->where('group_value_id', $v['groupValueId']);
            });
        }
        return $formResult->first();
    }
}
