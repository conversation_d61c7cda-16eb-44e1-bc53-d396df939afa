<?php

namespace App\Http\Middleware;

use App\Http\Responses\GeneralResponse;
use App\Models\Facility;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class FacilityGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $facilities = Facility::query()->where('company_id', auth()->user()->company_id)->get();
        if ($facilities->count() > 0) {
            return $next($request);
        }
        return (new GeneralResponse(false))
            ->setData(['error' => 'no_facility'])
            ->setMessages(Lang::get('messages.facility.not_defined'))
            ->toJson();
    }
}
