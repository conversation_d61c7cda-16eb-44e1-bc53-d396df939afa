<?php

namespace App\Http\Middleware;

use App\Http\Responses\GeneralResponse;
use App\Models\Report;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class ReportGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $reports = Report::query()->where('company_id', auth()->user()->company_id)->get();
        if ($reports->count() > 0) {
            return $next($request);
        }
        return (new GeneralResponse(false))
            ->setData(['error' => 'no_report'])
            ->setMessages(Lang::get('messages.report.not_defined'))
            ->toJson();
    }
}
