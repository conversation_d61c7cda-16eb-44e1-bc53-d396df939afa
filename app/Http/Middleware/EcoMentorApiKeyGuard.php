<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

class EcoMentorApiKeyGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $headerApiKey = $request->header('ECO-MENTOR-API-KEY');
        $envApiKey = env('ECO_MENTOR_API_KEY');
        if ($headerApiKey && $envApiKey && $headerApiKey == $envApiKey) {
            return $next($request);
        }
        throw new HttpException(Response::HTTP_FORBIDDEN);
    }
}
