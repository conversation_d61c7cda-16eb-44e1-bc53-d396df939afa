<?php

namespace App\Http\Middleware;

use App\Http\Responses\GeneralResponse;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class EcoMentorTokenGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $ecoMentorToken = $request->headers->get('Authorization');
        $user = User::query()->where('eco_mentor_token', $ecoMentorToken)->first();
        if ($user) {
            $user->last_activity_at = now();
            $user->save();
            Auth::setUser($user);
            return $next($request);
        }
        return (new GeneralResponse(false, Response::HTTP_UNAUTHORIZED))->setMessages(Lang::get('messages.general.unauthenticated'))->toJson();
    }
}
