<?php

namespace App\Http\Middleware;

use App\Http\Responses\GeneralResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class SuperAdminGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $headerEmail = $request->header('SUPER-ADMIN-EMAIL');
        $headerPassword = $request->header('SUPER-ADMIN-PASSWORD');
        $envEmail = env('SUPER_ADMIN_EMAIL');
        $envPassword = env('SUPER_ADMIN_PASSWORD');

        if ($headerEmail && $headerPassword && $envEmail && $envPassword) {
            if ($headerEmail == $envEmail && $headerPassword == $envPassword) {
                return $next($request);
            }
        }
        return (new GeneralResponse(false, Response::HTTP_FORBIDDEN))->setMessages(Lang::get('messages.general.forbidden'))->toJson();
    }
}
