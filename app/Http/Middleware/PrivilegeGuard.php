<?php

namespace App\Http\Middleware;

use App\Http\Responses\GeneralResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class PrivilegeGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next, string ...$privileges)
    {
        if (in_array(auth()->user()->privilege->slug, $privileges)) {
            return $next($request);
        }
        return (new GeneralResponse(false, Response::HTTP_FORBIDDEN))->setMessages(Lang::get('messages.general.forbidden'))->toJson();
    }
}
