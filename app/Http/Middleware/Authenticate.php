<?php

namespace App\Http\Middleware;

use App\Http\Responses\GeneralResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        if ($user) {
            $user->last_activity_at = now();
            $user->save();
            return $next($request);
        }
        return (new GeneralResponse(false, Response::HTTP_UNAUTHORIZED))->setMessages(Lang::get('messages.general.unauthenticated'))->toJson();
    }
}
