<?php

namespace App\Http\Middleware;

use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Http\Services\Inosuit\AssociationService;
use App\Http\Services\Inosuit\CompanyService;
use App\Http\Services\Inosuit\MentorService;
use App\Http\Services\Inosuit\TimAdminService;
use App\Models\Period;
use App\Models\User;
use App\Utils\GeneralUtil;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class InosuitPeriodGuard
{
    private readonly TimAdminService $timAdminService;
    private readonly AssociationService $associationService;
    private readonly CompanyService $companyService;
    private readonly MentorService $mentorService;

    public function __construct()
    {
        $this->timAdminService = new TimAdminService();
        $this->associationService = new AssociationService();
        $this->companyService = new CompanyService();
        $this->mentorService = new MentorService();
    }

    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $periodId = request()->headers->get(GeneralUtil::$inosuitPeriodKey);
        $user = User::with('privilege', 'association', 'company.companyPeriods', 'mentor')->findOrFail(auth()->id());

        if ($user->privilege->slug == RolePrivilegeEnum::TIM_ADMIN->value) {
            if ($periodId) {
                $period = Period::query()->find($periodId);
                if (!$period) {
                    throw new BadRequestHttpException('Geçersiz dönem');
                }
            } else {
                // $periods = $this->timAdminService->getPeriods(new \App\Http\Requests\Inosuit\TimAdmin\GetPeriodRequest());
                // if (count($periods) == 0) {
                //     throw new BadRequestHttpException('Dönem bulunamadı');
                // }
                // $periodId = $periods[0]->id;
                $periods = Period::query()->orderBy('start_date')->get();
                if (count($periods) > 0) {
                    $periodId = $periods[0]->id;
                } else {
                    $periodId = null;
                }
            }
        } else if ($user->privilege->slug == RolePrivilegeEnum::ASSOCIATION->value) {
            if ($periodId) {
                $companies = $user->association->getCompanies($periodId);
                if (count($companies) == 0) {
                    throw new BadRequestHttpException('Geçersiz dönem');
                }
            } else {
                $periods = $this->associationService->getPeriods(new \App\Http\Requests\Inosuit\Association\GetPeriodRequest());

                if (count($periods) == 0) {
                    // Hata fırlatmak yerine null değer set et
                    $periodId = null;
                } else {
                    $periodId = $periods[0]->id;
                }
            }
        } else if ($user->privilege->slug == RolePrivilegeEnum::COMPANY->value) {
            if ($periodId) {
                $selectedPeriod = $user->company->companyPeriods
                    ->first(fn($cp) => $cp->period_id == $periodId);
                if (!$selectedPeriod) {
                    throw new BadRequestHttpException('Geçersiz dönem');
                }
            } else {
                $periods = $this->companyService->getPeriods(new \App\Http\Requests\Inosuit\Company\GetPeriodRequest());
                if (count($periods) == 0) {
                    throw new BadRequestHttpException('Dönem bulunamadı');
                }
                $periodId = $periods[0]->id;
            }
        } else if ($user->privilege->slug == RolePrivilegeEnum::MENTOR->value) {
            if ($periodId) {
                $companies = $user->mentor->getCompanies($periodId);
                if (count($companies) == 0) {
                    throw new BadRequestHttpException('Geçersiz dönem');
                }
            } else {
                $periods = $this->mentorService->getPeriods(new \App\Http\Requests\Inosuit\Mentor\GetPeriodRequest());
                if (count($periods) == 0) {
                    throw new BadRequestHttpException('Dönem bulunamadı');
                }
                $periodId = $periods[0]->id;
            }
        } else {
            throw new BadRequestHttpException('Geçersiz kullanıcı tipi');
        }

        request()->headers->set(GeneralUtil::$inosuitPeriodKey, $periodId ? (int)$periodId : null);
        return $next($request);
    }
}
