<?php

namespace App\Http\Middleware;

use App\Enums\PermissionEnum;
use App\Http\Responses\GeneralResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\HttpFoundation\Response;

class PermissionGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next, int ...$permissions)
    {
        $permissionEnums = array_map(fn($p) => PermissionEnum::from($p), $permissions);
        if (auth()->user()->hasPermission(...$permissionEnums)) {
            return $next($request);
        }
        return (new GeneralResponse(false, Response::HTTP_FORBIDDEN))->setMessages(Lang::get('messages.general.forbidden'))->toJson();
    }
}
