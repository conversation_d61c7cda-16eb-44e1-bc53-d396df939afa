<?php

namespace App\Http\Controllers;

use App\Http\Requests\Uncertainty\GetUncertaintyRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Config;
use App\Models\FormUncertainty;
use Illuminate\Http\JsonResponse;

class UncertaintyController extends Controller
{
    public function getUncertainty(GetUncertaintyRequest $request): JsonResponse
    {
        $FV = FormUncertainty::query()
            ->where('form_id', $request->formId)
            ->where('group_value_id', $request->groupValueId)
            ->firstOrFail()
            ->uncertainty['FV'];
        $config = Config::query()
            ->where('key', 'natural_gas_group_value_ids')
            ->first();
        $naturalGasGroupValueIds = $config ? explode(',', $config->value) : [];
        $isDisabled = !in_array($request->groupValueId, $naturalGasGroupValueIds);
        return (new GeneralResponse())->setData([
            'error_margin' => $FV,
            'is_disabled' => $isDisabled,
        ])->toJson();
    }
}
