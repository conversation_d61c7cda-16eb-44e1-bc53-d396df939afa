<?php

namespace App\Http\Controllers\Inosuit;

use App\Enums\ActivityEnum;
use App\Enums\Inosuit\CompanyMeetingStatusEnum;
use App\Enums\Inosuit\CompanyPaperAnswerEnum;
use App\Enums\Inosuit\PaperStatusEnum;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Inosuit\Company\CreateApplicationRequest;
use App\Http\Requests\Inosuit\Company\CreateMeetingRequest;
use App\Http\Requests\Inosuit\Company\CreatePaperRequest;
use App\Http\Requests\Inosuit\Company\DeleteMeetingRequest;
use App\Http\Requests\Inosuit\Company\GetApplicationQuestionRequest;
use App\Http\Requests\Inosuit\Company\GetPaperTermRequest;
use App\Http\Requests\Inosuit\Company\GetPeriodRequest;
use App\Http\Requests\Inosuit\Company\GetCalendarRequest;
use App\Http\Requests\Inosuit\Company\GetPaperDetailRequest;
use App\Http\Requests\Inosuit\Company\GetProfileRequest;
use App\Http\Requests\Inosuit\Company\GetMeetingRequest;
use App\Http\Requests\Inosuit\Company\GetMentorRequest;
use App\Http\Requests\Inosuit\Company\GetPaperRequest;
use App\Http\Requests\Inosuit\Company\GetPaperQuestionRequest;
use App\Http\Requests\Inosuit\Company\UpdateCurrentPeriodRequest;
use App\Http\Requests\Inosuit\Company\UpdateMeetingRequest;
use App\Http\Requests\Inosuit\Company\UpdatePaperRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\Inosuit\CompanyService;
use App\Models\Activity;
use App\Models\Calendar;
use App\Models\Company;
use App\Models\CompanyInosuitApplication;
use App\Models\CompanyInosuitApplicationAnswer;
use App\Models\CompanyInosuitApplicationQuestion;
use App\Models\CompanyMeeting;
use App\Models\CompanyPaper;
use App\Models\CompanyPaperAnswer;
use App\Models\CompanyPaperMeeting;
use App\Models\CompanyPaperQuestion;
use App\Models\CompanyPeriod;
use App\Models\Mentor\MentorPaperMeeting;
use App\Models\Period;
use App\Models\User;
use App\Notifications\Inosuit\CompanyApplicationNotificationMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class CompanyController extends Controller
{
    private readonly CompanyService $companyService;

    public function __construct()
    {
        $this->companyService = new CompanyService();
    }

    public function getPeriods(GetPeriodRequest $request): JsonResponse
    {
        $result = $this->companyService->getPeriods($request);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getProfile(GetProfileRequest $request): JsonResponse
    {
        $company = Company::query()
            ->with([
                'currentPeriod.period',
                'currentPeriod.association',
                'currentPeriod.mentor.mentor.university',
                'currentPeriod.mentor.mentor.faculty',
                'currentPeriod.mentor.mentor.department',
                'currentPeriod.meetings',
            ])
            ->findOrFail(auth()->user()->company_id);
        if ($company->email && $company->phone && $company->website && $company->logo) {
            $company->company_is_completed = true;
        }
        $currentPeriod = $company->currentPeriod;
        if ($currentPeriod && $currentPeriod->full_name && $currentPeriod->email && $currentPeriod->phone) {
            $company->authorised_is_completed = true;
        }
        return (new GeneralResponse())->setData($company->toArray())->toJson();
    }

    public function getPapers(GetPaperRequest $request): JsonResponse
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();
        $papers = CompanyPaper::with('createdUser', 'companyPeriod.period', 'companyPeriod.association', 'companyPeriod.mentor')
            ->where('company_period_id', $currentPeriod->id)
            ->paginate($request->limit);
        foreach ($papers as $p) {
            $p->companyPeriod->makeHidden(['result_report', 'status']);
        }
        return (new GeneralResponse())
            ->setTotalCount($papers->total())
            ->setData($papers->items())
            ->toJson();
    }

    public function getPaperDetail(GetPaperDetailRequest $request): JsonResponse
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();
        $paper = CompanyPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.mentor',
                'companyPeriod.period',
                'companyPaperMeetings.meeting' => fn($q) => $q->select('id', 'name', 'location_type', 'start_date', 'end_date', 'company_status', 'mentor_status'),
                'companyPaperAnswers'
            ])
            ->where('company_period_id', $currentPeriod->id)
            ->findOrFail($request->paperId);
        $paper->meetings = $paper->companyPaperMeetings->map(fn($cpm) => $cpm->meeting);
        $paper->makeHidden(['companyPaperMeetings']);
        $paper->companyPeriod->makeHidden(['result_report', 'status']);
        return (new GeneralResponse())->setData($paper)->toJson();
    }

    public function getPaperQuestions(GetPaperQuestionRequest $request): JsonResponse
    {
        $data = [
            'questions' => CompanyPaperQuestion::query()->get(),
            'answers' => CompanyPaperAnswerEnum::getAnswers(),
        ];
        return (new GeneralResponse())->setData($data)->toJson();
    }

    public function getPaperTerms(GetPaperTermRequest $request): JsonResponse
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();
        $period = Period::query()->findOrFail($currentPeriod->period_id);
        $terms = [];

        $paperExistTermDates = CompanyPaper::query()
            ->where('company_period_id', $currentPeriod->id)
            ->pluck('term_date')
            ->map(fn($date) => Carbon::parse($date)->format('Y-m-d'))
            ->toArray();

        $startDate = Carbon::parse($period->start_date)->startOfMonth();
        $endDate = Carbon::parse($period->end_date)->startOfMonth();
        while ($startDate <= $endDate) {
            $formattedTermDate = $startDate->format('Y-m-d');
            $terms[] = [
                'name' => $startDate->locale('tr')->translatedFormat('F Y'),
                'termDate' => $formattedTermDate,
                'startDate' => $startDate->format('Y-m-d H:i:s'),
                'endDate' => $startDate->copy()->endOfMonth()->format('Y-m-d H:i:s'),
                'paperExist' => in_array($formattedTermDate, $paperExistTermDates),
            ];
            $startDate->addMonth();
        }
        return (new GeneralResponse())->setData($terms)->toJson();
    }

    public function getMentors(GetMentorRequest $request): JsonResponse
    {
        $mentors = User::query()
            ->with([
                'companyPeriods' => fn($q) => $q->where('company_id', auth()->user()->company_id),
                'companyPeriods.period',
            ])
            ->select('id', 'full_name', 'email', 'phone', 'photo')
            ->has('mentor')
            ->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::MENTOR))
            ->whereHas('companyPeriods', fn($q) => $q->where('company_id', auth()->user()->company_id))
            ->paginate($request->limit);
        foreach ($mentors as $m) {
            $m->period = $m->companyPeriods->first()->period;
            unset($m->companyPeriods);
        }
        return (new GeneralResponse())
            ->setTotalCount($mentors->total())
            ->setData($mentors->items())
            ->toJson();
    }

    public function getMeetings(GetMeetingRequest $request): JsonResponse
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();
        $meetings = CompanyMeeting::with('companyPeriod.mentor')
            ->where('company_period_id', $currentPeriod->id)
            ->whereNot(function ($q) use ($request) {
                if (!$request->startDate || !$request->endDate) {
                    return $q;
                }
                return $q
                    ->where('start_date', '>', $request->startDate)
                    ->where('start_date', '>', $request->endDate)
                    ->orWhere('end_date', '<', $request->startDate)
                    ->where('end_date', '<', $request->endDate);
            })
            ->paginate($request->limit);
        foreach ($meetings as $m) {
            $m->companyPeriod->makeHidden(['result_report', 'status']);
        }
        return (new GeneralResponse())
            ->setTotalCount($meetings->total())
            ->setData($meetings->items())
            ->toJson();
    }

    public function getCalendars(GetCalendarRequest $request): JsonResponse
    {
        if (!$request->periodId) {
            $periods = $this->getPeriods(new GetPeriodRequest())->getData()->data;
            if (count($periods) > 0) {
                $request->merge(['periodId' => $periods[0]->id]);
            }
        }
        $period = CompanyPeriod::query()
            ->where('company_id', auth()->user()->company_id)
            ->where('period_id', $request->periodId)
            ->firstOrFail();
        $calendars = Calendar::query()
            ->where('period_id', $request->periodId)
            ->orderBy('order')
            ->get();
        $currentCalendar = $calendars->first(fn($c) => $c->id == $period->calendar_id);
        if ($currentCalendar) {
            $currentCalendar->is_current = true;
        }
        return (new GeneralResponse())->setData($calendars)->toJson();
    }

    public function getApplicationQuestions(GetApplicationQuestionRequest $request): JsonResponse
    {
        $questions = CompanyInosuitApplicationQuestion::query()->orderBy('order')->get();
        return (new GeneralResponse())->setData($questions)->toJson();
    }

    public function createApplication(CreateApplicationRequest $request): JsonResponse
    {
        $now = now();
        DB::beginTransaction();
        try {
            $application = new CompanyInosuitApplication();
            $application->sector_id = $request->sectorId;
            $application->association_id = $request->associationId;
            $application->period_id = $request->periodId;
            $application->name = $request->name;
            $application->tax_number = $request->taxNumber;
            $application->email = $request->email;
            $application->phone = $request->phone;
            $application->website = $request->website;
            $application->address = $request->address;
            $application->activity = $request->activity;
            $application->found_date = $request->foundDate;
            $application->white_collar_count = $request->whiteCollarCount;
            $application->blue_collar_count = $request->blueCollarCount;
            $application->annual_sale = $request->annualSale;
            $application->balance_sheet = $request->balanceSheet;
            $application->is_kosgeb = $request->isKosgeb;
            $application->is_arge = $request->isArge;
            $application->manager_full_name = $request->manager['fullName'];
            $application->manager_email = $request->manager['email'];
            $application->manager_phone = $request->manager['phone'];
            $application->manager_title = $request->manager['title'];
            $application->contact_full_name = $request->contact['fullName'];
            $application->contact_email = $request->contact['email'];
            $application->contact_phone = $request->contact['phone'];
            $application->contact_title = $request->contact['title'];
            $application->save();

            if (isset($request->answers)) {
                CompanyInosuitApplicationAnswer::query()->insert(array_map(fn($a) => [
                    'application_id' => $application->id,
                    'question_id' => $a['questionId'],
                    'answer' => $a['answer'] ?? null,
                    'answer_text' => $a['answerText'] ?? null,
                    'created_at' => $now,
                    'updated_at' => $now,
                ], $request->answers));
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }

        if (isset($application->id)) {
            try {
                $notificationEmail = env('NOTIFICATION_INOSUIT_EMAIL');
                Notification::route('mail', $notificationEmail)->notify(new CompanyApplicationNotificationMail($application));
            } catch (\Exception $e) {
                Log::error($e);
            }
        }
        return (new GeneralResponse())->toJson();
    }

    public function createPaper(CreatePaperRequest $request): JsonResponse
    {
        $paperCount = CompanyPaper::query()
            ->where('company_period_id', $request->currentPeriod->id)
            ->count();
        $month = mb_strtolower(Carbon::parse($request->termDate)->locale(app()->getLocale())->translatedFormat('M'));
        $meetings = [];
        $answers = [];

        DB::beginTransaction();
        try {
            $paper = new CompanyPaper();
            $paper->created_user_id = auth()->id();
            $paper->company_period_id = $request->currentPeriod->id;
            $paper->name = $month . '_f_' . ($paperCount + 1);
            $paper->observation = $request->observation;
            $paper->term_date = $request->termDate;
            $paper->save();
            foreach ($request->meetingIds as $meetingId) {
                $meetings[] = [
                    'company_paper_id' => $paper->id,
                    'meeting_id' => $meetingId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            foreach ($request->answers as $answer) {
                $answers[] = [
                    'company_paper_id' => $paper->id,
                    'question_id' => $answer['questionId'],
                    'answer_type' => $answer['answerType'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            CompanyPaperMeeting::query()->insert($meetings);
            CompanyPaperAnswer::query()->insert($answers);

            $effectedUserIds = User::query()
                ->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::TIM_ADMIN))
                ->pluck('id')
                ->toArray();
            if (count($effectedUserIds) > 0) {
                Activity::createActivity(
                    ActivityEnum::PAPER_CREATE,
                    $effectedUserIds,
                    ['paperId' => $paper->id, 'companyPaper' => true]
                );
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function createMeeting(CreateMeetingRequest $request): JsonResponse
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();

        DB::beginTransaction();
        try {
            $meeting = new CompanyMeeting();
            $meeting->created_user_id = auth()->id();
            $meeting->company_period_id = $currentPeriod->id;
            $meeting->name = $request->name;
            $meeting->description = $request->description;
            $meeting->location = $request->location;
            $meeting->location_type = $request->locationType;
            $meeting->company_status = CompanyMeetingStatusEnum::APPROVED;
            $meeting->company_note = $request->note;
            $meeting->start_date = $request->startDate;
            $meeting->end_date = $request->endDate;
            $meeting->save();

            $mentorUserId = $currentPeriod->mentor_user_id;
            if ($mentorUserId) {
                Activity::createActivity(
                    ActivityEnum::MEETING_CREATE,
                    [$mentorUserId],
                    ['meetingId' => $meeting->id]
                );
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())
            ->setData(['id' => $meeting->id])
            ->toJson();
    }

    public function updatePaper(UpdatePaperRequest $request): JsonResponse
    {
        $paper = CompanyPaper::query()
            ->whereIn('status', [PaperStatusEnum::DECLINED, PaperStatusEnum::PENDING])
            ->findOrFail($request->paperId);
        if ($request->termDate) {
            $month = mb_strtolower(Carbon::parse($request->termDate)->locale(app()->getLocale())->translatedFormat('M'));
            $paperCode = collect(explode('_', $paper->name))->pop();
            $name = $month . '_f_' . $paperCode;
        }
        $meetings = [];
        $answers = [];

        DB::beginTransaction();
        try {
            $paper->name = $name ?? $paper->name;
            $paper->observation = $request->observation ?? $paper->observation;
            $paper->status = PaperStatusEnum::PENDING;
            $paper->term_date = $request->termDate ?? $paper->term_date;
            $paper->save();
            if ($request->meetingIds) {
                CompanyPaperMeeting::query()->where('company_paper_id', $paper->id)->delete();
                foreach ($request->meetingIds as $meetingId) {
                    $meetings[] = [
                        'company_paper_id' => $paper->id,
                        'meeting_id' => $meetingId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
            if ($request->answers) {
                CompanyPaperAnswer::query()->where('company_paper_id', $paper->id)->delete();
                foreach ($request->answers as $answer) {
                    $answers[] = [
                        'company_paper_id' => $paper->id,
                        'question_id' => $answer['questionId'],
                        'answer_type' => $answer['answerType'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
            CompanyPaperMeeting::query()->insert($meetings);
            CompanyPaperAnswer::query()->insert($answers);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateCurrentPeriod(UpdateCurrentPeriodRequest $request): JsonResponse
    {
        $currentPeriod = CompanyPeriod::getCurrentPeriod();
        $currentPeriod->full_name = $request->fullName ?? $currentPeriod->full_name;
        $currentPeriod->email = $request->email ?? $currentPeriod->email;
        $currentPeriod->phone = $request->phone ?? $currentPeriod->phone;
        $currentPeriod->title = $request->title ?? $currentPeriod->title;
        $currentPeriod->save();
        return (new GeneralResponse())->toJson();
    }

    public function updateMeeting(UpdateMeetingRequest $request): JsonResponse
    {
        $meeting = CompanyMeeting::query()->findOrFail($request->meetingId);
        $meeting->name = $request->has('name') && !$request->name ? null : ($request->name ?? $meeting->name);
        $meeting->description = $request->has('description') && !$request->description ? null : ($request->description ?? $meeting->description);
        $meeting->location = $request->has('location') && !$request->location ? null : ($request->location ?? $meeting->location);
        $meeting->location_type = $request->locationType ?? $meeting->location_type;
        $meeting->company_status = $request->companyStatus ?? $meeting->company_status;
        $meeting->company_note = $request->has('companyNote') && !$request->companyNote ? null : ($request->companyNote ?? $meeting->company_note);
        $meeting->start_date = $request->startDate ?? $meeting->start_date;
        $meeting->end_date = $request->endDate ?? $meeting->end_date;
        $meeting->save();
        return (new GeneralResponse())->toJson();
    }

    public function deleteMeeting(DeleteMeetingRequest $request): JsonResponse
    {
        $meeting = CompanyMeeting::query()
            ->where('created_user_id', auth()->id())
            ->findOrFail($request->meetingId);

        DB::beginTransaction();
        try {
            CompanyPaperMeeting::query()
                ->where('meeting_id', $meeting->id)
                ->delete();
            MentorPaperMeeting::query()
                ->where('meeting_id', $meeting->id)
                ->delete();
            $meeting->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }
}
