<?php

namespace App\Http\Controllers\Inosuit;

use App\Enums\ActivityEnum;
use App\Enums\FrontProjectEnum;
use App\Enums\Inosuit\CompanyPeriodStatusEnum;
use App\Enums\Inosuit\InosuitApplicationStatusEnum;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\RoleEnum;
use App\Enums\UserEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Inosuit\TimAdmin\GetApplicationExportRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetPeriodRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetPeriodReportExportRequest;
use App\Http\Requests\Inosuit\TimAdmin\CreateCalendarRequest;
use App\Http\Requests\Inosuit\TimAdmin\CreatePeriodRequest;
use App\Http\Requests\Inosuit\TimAdmin\DeleteCalendarRequest;
use App\Http\Requests\Inosuit\TimAdmin\DeletePeriodRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCalendarCompanyRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCalendarRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCompanyApplicationDetailRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCompanyApplicationRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCompanyPaperDetailRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCompanyProfileRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetCompanyRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetMeetingRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetMentorApplicationDetailRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetMentorApplicationRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetMentorPaperDetailRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetMentorProfileRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetMentorRequest;
use App\Http\Requests\Inosuit\TimAdmin\GetPaperRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateAssociationRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateCalendarRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateCompanyApplicationRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateCompanyPaperRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateCompanyRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateMentorApplicationRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateMentorPaperRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdateMentorRequest;
use App\Http\Requests\Inosuit\TimAdmin\UpdatePeriodRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\Inosuit\TimAdminService;
use App\Models\Activity;
use App\Models\Association;
use App\Models\Calendar;
use App\Models\Company;
use App\Models\CompanyInosuitApplication;
use App\Models\CompanyMeeting;
use App\Models\CompanyPaper;
use App\Models\CompanyPeriod;
use App\Models\Mentor\Mentor;
use App\Models\Mentor\MentorInosuitApplication;
use App\Models\Mentor\MentorPaper;
use App\Models\Period;
use App\Models\PeriodLane;
use App\Models\Role;
use App\Models\RolePrivilege;
use App\Models\User;
use App\Notifications\Inosuit\ApplicationApprovedMail;
use App\Notifications\Inosuit\ApplicationDeniedMail;
use App\Utils\GeneralUtil;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TimAdminController extends Controller
{
    private readonly TimAdminService $timAdminService;

    public function __construct()
    {
        $this->timAdminService = new TimAdminService();
    }

    public function getPeriods(GetPeriodRequest $request): JsonResponse
    {
        $result = $this->timAdminService->getPeriods($request);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getPapers(GetPaperRequest $request): JsonResponse
    {
        $companyPapers = CompanyPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.lane',
                'companyPeriod.association',
                'companyPeriod.mentor',
            ])
            ->select('id', 'created_user_id', 'company_period_id', 'name', 'status', 'term_date')
            ->has('companyPeriod')
            ->where(
                fn($q) => isset($request->search) ? $q
                    ->orWhereHas('companyPeriod.company', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.period', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.lane', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.association', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.mentor', fn($q) => $q->where('full_name', 'LIKE', "%$request->search%"))
                    : $q
            )
            ->whereHas('companyPeriod', fn($q) => isset($request->companyIds) ? $q->whereIn('company_id', explode(',', $request->companyIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->laneIds) ? $q->whereIn('lane_id', explode(',', $request->laneIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->associationIds) ? $q->whereIn('association_id', explode(',', $request->associationIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->get();
        $mentorPapers = MentorPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.lane',
                'companyPeriod.association',
                'companyPeriod.mentor',
            ])
            ->select('id', 'created_user_id', 'company_period_id', 'name', 'status', 'term_date')
            ->has('companyPeriod')
            ->where(
                fn($q) => isset($request->search) ? $q
                    ->orWhereHas('companyPeriod.company', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.period', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.lane', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.association', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.mentor', fn($q) => $q->where('full_name', 'LIKE', "%$request->search%"))
                    : $q
            )
            ->whereHas('companyPeriod', fn($q) => isset($request->companyIds) ? $q->whereIn('company_id', explode(',', $request->companyIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->laneIds) ? $q->whereIn('lane_id', explode(',', $request->laneIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->associationIds) ? $q->whereIn('association_id', explode(',', $request->associationIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->get();
        $data = [];
        $addedMentorPaperIds = [];
        foreach ($companyPapers as $p) {
            $p->companyPaper = (object)[
                'id' => $p->id,
                'name' => $p->name,
                'status' => $p->status,
                'term_date' => $p->term_date,
                'created_user' => $p->createdUser,
            ];
            $termDate = date('Y-m', strtotime($p->term_date));
            $p->mentorPaper = $mentorPapers->first(fn($mp) => $p->company_period_id == $mp->company_period_id && date('Y-m', strtotime($mp->term_date)) === $termDate);
            if ($p->mentorPaper) {
                $p->mentorPaper = (object)[
                    'id' => $p->mentorPaper->id,
                    'name' => $p->mentorPaper->name,
                    'status' => $p->mentorPaper->status,
                    'term_date' => $p->mentorPaper->term_date,
                    'created_user' => $p->mentorPaper->createdUser,
                ];
                $addedMentorPaperIds[] = $p->mentorPaper->id;
            }
            $p->makeHidden(['id', 'name', 'status', 'term_date', 'createdUser']);
            $p->companyPeriod->makeHidden(['result_report', 'status']);
            $data[] = $p;
        }
        $selfMentorPapers = $mentorPapers->whereNotIn('id', $addedMentorPaperIds);
        foreach ($selfMentorPapers as $p) {
            $p->companyPaper = null;
            $p->mentorPaper = [
                'id' => $p->id,
                'name' => $p->name,
                'status' => $p->status,
                'term_date' => $p->term_date,
                'created_user' => $p->createdUser,
            ];
            $p->makeHidden(['id', 'name', 'status', 'term_date', 'createdUser']);
            $p->companyPeriod->makeHidden(['result_report', 'status']);
            $data[] = $p;
        }
        return (new GeneralResponse())
            ->setData(array_values($data))
            ->toJson();
    }

    public function getCompanies(GetCompanyRequest $request): JsonResponse
    {
        $companyPeriods = CompanyPeriod::query()
            ->select('id', 'id AS company_period_id', 'company_id', 'period_id', 'lane_id', 'association_id', 'mentor_user_id', 'status')
            ->with([
                'company' => fn($q) => $q->select('id', 'name'),
                'company.inosuitApplication.answers',
                'period',
                'lane',
                'association',
                'mentor',
                'companyPapers' => fn($q) => $q->select('id', 'company_period_id', 'name', 'status'),
                'mentorPapers' => fn($q) => $q->select('id', 'company_period_id', 'name', 'status'),
            ])
            ->where(fn($q) => isset($request->companyIds) ? $q->whereIn('company_id', explode(',', $request->companyIds)) : $q)
            ->whereHas('company', fn($q) => isset($request->isActiveStatuses) ? $q->whereIn('is_active', explode(',', $request->isActiveStatuses)) : $q)
            ->whereHas('company', fn($q) => isset($request->search) ? $q->where('name', 'LIKE', "%$request->search%") : $q)
            ->where(fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->where(fn($q) => isset($request->laneIds) ? $q->whereIn('lane_id', explode(',', $request->laneIds)) : $q)
            ->where(fn($q) => isset($request->associationIds) ? $q->whereIn('association_id', explode(',', $request->associationIds)) : $q)
            ->where(fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->orderBy('company_id')
            ->orderBy('period_id')
            ->paginate($request->limit);
        foreach ($companyPeriods as $cp) {
            $cp->makeHidden('id');
            $cp->company->makeHidden('inosuitApplication');
            $cp->company['answerPoint'] = $cp->company->inosuitApplication?->answers?->sum('answer') ?? 0;
        }
        return (new GeneralResponse())
            ->setTotalCount($companyPeriods->total())
            ->setData($companyPeriods->items())
            ->toJson();
    }

    public function getCompanyPaperDetail(GetCompanyPaperDetailRequest $request): JsonResponse
    {
        $paper = CompanyPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.mentor',
                'companyPaperMeetings.meeting',
                'companyPaperAnswers'
            ])
            ->has('companyPeriod')
            ->findOrFail($request->paperId);
        $paper->meetings = $paper->companyPaperMeetings->map(fn($cpm) => $cpm->meeting);
        $paper->makeHidden(['companyPaperMeetings']);
        $paper->companyPeriod->makeHidden(['result_report', 'status']);
        return (new GeneralResponse())->setData($paper)->toJson();
    }

    public function getCompanyApplications(GetCompanyApplicationRequest $request): JsonResponse
    {
        $applications = CompanyInosuitApplication::query()
            ->with(['sector', 'association', 'period', 'answers'])
            ->where(fn($q) => isset($request->sectorIds) ? $q->whereIn('sector_id', explode(',', $request->sectorIds)) : $q)
            ->where(fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->where(fn($q) => isset($request->associationIds) ? $q->whereIn('association_id', explode(',', $request->associationIds)) : $q)
            ->where(fn($q) => isset($request->statuses) ? $q->whereIn('status', explode(',', $request->statuses)) : $q)
            ->orderByDesc('created_at')
            ->paginate($request->limit);
        foreach ($applications as $application) {
            $application->answerPoint = $application->answers->sum('answer');
            $application->makeHidden('answers');
        }
        return (new GeneralResponse())
            ->setTotalCount($applications->total())
            ->setData($applications->items())
            ->toJson();
    }

    public function getCompanyApplicationDetail(GetCompanyApplicationDetailRequest $request): JsonResponse
    {
        $application = CompanyInosuitApplication::query()
            ->with(['sector', 'association', 'period', 'answers.question'])
            ->findOrFail($request->applicationId);
        return (new GeneralResponse())->setData($application->toArray())->toJson();
    }

    public function getCompanyProfile(GetCompanyProfileRequest $request): JsonResponse
    {
        $company = Company::query()
            ->select(
                'id',
                'name',
                'description',
                'sector_id',
                'is_active',
                'tax_number',
                'email',
                'phone',
                'website',
                'linkedin_url',
                'twitter_url',
                'logo',
                'address'
            )
            ->with([
                'sector',
                'inosuitApplication.answers.question',
                'companyPeriods.period',
                'companyPeriods.lane',
                'companyPeriods.mentor',
                'companyPeriods.association',
            ])
            ->findOrFail($request->companyId);
        $company->companyPeriods->makeHidden(['result_report', 'status']);
        return (new GeneralResponse())->setData($company->toArray())->toJson();
    }

    public function getMentors(GetMentorRequest $request): JsonResponse
    {
        $mentors = User::query()
            ->with([
                'companyPeriods' => fn($q) => $q->select('company_id', 'period_id', 'mentor_user_id'),
                'companyPeriods.company' => fn($q) => $q->select('id', 'name'),
                'mentor.university',
                'mentor.faculty',
                'mentor.department',
            ])
            ->select('id', 'full_name', 'email', 'phone', 'photo')
            ->has('mentor')
            ->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::MENTOR))
            ->whereHas('mentor', fn($q) => isset($request->universityIds) ? $q->whereIn('university_id', explode(',', $request->universityIds)) : $q)
            ->whereHas('mentor', fn($q) => isset($request->facultyIds) ? $q->whereIn('faculty_id', explode(',', $request->facultyIds)) : $q)
            ->whereHas('mentor', fn($q) => isset($request->departmentIds) ? $q->whereIn('department_id', explode(',', $request->departmentIds)) : $q)
            ->whereHas('mentor', fn($q) => isset($request->isActiveStatuses) ? $q->whereIn('is_active', explode(',', $request->isActiveStatuses)) : $q)
            ->where(function ($q) use ($request) {
                return isset($request->companyIds) ? $q->whereHas('companyPeriods', fn($q) => $q->whereIn('company_id', explode(',', $request->companyIds))) : $q;
            })
            ->get();
        foreach ($mentors as $m) {
            $m->companies = $m->companyPeriods->map(fn($cp) => $cp->company);
            $m->cv_url = $m->mentor->cv_url;
            $m->is_active = $m->mentor->is_active;
            $m->university = $m->mentor->university;
            $m->faculty = $m->mentor->faculty;
            $m->department = $m->mentor->department;
            unset($m->companyPeriods);
            unset($m->mentor);
        }
        return (new GeneralResponse())->setData($mentors->toArray())->toJson();
    }

    public function getMentorPaperDetail(GetMentorPaperDetailRequest $request): JsonResponse
    {
        $paper = MentorPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.mentor',
                'mentorPaperMeetings.meeting',
            ])
            ->has('companyPeriod')
            ->findOrFail($request->paperId);
        $paper->meetings = $paper->mentorPaperMeetings->map(fn($mpm) => $mpm->meeting);
        $paper->makeHidden(['mentorPaperMeetings']);
        $paper->companyPeriod->makeHidden(['result_report', 'status']);
        return (new GeneralResponse())->setData($paper)->toJson();
    }

    public function getMentorApplications(GetMentorApplicationRequest $request): JsonResponse
    {
        $applications = MentorInosuitApplication::query()
            ->with('university', 'faculty', 'department')
            ->where(fn($q) => isset($request->universityIds) ? $q->whereIn('university_id', explode(',', $request->universityIds)) : $q)
            ->where(fn($q) => isset($request->facultyIds) ? $q->whereIn('faculty_id', explode(',', $request->facultyIds)) : $q)
            ->where(fn($q) => isset($request->departmentIds) ? $q->whereIn('department_id', explode(',', $request->departmentIds)) : $q)
            ->where(fn($q) => isset($request->statuses) ? $q->whereIn('status', explode(',', $request->statuses)) : $q)
            ->orderByDesc('created_at')
            ->paginate($request->limit);
        return (new GeneralResponse())
            ->setTotalCount($applications->total())
            ->setData($applications->items())
            ->toJson();
    }

    public function getMentorApplicationDetail(GetMentorApplicationDetailRequest $request): JsonResponse
    {
        $application = MentorInosuitApplication::query()
            ->with(['university', 'faculty', 'department'])
            ->findOrFail($request->applicationId);
        return (new GeneralResponse())->setData($application->toArray())->toJson();
    }

    public function getMentorProfile(GetMentorProfileRequest $request): JsonResponse
    {
        $user = User::query()
            ->with([
                'mentor.university',
                'mentor.faculty',
                'mentor.department',
                'inosuitApplication',
                'companyPeriods.company'
            ])
            ->findOrFail($request->mentorUserId);
        $data = [
            'id' => $user->id,
            'full_name' => $user->full_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'title' => $user->title,
            'photo' => $user->photo,
            'mentor' => $user->mentor,
            'inosuit_application' => $user->inosuitApplication,
            'companies' => $user->companyPeriods->map(fn($cp) => ['id' => $cp->company->id, 'name' => $cp->company->name]),
        ];
        return (new GeneralResponse())->setData($data)->toJson();
    }

    public function getMeetings(GetMeetingRequest $request): JsonResponse
    {
        $meetings = CompanyMeeting::query()
            ->with([
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.mentor',
            ])
            ->whereHas('companyPeriod', fn($q) => isset($request->companyIds) ? $q->whereIn('company_id', explode(',', $request->companyIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->paginate($request->limit);
        foreach ($meetings as $m) {
            $m->companyPeriod->makeHidden(['result_report', 'status']);
        }
        return (new GeneralResponse())
            ->setTotalCount($meetings->total())
            ->setData($meetings->items())
            ->toJson();
    }

    public function getCalendars(GetCalendarRequest $request): JsonResponse
    {
        $periodId = $request->periodId;
        if (!$periodId) {
            $periods = $this->getPeriods(new GetPeriodRequest())->getData()->data;
            $periodId = $periods[0]->id;
        }
        $periods = CompanyPeriod::query()
            ->with([
                'company' => fn($q) => $q->select('id', 'name'),
            ])
            ->where('period_id', $periodId)
            ->get();
        $calendars = Calendar::query()
            ->where('period_id', $periodId)
            ->orderBy('order')
            ->get();
        foreach ($calendars as $cal) {
            $cal->companyCount = $periods
                ->filter(fn($p) => $p->calendar_id == $cal->id)
                ->count();
        }
        return (new GeneralResponse())->setData($calendars)->toJson();
    }

    public function getCalendarCompanies(GetCalendarCompanyRequest $request): JsonResponse
    {
        $companies = CompanyPeriod::query()
            ->with([
                'company' => fn($q) => $q->select('id', 'name'),
            ])
            ->where('period_id', $request->periodId)
            ->where('calendar_id', $request->calendarId)
            ->get()
            ->pluck('company');
        return (new GeneralResponse())->setData($companies->toArray())->toJson();
    }

    public function getPeriodReportExport(GetPeriodReportExportRequest $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return $this->timAdminService->exportPeriodReports($request);
    }

    public function getApplicationExport(GetApplicationExportRequest $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $spreadsheet = new Spreadsheet();

        $this->getCompanyApplicationsForExport($spreadsheet);
        $this->getMentorApplicationsForExport($spreadsheet);

        $spreadsheet->setActiveSheetIndex(0);
        $filename = 'applications.xlsx';
        $writer = new Xlsx($spreadsheet);
        $temp_file = tempnam(sys_get_temp_dir(), $filename);
        $writer->save($temp_file);

        return Response::download($temp_file, $filename)->deleteFileAfterSend();
    }

    private function getCompanyApplicationsForExport(Spreadsheet $spreadsheet): void
    {
        $frontProject = GeneralUtil::getFrontProject();
        $applications = CompanyInosuitApplication::query()
            ->with(['sector', 'association', 'period', 'answers.question'])
            ->orderByDesc('created_at')
            ->get();

        $sheet = $spreadsheet->setActiveSheetIndex(0);
        $sheet->setTitle('Firma Başvuruları');

        $sheet->setCellValue('A1', 'Firma Dönemi');
        $sheet->setCellValue('B1', 'Firma Ünvanı');
        $sheet->setCellValue('C1', 'Vergi Numarası');
        $sheet->setCellValue('D1', 'E-posta');
        $sheet->setCellValue('E1', 'Telefon');
        $sheet->setCellValue('F1', 'Website');
        $sheet->setCellValue('G1', 'Adres');
        $sheet->setCellValue('H1', 'Firma Sektörü');
        $sheet->setCellValue('I1', 'Faaliyet Alanları');
        $sheet->setCellValue('J1', 'Firma Kuruluş Tarihi');
        $sheet->setCellValue('K1', 'Firma Çalışan Sayısı');
        $sheet->setCellValue('L1', 'Yıllık Net Satış Hasılatı');
        $sheet->setCellValue('M1', 'Firma Yıllık Mali Bilanço Toplamı');
        $sheet->setCellValue('N1', 'Kosgeb Üyesi');
        $sheet->setCellValue('O1', 'Destek Almak İstediği İhracatçı Birliği');
        $sheet->setCellValue('P1', 'Yetkili İsim Soyisim');
        $sheet->setCellValue('Q1', 'Yetkili Eposta');
        $sheet->setCellValue('R1', 'Yetkili Telefon');
        $sheet->setCellValue('S1', 'Yetkili Ünvan');
        $sheet->setCellValue('T1', 'İletişim İsim Soyisim');
        $sheet->setCellValue('U1', 'İletişim Eposta');
        $sheet->setCellValue('V1', 'İletişim Telefon');
        $sheet->setCellValue('W1', 'İletişim Ünvan');

        if ($frontProject === FrontProjectEnum::ECOTIM) {
            $sheet->setCellValue('X1', 'Başvuru Durumu');
            $sheet->setCellValue('Y1', 'Başvuru Tarihi');
        } else {
            $sheet->setCellValue('X1', 'Değerlendirme Puanı');
            $sheet->setCellValue('Y1', 'Başvuru Durumu');
            $sheet->setCellValue('Z1', 'Başvuru Tarihi');
        }

        $row = 2;
        foreach ($applications as $a) {
            if (!$a->period) {
                continue;
            }
            $answerPoint = $a->answers->sum('answer');

            $sheet->setCellValue('A' . $row, $a->period->name);
            $sheet->setCellValue('B' . $row, $a->name);
            $sheet->setCellValue('C' . $row, $a->tax_number);
            $sheet->setCellValue('D' . $row, $a->email);
            $sheet->setCellValue('E' . $row, $a->phone);
            $sheet->setCellValue('F' . $row, $a->website);
            $sheet->setCellValue('G' . $row, $a->address);
            $sheet->setCellValue('H' . $row, $a->sector->name);
            $sheet->setCellValue('I' . $row, $a->activity);
            $sheet->setCellValue('J' . $row, Carbon::parse($a->found_date)->format('d.m.Y'));
            $sheet->setCellValue('K' . $row, "$a->white_collar_count Beyaz Yaka, $a->blue_collar_count Mavi Yaka");
            $sheet->setCellValue('L' . $row, $a->annual_sale);
            $sheet->setCellValue('M' . $row, $a->balance_sheet);
            $sheet->setCellValue('N' . $row, $a->is_kosgeb ? 'Evet' : 'Hayır');
            $sheet->setCellValue('O' . $row, $a->association->name);
            $sheet->setCellValue('P' . $row, $a->manager_full_name);
            $sheet->setCellValue('Q' . $row, $a->manager_email);
            $sheet->setCellValue('R' . $row, $a->manager_phone);
            $sheet->setCellValue('S' . $row, $a->manager_title);
            $sheet->setCellValue('T' . $row, $a->contact_full_name);
            $sheet->setCellValue('U' . $row, $a->contact_email);
            $sheet->setCellValue('V' . $row, $a->contact_phone);
            $sheet->setCellValue('W' . $row, $a->contact_title);

            if ($frontProject === FrontProjectEnum::ECOTIM) {
                $sheet->setCellValue('X' . $row, $a->status === 1 ? 'Onaylandı' : ($a->status === -1 ? 'Reddedildi' : 'Beklemede'));
                $sheet->setCellValue('Y' . $row, GeneralUtil::formatDate($a->created_at));
            } else {
                $sheet->setCellValue('X' . $row, $answerPoint);
                $sheet->setCellValue('Y' . $row, $a->status === 1 ? 'Onaylandı' : ($a->status === -1 ? 'Reddedildi' : 'Beklemede'));
                $sheet->setCellValue('Z' . $row, GeneralUtil::formatDate($a->created_at));
            }
            $row++;
        }

        // Question Answers
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Firma Cevapları');

        $sheet->setCellValue('A1', 'Firma Ünvanı');
        $sheet->setCellValue('B1', 'Soru');
        $sheet->setCellValue('C1', 'Cevap');

        $row = 2;
        foreach ($applications as $a) {
            if ($a->answers->count() == 0) {
                continue;
            }
            foreach ($a->answers as $i => $answer) {
                $answerValue = $answer->answer;
                if ($frontProject === FrontProjectEnum::ECOTIM) {
                    $answerValue = $answer->answer_text ?? ($answer->answer ? 'Evet' : 'Hayır');
                }
                if ($i === 0) {
                    $sheet->setCellValue('A' . $row, $a->name);
                }
                $sheet->setCellValue('B' . $row, $answer->question->name);
                $sheet->setCellValue('C' . $row, $answerValue);
                $row++;
            }
            $row++;
        }
    }

    private function getMentorApplicationsForExport(Spreadsheet $spreadsheet): void
    {
        $frontProject = GeneralUtil::getFrontProject();
        $applications = MentorInosuitApplication::query()
            ->with('university', 'faculty', 'department')
            ->orderByDesc('created_at')
            ->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Mentor Başvuruları');

        $sheet->setCellValue('A1', 'Mentor');
        $sheet->setCellValue('B1', 'Eposta');
        $sheet->setCellValue('C1', 'Telefon');
        $sheet->setCellValue('D1', 'Ünvan');
        $sheet->setCellValue('E1', 'Linkedin');
        $sheet->setCellValue('F1', 'CV');

        if ($frontProject === FrontProjectEnum::ECOTIM) {
            $sheet->setCellValue('G1', 'Bağlı Olduğunuz Kurum');
            $sheet->setCellValue('H1', 'Kurum Tam Adı');
            $sheet->setCellValue('I1', 'Kaç Yıllık Profesyonel İş Deneyiminiz Mevcuttur?');
            $sheet->setCellValue('J1', 'Sürdürülebilirlik Faaliyet Alanında Çalışmalarınız Mevcut Mudur?');
            $sheet->setCellValue('K1', 'Sürdürülebilirlik Faaliyet Alanında Çalışmalarınızı Açıklayınız');
            $sheet->setCellValue('L1', 'ISO/TSE Onaylı Sertifikalarınızı Yazınız');
            $sheet->setCellValue('M1', 'Başvuru Durumu');
            $sheet->setCellValue('N1', 'Başvuru Tarihi');
        } else {
            $sheet->setCellValue('G1', 'Üniversite');
            $sheet->setCellValue('H1', 'Fakülte');
            $sheet->setCellValue('I1', 'Bölüm');
            $sheet->setCellValue('J1', 'Başvuru Durumu');
            $sheet->setCellValue('K1', 'Başvuru Tarihi');
        }

        $row = 2;
        foreach ($applications as $a) {
            $sheet->setCellValue('A' . $row, $a->full_name);
            $sheet->setCellValue('B' . $row, $a->email);
            $sheet->setCellValue('C' . $row, $a->phone);
            $sheet->setCellValue('D' . $row, $a->title);
            $sheet->setCellValue('E' . $row, $a->linkedin_url);
            $sheet->setCellValue('F' . $row, $a->cv_url);

            if ($frontProject === FrontProjectEnum::ECOTIM) {
                $sheet->setCellValue('G' . $row, $a->organization_type);
                $sheet->setCellValue('H' . $row, $a->organization_name);
                $sheet->setCellValue('I' . $row, $a->experience_year);
                $sheet->setCellValue('J' . $row, $a->is_sustainable ? 'Evet' : 'Hayır');
                $sheet->setCellValue('K' . $row, $a->sustainable_text);
                $sheet->setCellValue('L' . $row, $a->iso_certificate_text);
                $sheet->setCellValue('M' . $row, $a->status === 1 ? 'Onaylandı' : ($a->status === -1 ? 'Reddedildi' : 'Beklemede'));
                $sheet->setCellValue('N' . $row, GeneralUtil::formatDate($a->created_at));
            } else {
                $sheet->setCellValue('G' . $row, $a->university?->name);
                $sheet->setCellValue('H' . $row, $a->faculty?->name);
                $sheet->setCellValue('I' . $row, $a->department?->name);
                $sheet->setCellValue('J' . $row, $a->status === 1 ? 'Onaylandı' : ($a->status === -1 ? 'Reddedildi' : 'Beklemede'));
                $sheet->setCellValue('K' . $row, GeneralUtil::formatDate($a->created_at));
            }
            $row++;
        }
    }

    public function createPeriod(CreatePeriodRequest $request): JsonResponse
    {
        $period = new Period();
        $period->name = $request->name;
        $period->start_date = $request->startDate;
        $period->end_date = $request->endDate;
        $period->save();

        return (new GeneralResponse())->toJson();
    }

    public function createCalendar(CreateCalendarRequest $request): JsonResponse
    {
        $maxOrder = Calendar::query()
            ->where('period_id', $request->periodId)
            ->max('order');

        $calendar = new Calendar();
        $calendar->period_id = $request->periodId;
        $calendar->name = $request->name;
        $calendar->description = $request->description;
        $calendar->goal = $request->goal;
        $calendar->advice = $request->advice;
        $calendar->term = $request->term;
        $calendar->tool = $request->tool;
        $calendar->source = $request->source;
        $calendar->order = $maxOrder ? $maxOrder + 1 : 1;
        $calendar->save();

        return (new GeneralResponse())->toJson();
    }

    public function updateCompany(UpdateCompanyRequest $request): JsonResponse
    {
        $companyPeriod = CompanyPeriod::query()->findOrFail($request->companyPeriodId);
        if ($request->laneId) {
            PeriodLane::query()
                ->where('period_id', $companyPeriod->period_id)
                ->findOrFail($request->laneId);
        }
        $companyPeriod->status = $request->status ?? $companyPeriod->status;
        $companyPeriod->lane_id = $request->laneId ?? $companyPeriod->lane_id;
        $companyPeriod->association_id = $request->associationId ?? $companyPeriod->association_id;
        $companyPeriod->mentor_user_id = $request->mentorUserId ?? $companyPeriod->mentor_user_id;
        $companyPeriod->save();

        return (new GeneralResponse())->toJson();
    }

    public function updateCompanyPaper(UpdateCompanyPaperRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $paper = CompanyPaper::query()->findOrFail($request->paperId);
            $paper->status = $request->status ?? $paper->status;
            $paper->save();

            $companyId = $paper->companyPeriod->company_id;
            $effectedUserIds = User::query()->where('company_id', $companyId)->pluck('id')->toArray();
            if (count($effectedUserIds) > 0) {
                Activity::createActivity(
                    ActivityEnum::PAPER_UPDATE,
                    $effectedUserIds,
                    ['paperId' => $paper->id, 'status' => $paper->status, 'companyPaper' => true]
                );
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateCompanyApplication(UpdateCompanyApplicationRequest $request): JsonResponse
    {
        $application = CompanyInosuitApplication::with('answers')
            ->whereIn('status', [InosuitApplicationStatusEnum::DECLINED, InosuitApplicationStatusEnum::PENDING])
            ->findOrFail($request->applicationId);
        if (!$request->isApprove) {
            if ($application->status == InosuitApplicationStatusEnum::DECLINED->value) {
                throw new BadRequestHttpException('Başvuru zaten reddedilmiştir.');
            }
            $application->status = InosuitApplicationStatusEnum::DECLINED;
            $application->save();
            Notification::route('mail', $application->manager_email)->notify(new ApplicationDeniedMail($application->manager_full_name));
            if ($application->manager_email != $application->contact_email) {
                Notification::route('mail', $application->contact_email)->notify(new ApplicationDeniedMail($application->contact_full_name));
            }
            return (new GeneralResponse())->toJson();
        }
        $managerRoleId = Role::query()
            ->where('slug', '=', RoleEnum::MANAGER)
            ->firstOrFail()
            ->id;
        $assistantRoleId = Role::query()
            ->where('slug', '=', RoleEnum::ASSISTANT)
            ->firstOrFail()
            ->id;
        $privilegeId = RolePrivilege::query()
            ->where('slug', '=', RolePrivilegeEnum::COMPANY)
            ->firstOrFail()
            ->id;

        $managerUser = User::with('company')
            ->where('email', $application->manager_email)
            ->first();
        $contactUser = User::with('company')
            ->where('email', $application->contact_email)
            ->first();
        if ($managerUser && $contactUser && $managerUser->company_id != $contactUser->company_id) {
            throw new BadRequestHttpException('Firma yöneticisi ve iletişim yetkilisi kullanıcıları sistemde mevcut ve farklı şirketlerde görünmektedir.');
        }
        $company = null;
        if ($managerUser) {
            $company = $managerUser->company;
        } else if ($contactUser) {
            $company = $contactUser->company;
        }
        if ($company) {
            $companyPeriod = CompanyPeriod::query()
                ->where('company_id', $company->id)
                ->where('period_id', $application->period_id)
                ->first();
            if ($companyPeriod) {
                throw new BadRequestHttpException('Firmanın bu dönem için zaten başvurusu onaylanmıştır.');
            }
        }
        $answerPoint = $application->answers->sum('answer');
        $laneId = null;
        if ($answerPoint < 35) {
            $laneId = PeriodLane::query()
                ->where('period_id', $application->period_id)
                ->where('is_preparation', true)
                ->first()?->id;
        }

        DB::beginTransaction();
        try {
            if (!$company) {
                $company = new Company();
                $company->name = $application->name;
                $company->description = $application->activity;
                $company->sector_id = $application->sector_id;
                $company->tax_number = $application->tax_number;
                $company->email = $application->email;
                $company->phone = $application->phone;
                $company->website = $application->website;
                $company->address = $application->address;
                $company->save();
            }

            $companyPeriod = new CompanyPeriod();
            $companyPeriod->company_id = $company->id;
            $companyPeriod->period_id = $application->period_id;
            $companyPeriod->lane_id = $laneId;
            $companyPeriod->association_id = $application->association_id;
            $companyPeriod->full_name = $application->manager_full_name;
            $companyPeriod->email = $application->manager_email;
            $companyPeriod->phone = $application->manager_phone;
            $companyPeriod->title = $application->manager_title;
            $companyPeriod->status = CompanyPeriodStatusEnum::APPROVED;
            $companyPeriod->save();

            $managerPassword = null;
            $contactPassword = null;
            if ($managerUser) {
                $managerUser->privilege_id = $privilegeId;
                $managerUser->save();
            } else {
                $managerPassword = Str::random(rand(8, 12));
                $managerUser = new User();
                $managerUser->full_name = $application->manager_full_name;
                $managerUser->email = $application->manager_email;
                $managerUser->phone = $application->manager_phone;
                $managerUser->title = $application->manager_title;
                $managerUser->is_approved_password = false;
                $managerUser->password = Hash::make($managerPassword);
                $managerUser->company_id = $company->id;
                $managerUser->type_id = UserEnum::FIRM;
                $managerUser->role_id = $managerRoleId;
                $managerUser->privilege_id = $privilegeId;
                $managerUser->email_verified_at = now();
                $managerUser->save();
            }

            if ($contactUser) {
                $contactUser->privilege_id = $privilegeId;
                $contactUser->save();
            } else if (!$contactUser && $application->manager_email != $application->contact_email) {
                $contactPassword = Str::random(rand(8, 12));
                $contactUser = new User();
                $contactUser->full_name = $application->contact_full_name;
                $contactUser->email = $application->contact_email;
                $contactUser->phone = $application->contact_phone;
                $contactUser->title = $application->contact_title;
                $contactUser->is_approved_password = false;
                $contactUser->password = Hash::make($contactPassword);
                $contactUser->company_id = $company->id;
                $contactUser->type_id = UserEnum::FIRM;
                $contactUser->role_id = $assistantRoleId;
                $contactUser->privilege_id = $privilegeId;
                $contactUser->email_verified_at = now();
                $contactUser->save();
            }

            $application->company_id = $company->id;
            $application->status = InosuitApplicationStatusEnum::APPROVED;
            $application->save();

            Notification::route('mail', $managerUser->email)->notify(new ApplicationApprovedMail($managerUser, $managerPassword));
            Notification::route('mail', $contactUser->email)->notify(new ApplicationApprovedMail($contactUser, $contactPassword));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateMentor(UpdateMentorRequest $request): JsonResponse
    {
        $mentor = Mentor::query()->where('user_id', $request->userId)->firstOrFail();
        $mentor->is_active = $request->isActive ?? $mentor->is_active;
        $mentor->save();
        return (new GeneralResponse())->toJson();
    }

    public function updateMentorPaper(UpdateMentorPaperRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $paper = MentorPaper::query()->findOrFail($request->paperId);
            $paper->status = $request->status ?? $paper->status;
            $paper->save();

            $mentorUserId = $paper->companyPeriod->mentor_user_id;
            if ($mentorUserId) {
                Activity::createActivity(
                    ActivityEnum::PAPER_UPDATE,
                    [$mentorUserId],
                    ['paperId' => $paper->id, 'status' => $paper->status, 'mentorPaper' => true]
                );
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateMentorApplication(UpdateMentorApplicationRequest $request): JsonResponse
    {
        $application = MentorInosuitApplication::query()
            ->whereIn('status', [InosuitApplicationStatusEnum::DECLINED, InosuitApplicationStatusEnum::PENDING])
            ->findOrFail($request->applicationId);
        if (!$request->isApprove) {
            $application->status = InosuitApplicationStatusEnum::DECLINED;
            $application->save();
            Notification::route('mail', $application->email)->notify(new ApplicationDeniedMail($application->full_name));
            return (new GeneralResponse())->toJson();
        }
        $roleId = Role::query()
            ->where('slug', '=', RoleEnum::MANAGER)
            ->firstOrFail()
            ->id;
        $privilegeId = RolePrivilege::query()
            ->where('slug', '=', RolePrivilegeEnum::MENTOR)
            ->firstOrFail()
            ->id;
        $user = User::with('mentor')
            ->where('email', $application->email)
            ->first();
        if ($user && $user->mentor) {
            throw new BadRequestHttpException('Kullanıcı zaten mentor olarak kayıtlıdır.');
        }

        DB::beginTransaction();
        try {
            $password = null;
            if ($user) {
                $user->privilege_id = $privilegeId;
                $user->save();
            } else {
                $password = Str::random(rand(8, 12));
                $user = new User();
                $user->full_name = $application->full_name;
                $user->email = $application->email;
                $user->phone = $application->phone;
                $user->title = $application->title;
                $user->photo = $application->image_url;
                $user->is_approved_password = false;
                $user->password = Hash::make($password);
                $user->type_id = UserEnum::FIRM;
                $user->role_id = $roleId;
                $user->privilege_id = $privilegeId;
                $user->email_verified_at = now();
                $user->save();
            }

            $mentor = new Mentor();
            $mentor->user_id = $user->id;
            $mentor->university_id = $application->university_id;
            $mentor->faculty_id = $application->faculty_id;
            $mentor->department_id = $application->department_id;
            $mentor->cv_url = $application->cv_url;
            $mentor->save();

            $application->mentor_user_id = $user->id;
            $application->status = InosuitApplicationStatusEnum::APPROVED;
            $application->save();

            Notification::route('mail', $user->email)->notify(new ApplicationApprovedMail($user, $password));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateAssociation(UpdateAssociationRequest $request): JsonResponse
    {
        $association = Association::query()->findOrFail($request->associationId);
        $association->is_active = $request->isActive ?? $association->is_active;
        $association->save();
        return (new GeneralResponse())->toJson();
    }

    public function updatePeriod(UpdatePeriodRequest $request): JsonResponse
    {
        $period = Period::query()->findOrFail($request->periodId);
        $period->name = $request->name ?? $period->name;
        $period->start_date = $request->startDate ?? $period->start_date;
        $period->end_date = $request->endDate ?? $period->end_date;
        $period->save();
        return (new GeneralResponse())->toJson();
    }

    public function updateCalendar(UpdateCalendarRequest $request): JsonResponse
    {
        $calendar = Calendar::query()->findOrFail($request->calendarId);

        DB::beginTransaction();
        try {
            if ($request->order) {
                if ($calendar->order > $request->order) {
                    Calendar::query()
                        ->where('period_id', '=', $calendar->period_id)
                        ->where('order', '>=', $request->order)
                        ->where('order', '<', $calendar->order)
                        ->increment('order');
                } elseif ($calendar->order < $request->order) {
                    Calendar::query()
                        ->where('period_id', '=', $calendar->period_id)
                        ->where('order', '>', $calendar->order)
                        ->where('order', '<=', $request->order)
                        ->decrement('order');
                }
            }
            $calendar->order = $request->order ?? $calendar->order;
            $calendar->name = $request->name;
            $calendar->description = $request->description;
            $calendar->goal = $request->goal;
            $calendar->advice = $request->advice;
            $calendar->term = $request->term;
            $calendar->tool = $request->tool;
            $calendar->source = $request->source;
            $calendar->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function deletePeriod(DeletePeriodRequest $request): JsonResponse
    {
        Period::query()
            ->findOrFail($request->periodId)
            ->delete();
        return (new GeneralResponse())->toJson();
    }

    public function deleteCalendar(DeleteCalendarRequest $request): JsonResponse
    {
        $calendar = Calendar::query()->findOrFail($request->calendarId);

        DB::beginTransaction();
        try {
            $calendar->delete();
            Calendar::query()
                ->where('period_id', '=', $calendar->period_id)
                ->where('order', '>', $calendar->order)
                ->decrement('order');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }
}
