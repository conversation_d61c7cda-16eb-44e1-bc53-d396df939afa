<?php

namespace App\Http\Controllers\Inosuit;

use App\Http\Controllers\Controller;
use App\Http\Requests\Inosuit\Inosuit\CreateFileRequest;
use App\Http\Requests\Inosuit\Inosuit\GetActivityRequest;
use App\Http\Requests\Inosuit\Inosuit\GetAssociationRequest;
use App\Http\Requests\Inosuit\Inosuit\GetPeriodRequest;
use App\Http\Requests\Inosuit\Inosuit\UpdatePasswordRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\FileService;
use App\Models\Activity;
use App\Models\Association;
use App\Models\CompanyMeeting;
use App\Models\CompanyPaper;
use App\Models\Mentor\MentorPaper;
use App\Models\Period;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class InosuitController extends Controller
{
    private readonly FileService $fileService;

    public function __construct()
    {
        $this->fileService = new FileService();
    }

    public function getActivities(GetActivityRequest $request): JsonResponse
    {
        $companyPaperIdsForStatus = collect();
        $mentorPaperIdsForStatus = collect();
        if (isset($request->statuses)) {
            $statuses = explode(',', $request->statuses);
            $companyPaperIdsForStatus = CompanyPaper::query()
                ->select('id')
                ->whereIn('status', $statuses)
                ->pluck('id');
            $mentorPaperIdsForStatus = MentorPaper::query()
                ->select('id')
                ->whereIn('status', $statuses)
                ->pluck('id');
        }
        $activities = Activity::query()
            ->with([
                'createdUser.company' => fn($q) => $q->select('id', 'name'),
            ])
            ->whereJsonContains('effected_user_ids', auth()->id())
            ->where(function ($q) use ($request, $companyPaperIdsForStatus, $mentorPaperIdsForStatus) {
                if (!isset($request->statuses)) {
                    return $q;
                }
                return $q
                    ->where(fn($qc) => $companyPaperIdsForStatus->each(fn($id) => $qc->orWhere(fn($oqc) => $oqc->whereJsonContains('payload->paperId', $id)->where('payload->companyPaper', true))))
                    ->orWhere(fn($qm) => $mentorPaperIdsForStatus->each(fn($id) => $qm->orWhere(fn($oqm) => $oqm->whereJsonContains('payload->paperId', $id)->where('payload->mentorPaper', true))));
            })
            ->orderByDesc('created_at')
            ->get();
        $meetingIds = [];
        $companyPaperIds = [];
        $mentorPaperIds = [];
        foreach ($activities as $activity) {
            if (!$activity->payload) {
                continue;
            }
            if (isset($activity->payload['meetingId'])) {
                $meetingIds[] = $activity->payload['meetingId'];
            }
            if (isset($activity->payload['paperId']) && isset($activity->payload['companyPaper'])) {
                $companyPaperIds[] = $activity->payload['paperId'];
            }
            if (isset($activity->payload['paperId']) && isset($activity->payload['mentorPaper'])) {
                $mentorPaperIds[] = $activity->payload['paperId'];
            }
        }
        $meetings = CompanyMeeting::with('createdUser')
            ->whereIn('id', array_values(array_unique($meetingIds)))
            ->get();
        $companyPapers = CompanyPaper::with('createdUser')
            ->whereIn('id', array_values(array_unique($companyPaperIds)))
            ->get();
        $mentorPapers = MentorPaper::with('createdUser')
            ->whereIn('id', array_values(array_unique($mentorPaperIds)))
            ->get();
        foreach ($activities as $activity) {
            if ($activity->payload) {
                if (isset($activity->payload['meetingId'])) {
                    $activity->meeting = $meetings->firstWhere('id', $activity->payload['meetingId']);
                }
                if (isset($activity->payload['paperId']) && isset($activity->payload['companyPaper'])) {
                    $activity->paper = $companyPapers->firstWhere('id', $activity->payload['paperId']);
                }
                if (isset($activity->payload['paperId']) && isset($activity->payload['mentorPaper'])) {
                    $activity->paper = $mentorPapers->firstWhere('id', $activity->payload['paperId']);
                }
            }
        }
        return (new GeneralResponse())
            ->setData($activities->toArray())
            ->toJson();
    }

    public function getAssociations(GetAssociationRequest $request): JsonResponse
    {
        $associations = Association::query()
            ->where('is_active', '=', true)
            ->get();
        return (new GeneralResponse())->setData($associations)->toJson();
    }

    public function getPeriods(GetPeriodRequest $request): JsonResponse
    {
        $periods = Period::query()
            ->where(fn($q) => isset($request->visibleInsouitApplication) ? $q->where('visible_inosuit_application', $request->visibleInsouitApplication) : $q)
            ->orderByDesc('start_date')
            ->get();
        return (new GeneralResponse())->setData($periods)->toJson();
    }

    public function createFile(CreateFileRequest $request): JsonResponse
    {
        $file = $request->file('file');
        $this->fileService->getFileTypeFromMime($file->getMimeType());
        $extension = strtolower(collect(explode('.', $file->getClientOriginalName()))->pop());
        $fileName = $request->post('path') . '/' . Str::random(rand(30, 50)) . '-' . now()->getTimestamp() . '.' . $extension;
        Storage::disk('s3')->put($fileName, $file->getContent());
        $url = Storage::disk('s3')->url($fileName);
        return (new GeneralResponse())->setData(['url' => $url])->toJson();
    }

    public function updatePassword(UpdatePasswordRequest $request): JsonResponse
    {
        $user = auth()->user();
        $isMatch = Hash::check($request->oldPassword, $user->password);
        if (!$isMatch) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.update_password_wrong'))->toJson();
        }
        $user->password = Hash::make($request->newPassword);
        $user->save();
        return (new GeneralResponse())->setMessages(Lang::get('messages.user.update_password_success'))->toJson();
    }
}
