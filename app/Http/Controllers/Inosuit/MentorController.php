<?php

namespace App\Http\Controllers\Inosuit;

use App\Enums\ActivityEnum;
use App\Enums\Inosuit\CompanyMeetingStatusEnum;
use App\Enums\Inosuit\PaperStatusEnum;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Inosuit\Mentor\CreateApplicationRequest;
use App\Http\Requests\Inosuit\Mentor\CreateMeetingRequest;
use App\Http\Requests\Inosuit\Mentor\CreatePaperRequest;
use App\Http\Requests\Inosuit\Mentor\DeleteMeetingRequest;
use App\Http\Requests\Inosuit\Mentor\GetPaperTermRequest;
use App\Http\Requests\Inosuit\Mentor\GetPeriodRequest;
use App\Http\Requests\Inosuit\Mentor\GetCalendarRequest;
use App\Http\Requests\Inosuit\Mentor\GetCompanyRequest;
use App\Http\Requests\Inosuit\Mentor\GetPaperDetailRequest;
use App\Http\Requests\Inosuit\Mentor\GetProfileRequest;
use App\Http\Requests\Inosuit\Mentor\GetMeetingRequest;
use App\Http\Requests\Inosuit\Mentor\GetPaperRequest;
use App\Http\Requests\Inosuit\Mentor\UpdateCalendarRequest;
use App\Http\Requests\Inosuit\Mentor\UpdateMeetingRequest;
use App\Http\Requests\Inosuit\Mentor\UpdatePaperRequest;
use App\Http\Requests\Inosuit\Mentor\UpdateProfileRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\Inosuit\MentorService;
use App\Models\Activity;
use App\Models\Calendar;
use App\Models\CompanyMeeting;
use App\Models\CompanyPaperMeeting;
use App\Models\CompanyPeriod;
use App\Models\Mentor\Mentor;
use App\Models\Mentor\MentorInosuitApplication;
use App\Models\Mentor\MentorPaper;
use App\Models\Mentor\MentorPaperMeeting;
use App\Models\Period;
use App\Models\User;
use App\Notifications\Inosuit\MentorApplicationNotificationMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class MentorController extends Controller
{
    private readonly MentorService $mentorService;

    public function __construct()
    {
        $this->mentorService = new MentorService();
    }

    public function getPeriods(GetPeriodRequest $request): JsonResponse
    {
        $result = $this->mentorService->getPeriods($request);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getProfile(GetProfileRequest $request): JsonResponse
    {
        $user = User::with('mentor')->findOrFail(auth()->user()->id);
        $companies = $user->mentor->getCurrentCompanies();
        $data = [
            'id' => $user->id,
            'fullName' => $user->full_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'title' => $user->title,
            'photo' => $user->photo,
            'mentor' => $user->mentor,
            'companies' => $companies->map(fn($c) => ['id' => $c->id, 'name' => $c->name]),
        ];
        if ($user->full_name && $user->email && $user->phone && $user->mentor->university_id &&
            $user->mentor->faculty_id && $user->mentor->department_id) {
            $data['mentor_is_completed'] = true;
        }
        return (new GeneralResponse())->setData($data)->toJson();
    }

    public function getPapers(GetPaperRequest $request): JsonResponse
    {
        $papers = MentorPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.association'
            ])
            ->whereIn('company_period_id', Mentor::getCompanyPeriodIds())
            ->paginate($request->limit);
        foreach ($papers as $p) {
            $p->companyPeriod->makeHidden(['result_report', 'status']);
        }
        return (new GeneralResponse())
            ->setTotalCount($papers->total())
            ->setData($papers->items())
            ->toJson();
    }

    public function getPaperDetail(GetPaperDetailRequest $request): JsonResponse
    {
        $paper = MentorPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.mentor',
                'companyPeriod.period',
                'mentorPaperMeetings.meeting' => fn($q) => $q->select('id', 'name', 'location_type', 'start_date', 'end_date', 'company_status', 'mentor_status'),
            ])
            ->whereIn('company_period_id', Mentor::getCompanyPeriodIds())
            ->findOrFail($request->paperId);
        $paper->meetings = $paper->mentorPaperMeetings->map(fn($mpm) => $mpm->meeting);
        $paper->makeHidden(['mentorPaperMeetings']);
        $paper->companyPeriod->makeHidden(['result_report', 'status']);
        return (new GeneralResponse())->setData($paper)->toJson();
    }

    public function getPaperTerms(GetPaperTermRequest $request): JsonResponse
    {
        $currentPeriod = $request->company->currentPeriod;
        $period = Period::query()->findOrFail($currentPeriod->period_id);
        $terms = [];

        $paperExistTermDates = MentorPaper::query()
            ->where('company_period_id', $currentPeriod->id)
            ->pluck('term_date')
            ->map(fn($date) => Carbon::parse($date)->format('Y-m-d'))
            ->toArray();

        $startDate = Carbon::parse($period->start_date)->startOfMonth();
        $endDate = Carbon::parse($period->end_date)->startOfMonth();
        while ($startDate <= $endDate) {
            $formattedTermDate = $startDate->format('Y-m-d');
            $terms[] = [
                'name' => $startDate->locale('tr')->translatedFormat('F Y'),
                'termDate' => $formattedTermDate,
                'startDate' => $startDate->format('Y-m-d H:i:s'),
                'endDate' => $startDate->copy()->endOfMonth()->format('Y-m-d H:i:s'),
                'paperExist' => in_array($formattedTermDate, $paperExistTermDates),
            ];
            $startDate->addMonth();
        }
        return (new GeneralResponse())->setData($terms)->toJson();
    }

    public function getCompanies(GetCompanyRequest $request): JsonResponse
    {
        $companyPeriods = CompanyPeriod::query()
            ->with([
                'company' => fn($q) => $q->select('id', 'name'),
                'mentorPapers' => fn($q) => $q->select('id', 'name'),
            ])
            ->whereIn('id', Mentor::getCompanyPeriodIds())
            ->paginate($request->limit);
        return (new GeneralResponse())
            ->setTotalCount($companyPeriods->total())
            ->setData($companyPeriods->items())
            ->toJson();
    }

    public function getMeetings(GetMeetingRequest $request): JsonResponse
    {
        $meetings = CompanyMeeting::query()
            ->with([
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.mentor',
            ])
            ->whereIn('company_period_id', Mentor::getCompanyPeriodIds())
            ->whereNot(function ($q) use ($request) {
                if (!$request->startDate || !$request->endDate) {
                    return $q;
                }
                return $q
                    ->where('start_date', '>', $request->startDate)
                    ->where('start_date', '>', $request->endDate)
                    ->orWhere('end_date', '<', $request->startDate)
                    ->where('end_date', '<', $request->endDate);
            })
            ->paginate($request->limit);
        foreach ($meetings as $m) {
            $m->companyPeriod->makeHidden(['result_report', 'status']);
        }
        return (new GeneralResponse())
            ->setTotalCount($meetings->total())
            ->setData($meetings->items())
            ->toJson();
    }

    public function getCalendars(GetCalendarRequest $request): JsonResponse
    {
        if (!$request->periodId) {
            $periods = $this->getPeriods(new GetPeriodRequest())->getData()->data;
            if (count($periods) > 0) {
                $request->merge(['periodId' => $periods[0]->id]);
            }
        }
        $companies = Mentor::getCompanies($request->periodId);
        $calendars = Calendar::query()
            ->where('period_id', $request->periodId)
            ->orderBy('order')
            ->get();
        foreach ($calendars as $cal) {
            $cal->companies = $companies
                ->filter(fn($c) => $c->companyPeriods->where('calendar_id', $cal->id)->count() > 0)
                ->map(fn($c) => ['id' => $c->id, 'name' => $c->name])
                ->values();
        }
        return (new GeneralResponse())->setData($calendars)->toJson();
    }

    public function createApplication(CreateApplicationRequest $request): JsonResponse
    {
        $application = new MentorInosuitApplication();
        $application->full_name = $request->fullName;
        $application->email = $request->email;
        $application->phone = $request->phone;
        $application->title = $request->title;
        $application->linkedin_url = $request->linkedinUrl;
        $application->cv_url = $request->cvUrl;
        $application->image_url = $request->imageUrl;
        $application->extra_urls = $request->extraUrls;
        $application->organization_type = $request->organizationType;
        $application->organization_name = $request->organizationName;
        $application->experience_year = $request->experienceYear;
        $application->is_sustainable = $request->isSustainable;
        $application->sustainable_text = $request->sustainableText;
        $application->iso_certificate_text = $request->isoCertificateText;
        $application->university_id = $request->universityId;
        $application->faculty_id = $request->facultyId;
        $application->department_id = $request->departmentId;
        $application->contact_full_name = isset($request->contact['fullName']) ? $request->contact['fullName'] : null;
        $application->contact_email = isset($request->contact['email']) ? $request->contact['email'] : null;
        $application->contact_phone = isset($request->contact['phone']) ? $request->contact['phone'] : null;
        $application->contact_title = isset($request->contact['title']) ? $request->contact['title'] : null;
        $application->save();

        if (isset($application->id)) {
            try {
                $notificationEmail = env('NOTIFICATION_INOSUIT_EMAIL');
                Notification::route('mail', $notificationEmail)->notify(new MentorApplicationNotificationMail($application));
            } catch (\Exception $e) {
                Log::error($e);
            }
        }
        return (new GeneralResponse())->toJson();
    }

    public function createPaper(CreatePaperRequest $request): JsonResponse
    {
        $company = $request->company;
        $paperCount = MentorPaper::query()
            ->where('company_period_id', $company->currentPeriod->id)
            ->count();
        $month = mb_strtolower(Carbon::parse($request->termDate)->locale(app()->getLocale())->translatedFormat('M'));
        $meetings = [];

        DB::beginTransaction();
        try {
            $paper = new MentorPaper();
            $paper->created_user_id = auth()->id();
            $paper->company_period_id = $company->currentPeriod->id;
            $paper->name = $month . '_m_' . ($paperCount + 1);
            $paper->activity_output = $request->activityOutput;
            $paper->unrealised_output = $request->unrealisedOutput;
            $paper->observation = $request->observation;
            $paper->extra_urls = $request->extraUrls;
            $paper->term_date = $request->termDate;
            $paper->save();
            foreach ($request->meetingIds as $meetingId) {
                $meetings[] = [
                    'mentor_paper_id' => $paper->id,
                    'meeting_id' => $meetingId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
            MentorPaperMeeting::query()->insert($meetings);

            $effectedUserIds = User::query()
                ->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::TIM_ADMIN))
                ->pluck('id')
                ->toArray();
            if (count($effectedUserIds) > 0) {
                Activity::createActivity(
                    ActivityEnum::PAPER_CREATE,
                    $effectedUserIds,
                    ['paperId' => $paper->id, 'mentorPaper' => true]
                );
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function createMeeting(CreateMeetingRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $meeting = new CompanyMeeting();
            $meeting->created_user_id = auth()->id();
            $meeting->company_period_id = $request->company->currentPeriod->id;
            $meeting->name = $request->name;
            $meeting->description = $request->description;
            $meeting->location = $request->location;
            $meeting->location_type = $request->locationType;
            $meeting->mentor_status = CompanyMeetingStatusEnum::APPROVED;
            $meeting->mentor_note = $request->note;
            $meeting->start_date = $request->startDate;
            $meeting->end_date = $request->endDate;
            $meeting->save();

            $companyId = $request->company->currentPeriod->company_id;
            $effectedUserIds = User::query()->where('company_id', $companyId)->pluck('id')->toArray();
            if (count($effectedUserIds) > 0) {
                Activity::createActivity(
                    ActivityEnum::MEETING_CREATE,
                    $effectedUserIds,
                    ['meetingId' => $meeting->id]
                );
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())
            ->setData(['id' => $meeting->id])
            ->toJson();
    }

    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $user = User::with('mentor')->findOrFail(auth()->id());
        $user->full_name = $request->fullName ?? $user->full_name;
        $user->phone = $request->phone ?? $user->phone;
        $user->title = $request->title ?? $user->title;
        $user->photo = $request->photo ?? $user->photo;
        $user->mentor->description = $request->has('description') && !$request->description ? null : ($request->description ?? $user->mentor->description);
        $user->mentor->address = $request->has('address') && !$request->address ? null : ($request->address ?? $user->mentor->address);
        $user->mentor->cv_url = $request->cvUrl ?? $user->mentor->cv_url;
        $user->mentor->university_id = $request->universityId ?? $user->mentor->university_id;
        $user->mentor->faculty_id = $request->facultyId ?? $user->mentor->faculty_id;
        $user->mentor->department_id = $request->departmentId ?? $user->mentor->department_id;
        $user->save();
        $user->mentor->save();
        return (new GeneralResponse())->toJson();
    }

    public function updatePaper(UpdatePaperRequest $request): JsonResponse
    {
        $paper = MentorPaper::query()->findOrFail($request->paperId);
        if ($request->termDate) {
            $month = mb_strtolower(Carbon::parse($request->termDate)->locale(app()->getLocale())->translatedFormat('M'));
            $paperCode = collect(explode('_', $paper->name))->pop();
            $name = $month . '_m_' . $paperCode;
        }
        $meetings = [];

        DB::beginTransaction();
        try {
            $paper->name = $name ?? $paper->name;
            $paper->activity_output = $request->activityOutput ?? $paper->activity_output;
            $paper->unrealised_output = $request->unrealisedOutput ?? $paper->unrealised_output;
            $paper->observation = $request->observation ?? $paper->observation;
            $paper->extra_urls = isset($request->extraUrls) ? $request->extraUrls : $paper->extra_urls;
            $paper->status = PaperStatusEnum::PENDING;
            $paper->term_date = $request->termDate ?? $paper->term_date;
            $paper->save();
            if ($request->meetingIds) {
                MentorPaperMeeting::query()->where('mentor_paper_id', $paper->id)->delete();
                foreach ($request->meetingIds as $meetingId) {
                    $meetings[] = [
                        'mentor_paper_id' => $paper->id,
                        'meeting_id' => $meetingId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }
            MentorPaperMeeting::query()->insert($meetings);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateMeeting(UpdateMeetingRequest $request): JsonResponse
    {
        $meeting = CompanyMeeting::query()->findOrFail($request->meetingId);
        $meeting->name = $request->has('name') && !$request->name ? null : ($request->name ?? $meeting->name);
        $meeting->description = $request->has('description') && !$request->description ? null : ($request->description ?? $meeting->description);
        $meeting->location = $request->has('location') && !$request->location ? null : ($request->location ?? $meeting->location);
        $meeting->location_type = $request->locationType ?? $meeting->location_type;
        $meeting->mentor_status = $request->mentorStatus ?? $meeting->mentor_status;
        $meeting->mentor_note = $request->has('mentorNote') && !$request->mentorNote ? null : ($request->mentorNote ?? $meeting->mentor_note);
        $meeting->start_date = $request->startDate ?? $meeting->start_date;
        $meeting->end_date = $request->endDate ?? $meeting->end_date;
        $meeting->save();
        return (new GeneralResponse())->toJson();
    }

    public function updateCalendar(UpdateCalendarRequest $request): JsonResponse
    {
        $period = Mentor::getCurrentCompanies()->where('id', $request->companyId)->firstOrFail()->currentPeriod;
        $period->calendar_id = $request->calendarId;
        $period->save();
        return (new GeneralResponse())->toJson();
    }

    public function deleteMeeting(DeleteMeetingRequest $request): JsonResponse
    {
        $meeting = CompanyMeeting::query()
            ->where('created_user_id', auth()->id())
            ->findOrFail($request->meetingId);

        DB::beginTransaction();
        try {
            CompanyPaperMeeting::query()
                ->where('meeting_id', $meeting->id)
                ->delete();
            MentorPaperMeeting::query()
                ->where('meeting_id', $meeting->id)
                ->delete();
            $meeting->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }
}
