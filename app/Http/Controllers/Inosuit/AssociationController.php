<?php

namespace App\Http\Controllers\Inosuit;

use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Inosuit\Association\GetCompanyProfileRequest;
use App\Http\Requests\Inosuit\Association\GetMentorProfileRequest;
use App\Http\Requests\Inosuit\Association\GetPeriodRequest;
use App\Http\Requests\Inosuit\Association\GetCompanyRequest;
use App\Http\Requests\Inosuit\Association\GetMentorRequest;
use App\Http\Requests\Inosuit\Association\GetPaperRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\Inosuit\AssociationService;
use App\Models\Company;
use App\Models\CompanyPaper;
use App\Models\CompanyPeriod;
use App\Models\Mentor\MentorPaper;
use App\Models\User;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;

class AssociationController extends Controller
{
    private readonly AssociationService $associationService;

    public function __construct()
    {
        $this->associationService = new AssociationService();
    }

    public function getPeriods(GetPeriodRequest $request): JsonResponse
    {
        $result = $this->associationService->getPeriods($request);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getPapers(GetPaperRequest $request): JsonResponse
    {
        $periodId = GeneralUtil::getInosuitPeriodId();
        if (!$periodId) {
            return (new GeneralResponse())
                ->setData([])
                ->toJson();
        }

        $companyPapers = CompanyPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.mentor',
            ])
            ->select('id', 'created_user_id', 'company_period_id', 'name', 'status', 'term_date')
            ->has('companyPeriod')
            ->whereHas('companyPeriod', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->where(
                fn($q) => isset($request->search) ? $q
                    ->orWhereHas('companyPeriod.company', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.period', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.mentor', fn($q) => $q->where('full_name', 'LIKE', "%$request->search%"))
                    : $q
            )
            ->whereHas('companyPeriod', fn($q) => isset($request->companyIds) ? $q->whereIn('company_id', explode(',', $request->companyIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->get();
        $mentorPapers = MentorPaper::query()
            ->with([
                'createdUser',
                'companyPeriod.company' => fn($q) => $q->select('id', 'name'),
                'companyPeriod.period',
                'companyPeriod.mentor',
            ])
            ->select('id', 'created_user_id', 'company_period_id', 'name', 'status', 'term_date')
            ->has('companyPeriod')
            ->whereHas('companyPeriod', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->where(
                fn($q) => isset($request->search) ? $q
                    ->orWhereHas('companyPeriod.company', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.period', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
                    ->orWhereHas('companyPeriod.mentor', fn($q) => $q->where('full_name', 'LIKE', "%$request->search%"))
                    : $q
            )
            ->whereHas('companyPeriod', fn($q) => isset($request->companyIds) ? $q->whereIn('company_id', explode(',', $request->companyIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->whereHas('companyPeriod', fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->get();
        $data = [];
        $addedMentorPaperIds = [];
        foreach ($companyPapers as $p) {
            $p->companyPaper = (object)[
                'id' => $p->id,
                'name' => $p->name,
                'status' => $p->status,
                'term_date' => $p->term_date,
                'created_user' => $p->createdUser,
            ];
            $termDate = date('Y-m', strtotime($p->term_date));
            $p->mentorPaper = $mentorPapers->first(fn($mp) => $p->company_period_id == $mp->company_period_id && date('Y-m', strtotime($mp->term_date)) === $termDate);
            if ($p->mentorPaper) {
                $p->mentorPaper = (object)[
                    'id' => $p->mentorPaper->id,
                    'name' => $p->mentorPaper->name,
                    'status' => $p->mentorPaper->status,
                    'term_date' => $p->mentorPaper->term_date,
                    'created_user' => $p->mentorPaper->createdUser,
                ];
                $addedMentorPaperIds[] = $p->mentorPaper->id;
            }
            $p->makeHidden(['id', 'name', 'status', 'term_date', 'createdUser']);
            $p->companyPeriod->makeHidden(['result_report', 'status']);
            $data[] = $p;
        }
        $selfMentorPapers = $mentorPapers->whereNotIn('id', $addedMentorPaperIds);
        foreach ($selfMentorPapers as $p) {
            $p->companyPaper = null;
            $p->mentorPaper = [
                'id' => $p->id,
                'name' => $p->name,
                'status' => $p->status,
                'term_date' => $p->term_date,
                'created_user' => $p->createdUser,
            ];
            $p->makeHidden(['id', 'name', 'status', 'term_date', 'createdUser']);
            $p->companyPeriod->makeHidden(['result_report', 'status']);
            $data[] = $p;
        }
        return (new GeneralResponse())
            ->setData(array_values($data))
            ->toJson();
    }

    public function getCompanies(GetCompanyRequest $request): JsonResponse
    {
        $companyPeriods = CompanyPeriod::query()
            ->select('id', 'id AS company_period_id', 'company_id', 'period_id', 'association_id', 'mentor_user_id', 'status')
            ->with([
                'company' => fn($q) => $q->select('id', 'name'),
                'period',
                'mentor',
                'companyPapers' => fn($q) => $q->select('id', 'company_period_id', 'name', 'status'),
                'mentorPapers' => fn($q) => $q->select('id', 'company_period_id', 'name', 'status'),
            ])
            ->whereHas('company', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->whereHas('company', fn($q) => isset($request->isActiveStatuses) ? $q->whereIn('is_active', explode(',', $request->isActiveStatuses)) : $q)
            ->whereHas('company', fn($q) => isset($request->search) ? $q->where('name', 'LIKE', "%$request->search%") : $q)
            ->where(fn($q) => isset($request->periodIds) ? $q->whereIn('period_id', explode(',', $request->periodIds)) : $q)
            ->where(fn($q) => isset($request->mentorUserIds) ? $q->whereIn('mentor_user_id', explode(',', $request->mentorUserIds)) : $q)
            ->orderBy('company_id')
            ->orderBy('period_id')
            ->paginate($request->limit);
        foreach ($companyPeriods as $cp) {
            $cp->makeHidden(['id']);
        }
        return (new GeneralResponse())
            ->setTotalCount($companyPeriods->total())
            ->setData($companyPeriods->items())
            ->toJson();
    }

    public function getCompanyProfile(GetCompanyProfileRequest $request): JsonResponse
    {
        $company = Company::query()
            ->select(
                'id',
                'name',
                'description',
                'sector_id',
                'is_active',
                'tax_number',
                'email',
                'phone',
                'website',
                'linkedin_url',
                'twitter_url',
                'logo',
                'address'
            )
            ->with([
                'sector',
                'inosuitApplication',
                'companyPeriods' => fn($q) => $q->where('association_id', auth()->user()->association_id),
                'companyPeriods.period',
                'companyPeriods.mentor',
                'companyPeriods.association',
            ])
            ->whereHas('companyPeriods', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->findOrFail($request->companyId);
        $company->companyPeriods->makeHidden(['result_report', 'status']);
        return (new GeneralResponse())->setData($company->toArray())->toJson();
    }

    public function getMentors(GetMentorRequest $request): JsonResponse
    {
        $mentors = User::query()
            ->with([
                'companyPeriods' => fn($q) => $q->select('company_id', 'period_id', 'mentor_user_id')->where('association_id', auth()->user()->association_id),
                'companyPeriods.company' => fn($q) => $q->select('id', 'name'),
                'mentor.university',
                'mentor.faculty',
                'mentor.department',
            ])
            ->select('id', 'full_name', 'email', 'phone', 'photo')
            ->has('mentor')
            ->whereHas('companyPeriods', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::MENTOR))
            ->whereHas('mentor', fn($q) => isset($request->universityIds) ? $q->whereIn('university_id', explode(',', $request->universityIds)) : $q)
            ->whereHas('mentor', fn($q) => isset($request->facultyIds) ? $q->whereIn('faculty_id', explode(',', $request->facultyIds)) : $q)
            ->whereHas('mentor', fn($q) => isset($request->departmentIds) ? $q->whereIn('department_id', explode(',', $request->departmentIds)) : $q)
            ->whereHas('mentor', fn($q) => isset($request->isActiveStatuses) ? $q->whereIn('is_active', explode(',', $request->isActiveStatuses)) : $q)
            ->where(function ($q) use ($request) {
                return isset($request->companyIds) ? $q->whereHas('companyPeriods', fn($q) => $q->whereIn('company_id', explode(',', $request->companyIds))) : $q;
            })
            ->get();
        foreach ($mentors as $m) {
            $m->companies = $m->companyPeriods->map(fn($cp) => $cp->company);
            $m->cv_url = $m->mentor->cv_url;
            $m->is_active = $m->mentor->is_active;
            $m->university = $m->mentor->university;
            $m->faculty = $m->mentor->faculty;
            $m->department = $m->mentor->department;
            unset($m->companyPeriods);
            unset($m->mentor);
        }
        return (new GeneralResponse())->setData($mentors->toArray())->toJson();
    }

    public function getMentorProfile(GetMentorProfileRequest $request): JsonResponse
    {
        $user = User::query()
            ->with([
                'mentor.university',
                'mentor.faculty',
                'mentor.department',
                'inosuitApplication',
                'companyPeriods' => fn($q) => $q->where('association_id', auth()->user()->association_id),
                'companyPeriods.company'
            ])
            ->whereHas('companyPeriods', fn($q) => $q->where('association_id', auth()->user()->association_id))
            ->findOrFail($request->mentorUserId);
        $data = [
            'id' => $user->id,
            'full_name' => $user->full_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'title' => $user->title,
            'photo' => $user->photo,
            'mentor' => $user->mentor,
            'inosuit_application' => $user->inosuitApplication,
            'companies' => $user->companyPeriods->map(fn($cp) => ['id' => $cp->company->id, 'name' => $cp->company->name]),
        ];
        return (new GeneralResponse())->setData($data)->toJson();
    }
}
