<?php

namespace App\Http\Controllers;

use App\Enums\FrontProjectEnum;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\RegisterEnum;
use App\Enums\RoleEnum;
use App\Enums\UserEnum;
use App\Http\Requests\User\DeleteProfilePhotoRequest;
use App\Http\Requests\User\GetProfileRequest;
use App\Http\Requests\User\LoginRequest;
use App\Http\Requests\User\LogoutRequest;
use App\Http\Requests\User\RegisterRequest;
use App\Http\Requests\User\ResetPasswordRequest;
use App\Http\Requests\User\SendForgotPasswordMailRequest;
use App\Http\Requests\User\UpdatePasswordRequest;
use App\Http\Requests\User\UpdateProfilePhotoRequest;
use App\Http\Requests\User\UpdateProfileRequest;
use App\Http\Requests\User\VerifyEmailRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Company;
use App\Models\Role;
use App\Models\UserEmailToken;
use App\Models\RolePrivilege;
use App\Models\TipContentUser;
use App\Models\User;
use App\Models\UserPending;
use App\Notifications\ResetPasswordMail;
use App\Notifications\UserPendingInformationMail;
use App\Notifications\UserVerifyMail;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class UserController extends Controller
{
    public function login(LoginRequest $request): JsonResponse
    {
        $user = User::with('company')->where('email', $request->email)->first();
        if ($user && Hash::check($request->password, $user->password)) {
            if (!$user->email_verified_at) {
                return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.login_not_verified_email'))->toJson();
            }
            if (GeneralUtil::getFrontProject() === FrontProjectEnum::GREENTIM && !$user->company) {
                return (new GeneralResponse(false))->setMessages('Şirketiniz bulunmuyor.')->toJson();
            }
            $user->mapAppsAccess();
            $token = $user->createToken(env('APP_NAME'))->accessToken;
            TipContentUser::addTip('first-login', $user->id);
            return (new GeneralResponse())->setData(['user' => $user, 'token' => $token])->toJson();
        }
        return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.login_wrong'))->toJson();
    }

    public function logout(LogoutRequest $request): JsonResponse
    {
        auth()->user()->token()->revoke();
        return (new GeneralResponse())->toJson();
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $company = new Company();
            $company->name = $request->companyName;
            $company->personnel_number_id = $request->companyPersonnelNumberId;
            $company->sector_id = $request->companySectorId;
            $company->tax_number = $request->companyTaxNumber;
            $company->save();

            if (RegisterEnum::tryFrom($request->registerTypeId) == RegisterEnum::APPLICATION) {
                $this->applicationRegister($request, $company);
            } else if (RegisterEnum::tryFrom($request->registerTypeId) == RegisterEnum::NORMAL) {
                $this->normalRegisterWithPassword($request, $company);
            } else {
                throw new BadRequestHttpException("unexpected register type: $request->registerTypeId");
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }

        return (new GeneralResponse())->setMessages(Lang::get('messages.user.register_success'))->toJson();
    }

    private function applicationRegister(RegisterRequest $request, Company $company): void
    {
        $managerPending = new UserPending();
        $managerPending->full_name = $request->fullName;
        $managerPending->email = $request->email;
        $managerPending->phone = $request->phone;
        $managerPending->title = $request->title;
        $managerPending->is_manager = true;
        $managerPending->company_id = $company->id;
        $managerPending->save();

        if ($request->secondUser) {
            $pending = new UserPending();
            $pending->full_name = $request->fullName2;
            $pending->email = $request->email2;
            $pending->phone = $request->phone2;
            $pending->title = $request->title2;
            $pending->company_id = $company->id;
            $pending->save();
        }

        Notification::route('mail', $managerPending->email)->notify(new UserPendingInformationMail($managerPending));
        if ($request->secondUser) {
            Notification::route('mail', $pending->email)->notify(new UserPendingInformationMail($pending));
        }
    }

    private function normalRegisterWithPassword(RegisterRequest $request, Company $company): void
    {
        $roleId = Role::query()
            ->where('slug', '=', RoleEnum::MANAGER)
            ->firstOrFail()
            ->id;
        $privilegeId = RolePrivilege::query()
            ->where('slug', '=', RolePrivilegeEnum::COMPANY)
            ->firstOrFail()
            ->id;

        $user = new User();
        $user->full_name = $request->fullName;
        $user->email = $request->email;
        $user->phone = $request->phone;
        $user->title = $request->title;
        $user->password = Hash::make($request->password);
        $user->company_id = $company->id;
        $user->type_id = UserEnum::FIRM;
        $user->role_id = $roleId;
        $user->privilege_id = $privilegeId;
        $user->kvkk_approved = $request->kvkkApproved;
        $user->clarification_text = $request->clarificationText;
        $user->privacy_policy = $request->privacyPolicy;
        $user->save();

        $emailVerifyToken = new UserEmailToken();
        $emailVerifyToken->email = $user->email;
        $emailVerifyToken->token = Str::random(rand(100, 255));
        $emailVerifyToken->token_type = UserEmailToken::VERIFY;
        $emailVerifyToken->created_at = now();
        $emailVerifyToken->expired_at = now()->addHours(2);
        $emailVerifyToken->save();

        Notification::route('mail', $user->email)->notify(new UserVerifyMail($user, $emailVerifyToken->token));
    }

    public function verifyEmail(VerifyEmailRequest $request): JsonResponse
    {
        $emailVerifyToken = UserEmailToken::query()
            ->where('token', $request->token)
            ->where('token_type', UserEmailToken::VERIFY)
            ->firstOrFail();
        if ($emailVerifyToken->expired_at < now()) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.verify_url_timeout'))->toJson();
        }
        $user = User::query()->where('email', $emailVerifyToken->email)->first();
        $user->email_verified_at = now();
        $user->save();
        $emailVerifyToken->delete();
        return (new GeneralResponse())->setMessages(Lang::get('messages.user.verify_success'))->toJson();
    }

    public function sendForgotPasswordMail(SendForgotPasswordMailRequest $request): JsonResponse
    {
        $user = User::query()->where('email', $request->email)->first();
        if (!$user) {
            throw new BadRequestHttpException('Kullanıcı bulunamadı.');
        }

        $last2HoursTokens = UserEmailToken::query()
            ->where('email', $user->email)
            ->where('token_type', UserEmailToken::RESET)
            ->whereBetween('created_at', [now()->subHours(2), now()])
            ->get();
        if ($last2HoursTokens->count() >= 3) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.send_forgot_over_limit'))->toJson();
        }

        $emailVerifyToken = new UserEmailToken();
        $emailVerifyToken->email = $user->email;
        $emailVerifyToken->token = Str::random(rand(100, 255));
        $emailVerifyToken->token_type = UserEmailToken::RESET;
        $emailVerifyToken->created_at = now();
        $emailVerifyToken->expired_at = now()->addHours(2);
        $emailVerifyToken->save();

        Notification::route('mail', $user->email)->notify(new ResetPasswordMail($user, $emailVerifyToken->token));
        return (new GeneralResponse())->setMessages(Lang::get('messages.user.send_forgot_success'))->toJson();
    }

    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        $emailVerifyToken = UserEmailToken::query()
            ->where('token', $request->token)
            ->where('token_type', UserEmailToken::RESET)
            ->firstOrFail();
        if ($emailVerifyToken->expired_at < now()) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.reset_password_expired'))->toJson();
        }
        $user = User::query()->where('email', $emailVerifyToken->email)->first();
        $user->password = Hash::make($request->password);
        $user->save();
        $emailVerifyToken->delete();
        $token = $user->createToken(env('APP_NAME'))->accessToken;
        return (new GeneralResponse())
            ->setData(['token' => $token])
            ->setMessages(Lang::get('messages.user.reset_password_success'))
            ->toJson();
    }

    public function updatePassword(UpdatePasswordRequest $request): JsonResponse
    {
        $user = auth()->user();
        if ($request->oldPassword && !Hash::check($request->oldPassword, $user->password)) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.update_password_wrong'))->toJson();
        }
        if (!$user->is_approved_password) {
            $token = $user->createToken(env('APP_NAME'))->accessToken;
        }
        $user->password = Hash::make($request->newPassword);
        $user->is_approved_password = true;
        $user->kvkk_approved = $request->kvkkApproved ?? $user->kvkk_approved;
        $user->clarification_text = $request->clarificationText ?? $user->clarification_text;
        $user->privacy_policy = $request->privacyPolicy ?? $user->privacy_policy;
        $user->save();

        $response = (new GeneralResponse())->setMessages(Lang::get('messages.user.update_password_success'));
        if (isset($token)) {
            $response->setData(['token' => $token]);
        }
        return $response->toJson();
    }

    public function getProfile(GetProfileRequest $request): JsonResponse
    {
        $user = User::with(['company.companyPeriods', 'role.permissions', 'role.languages', 'privilege'])->findOrFail(auth()->id());
        Role::addLangProperties([$user->role]);
        $user->mapPermissions();
        $user->mapAppsAccess();
        return (new GeneralResponse())->setData($user->toArray())->toJson();
    }

    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $user = auth()->user();
        $user->full_name = $request->fullName ?? $user->full_name;
        $user->phone = isset($request->phone) && $request->phone === null ? null : ($request->phone ?? $user->phone);
        $user->title = $request->title ?? $user->title;
        $user->photo = isset($request->photo) && $request->photo === null ? null : ($request->photo ?? $user->photo);
        $user->kvkk_approved = $request->kvkkApproved ?? $user->kvkk_approved;
        $user->clarification_text = $request->clarificationText ?? $user->clarification_text;
        $user->privacy_policy = $request->privacyPolicy ?? $user->privacy_policy;
        $user->save();
        return (new GeneralResponse())->toJson();
    }

    public function updateProfilePhoto(UpdateProfilePhotoRequest $request): JsonResponse
    {
        $image = $request->file('image');
        $fileName = Str::random(rand(30, 50)) . '-' . now()->getTimestamp() . '.' . explode('/', $image->getMimeType())[1];
        Storage::disk('s3')->put("profile/$fileName", $image->getContent());

        $user = User::query()->findOrFail(auth()->user()->id);
        $user->photo = Storage::disk('s3')->url("profile/$fileName");
        $user->save();
        return (new GeneralResponse())->toJson();
    }

    public function deleteProfilePhoto(DeleteProfilePhotoRequest $request): JsonResponse
    {
        $user = auth()->user();
        if (!$user->photo) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.user.profile_photo_is_null'))->toJson();
        }
        $exploded = explode('/', $user->photo);
        $path = $exploded[count($exploded) - 2] . '/' . $exploded[count($exploded) - 1];
        Storage::disk('s3')->delete($path);

        $user->photo = null;
        $user->save();
        return (new GeneralResponse())->toJson();
    }
}
