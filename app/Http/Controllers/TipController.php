<?php

namespace App\Http\Controllers;

use App\Http\Requests\Tip\GetContentRequest;
use App\Http\Requests\Tip\ReadContentRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\TipContent;
use App\Models\TipContentUser;
use Illuminate\Http\JsonResponse;

class TipController extends Controller
{
    public function getContents(GetContentRequest $request): JsonResponse
    {
        $tipContents = TipContent::with(['user', 'languages'])
            ->where('front_slug', $request->frontSlug)
            ->whereHas('user');
        if ($request->isRead !== null) {
            $tipContents = $tipContents
                ->where(function ($query) use ($request) {
                    $query->whereHas('user', fn($q) => $q->where('is_read', $request->isRead));
                    if ($request->isRead === false) {
                        $query->orWhereDoesntHave('user');
                    }
                });
        }
        $tipContents = $tipContents->get();

        $lastContent = $tipContents->sortByDesc(fn($c) => $c->user->created_at)->first();
        if ($request->isPast === true) {
            $tipContents = $tipContents->where('tip_id', '!=',  $lastContent->tip_id);
        } else if ($request->isPast === false) {
            $tipContents = $tipContents->where('tip_id', '=', $lastContent->tip_id);
        }

        TipContent::addLangProperties($tipContents);
        TipContent::mapUserReads($tipContents);
        return (new GeneralResponse())->setData($tipContents)->toJson();
    }

    public function readContents(ReadContentRequest $request): JsonResponse
    {
        $contentUser = TipContentUser::query()
            ->where('content_id', $request->contentId)
            ->where('user_id', auth()->id())
            ->firstOrFail();
        $contentUser->is_read = true;
        $contentUser->save();
        return (new GeneralResponse())->toJson();
    }
}
