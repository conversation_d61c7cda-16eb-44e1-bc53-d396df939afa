<?php

namespace App\Http\Controllers;

use App\Http\Requests\Authorization\CreateEmployeeRequest;
use App\Http\Requests\Authorization\DeleteEmployeeRequest;
use App\Http\Requests\Authorization\GetEmployeeRequest;
use App\Http\Requests\Authorization\GetRoleRequest;
use App\Http\Requests\Authorization\UpdateEmployeeRequest;
use App\Http\Requests\Authorization\UploadEmployeeExcelRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\TipContentUser;
use App\Models\Role;
use App\Models\User;
use App\Notifications\CreateEmployeeMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class AuthorizationController extends Controller
{
    public function getRoles(GetRoleRequest $request): JsonResponse
    {
        $roles = Role::with('languages')->get();
        Role::addLangProperties($roles);
        return (new GeneralResponse())->setData($roles->toArray())->toJson();
    }

    public function getEmployees(GetEmployeeRequest $request): JsonResponse
    {
        $users = User::with('role.languages')->where('company_id', auth()->user()->company_id)->get();
        Role::addLangProperties($users, 'role');
        return (new GeneralResponse())->setData($users->toArray())->toJson();
    }

    public function createEmployees(CreateEmployeeRequest $request): JsonResponse
    {
        $password = Str::random(rand(8, 12));

        $user = new User();
        $user->full_name = $request->fullName;
        $user->email = $request->email;
        $user->title = $request->title;
        $user->is_approved_password = false;
        $user->password = Hash::make($password);
        $user->company_id = auth()->user()->company_id;
        $user->type_id = auth()->user()->type_id;
        $user->role_id = $request->roleId;
        $user->privilege_id = auth()->user()->privilege_id;
        $user->email_verified_at = now();
        $user->save();

        Notification::route('mail', $user->email)->notify(new CreateEmployeeMail($user, $password));
        TipContentUser::addTip('employee-save');
        return (new GeneralResponse())->toJson();
    }

    public function updateEmployees(UpdateEmployeeRequest $request, int $employeeId): JsonResponse
    {
        if (auth()->id() == $employeeId) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.authorization.update_employee_self'))->toJson();
        }
        $user = User::query()->where('company_id', auth()->user()->company_id)->findOrFail($employeeId);
        $user->full_name = $request->fullName;
        $user->title = $request->title;
        $user->role_id = $request->roleId;
        $user->save();

        return (new GeneralResponse())->toJson();
    }

    public function deleteEmployees(DeleteEmployeeRequest $request, int $employeeId): JsonResponse
    {
        if (auth()->id() == $employeeId) {
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.authorization.delete_employee_self'))->toJson();
        }
        $user = User::query()->where('company_id', auth()->user()->company_id)->findOrFail($employeeId);
        $user->delete();
        return (new GeneralResponse())->toJson();
    }

    public function uploadEmployeesExcel(UploadEmployeeExcelRequest $request): JsonResponse
    {
        $authUser = auth()->user();
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($request->file('excel')->path());
        $excelUsers = [];
        $showPasswordsByEmail = [];
        foreach ($spreadsheet->getActiveSheet()->toArray() as $i => $row) {
            if ($i == 0) {
                continue;
            }
            if (!$row[0] && !$row[1] && !$row[2] && !$row[3]) {
                continue;
            }
            if (strstr(auth()->user()->email, '@') != strstr($row[1], '@')) {
                throw new BadRequestHttpException($row[1] . ' adresi geçersizdir. Sadece kendi şirketinize ait e-posta adresi ekleyebilirsiniz.');
            }
            $password = Str::random(rand(8, 12));
            $excelUsers[] = [
                'full_name' => $row[0],
                'email' => $row[1],
                'title' => $row[2],
                'is_approved_password' => false,
                'password' => Hash::make($password),
                'company_id' => $authUser->company_id,
                'type_id' => $authUser->type_id,
                'role_id' => Role::with('languages')->whereHas('languages', fn($q) => $q->where('name', $row[3]))->first()->id,
                'email_verified_at' => now(),
            ];
            $showPasswordsByEmail[$row[1]] = $password;
        }
        $validator = Validator::make($excelUsers, [
            '*.full_name' => 'required|string|max:255',
            '*.email' => [
                'required', 'string', 'email', 'max:255',
                Rule::unique('users', 'email')->whereNot('company_id', $authUser->company_id),
                Rule::unique('user_pendings', 'email')->whereNull('deleted_at')
            ],
            '*.title' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            return (new GeneralResponse(false))->setErrors($validator->errors())->toJson();
        }
        $alreadyExistEmails = User::query()
            ->whereIn('email', array_map(fn($eUser) => $eUser['email'], $excelUsers))
            ->get()
            ->pluck('email')->toArray();

        DB::beginTransaction();
        try {
            $savedUsers = [];
            foreach ($excelUsers as $eUser) {
                $savedUsers[] = User::query()->updateOrCreate([
                    'email' => $eUser['email'],
                    'deleted_at' => null,
                ], $eUser);
            }
            foreach ($savedUsers as $sUser) {
                if (!in_array($sUser->email, $alreadyExistEmails)) {
                    Notification::route('mail', $sUser->email)->notify(new CreateEmployeeMail($sUser, $showPasswordsByEmail[$sUser->email]));
                }
            }
            TipContentUser::addTip('employee-save');
            DB::commit();
            return (new GeneralResponse())->toJson();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
    }
}
