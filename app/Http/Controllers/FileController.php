<?php

namespace App\Http\Controllers;

use App\Http\Requests\File\CreateFileRequest;
use App\Http\Requests\File\CreateFileUrlRequest;
use App\Http\Requests\File\DeleteFileRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\FileService;
use Illuminate\Http\JsonResponse;

class FileController extends Controller
{
    private readonly FileService $fileService;

    public function __construct()
    {
        $this->fileService = new FileService();
    }

    public function createFile(CreateFileRequest $request): JsonResponse
    {
        $fileManager = $this->fileService->createFile($request);
        return (new GeneralResponse())->setData($fileManager)->toJson();
    }

    public function createFileUrl(CreateFileUrlRequest $request): JsonResponse
    {
        $fileManager = $this->fileService->createFileUrl($request);
        return (new GeneralResponse())->setData($fileManager)->toJson();
    }

    public function deleteFile(DeleteFileRequest $request): JsonResponse
    {
        $this->fileService->deleteFile($request);
        return (new GeneralResponse())->toJson();
    }
}
