<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Http\Requests\SuperAdmin\Tip\AddTipRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\TipContentUser;
use Illuminate\Http\JsonResponse;

class TipController extends Controller
{
    public function addTip(AddTipRequest $request): JsonResponse
    {
        TipContentUser::addTip($request->action, $request->userId);
        return (new GeneralResponse())->toJson();
    }
}
