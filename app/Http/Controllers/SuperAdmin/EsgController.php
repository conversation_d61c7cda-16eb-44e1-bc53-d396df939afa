<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Enums\RoleEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SuperAdmin\Esg\GetFacilityCountRequest;
use App\Http\Requests\SuperAdmin\Esg\GetReportFileRequest;
use App\Http\Requests\SuperAdmin\Esg\GetReportRequest;
use App\Http\Requests\SuperAdmin\Esg\LoginRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Company;
use App\Models\CompanyPersonnelNumber;
use App\Models\CompanySector;
use App\Models\Facility;
use App\Models\Oauth\OauthAccessToken;
use App\Models\Oauth\OauthPersonalAccessClient;
use App\Models\Report;
use App\Models\Role;
use App\Models\User;
use App\Models\UserType;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class EsgController extends Controller
{
    public function getReports(GetReportRequest $request): JsonResponse
    {
        $companyIds = explode(',', $request->companyIds);
        $years = Report::query()
            ->select('year')
            ->distinct()
            ->whereIn('company_id', $companyIds)
            ->orderByDesc('year')
            ->get()
            ->pluck('year')
            ->values();
        $reports = Report::with('form', 'result')
            ->whereIn('company_id', $companyIds)
            ->where(fn($q) => $request->year ? $q->where('year', $request->year) : $q)
            ->orderByDesc('facility_id')
            ->get();
        $facilities = Facility::withTrashed()->whereIn('company_id', $companyIds)->get();
        Report::mapResults($reports);
        if (!$request->year && $years->count() > 0) {
            //$reports = $reports->where('year', $years[0]);
        }
        $data = [];
        foreach ($reports->groupBy('company_id') as $companyId => $companyReports) {
            foreach ($companyReports->groupBy('facility_id') as $facilityId => $facilityReports) {
                $key = $companyId . '-' . $facilityId;
                $totalGas = collect($facilityReports)->sum(fn($r) => $r->result->greenhouse_gas_double);
                $data[$key] = [
                    'companyId' => $companyId,
                    'facilityId' => $facilityId,
                    'facilityName' => $facilities->where('id', $facilityId)->first()->name,
                    'totalGas' => GeneralUtil::numberFormat($totalGas),
                    'totalGasDouble' => $totalGas,
                ];
            }
        }
        return (new GeneralResponse())->setData([
            'years' => $years,
            'reports' => array_values($data),
        ])->toJson();
    }

    public function getReportFiles(GetReportFileRequest $request): JsonResponse
    {
        $companyIds = explode(',', $request->companyIds);
        $reports = Report::with('form.languages', 'user', 'result', 'files')
            ->whereIn('company_id', $companyIds)
            ->has('files')
            ->orderBy('form_id')
            ->get();
        Report::addLangProperties($reports, 'form');
        Report::mapResults($reports);

        $data = [];
        foreach ($reports->groupBy('company_id') as $companyId => $companyReports) {
            foreach ($companyReports as $r) {
                $data[] = [
                    'companyId' => $companyId,
                    'formName' => $r->form->name,
                    'email' => $r->user->email,
                    'files' => $r->files,
                    'totalGas' => GeneralUtil::numberFormat($r->result->greenhouse_gas_double),
                    'totalGasDouble' => $r->result->greenhouse_gas_double,
                ];
            }
        }
        return (new GeneralResponse())->setData(array_values($data))->toJson();
    }

    public function getFacilityCounts(GetFacilityCountRequest $request): JsonResponse
    {
        $companyIds = explode(',', $request->companyIds);
        $facilities = Facility::query()
            ->whereIn('company_id', $companyIds)
            ->get();
        $data = [];
        foreach ($facilities->groupBy('company_id') as $companyId => $companyFacilities) {
            $data[] = [
                'companyId' => $companyId,
                'facilityCount' => $companyFacilities->count(),
            ];
        }
        return (new GeneralResponse())->setData($data)->toJson();
    }

    public function login(LoginRequest $request): JsonResponse
    {
        // Login isteği bilgilerini logla
        Log::info('ESG Controller Login Request', [
            'email' => $request->email,
            'companyId' => $request->companyId,
            'companyName' => $request->companyName,
            'fullName' => $request->fullName,
            'jwtId' => $request->jwtId,
            'expiredAt' => $request->expiredAt,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toDateTimeString()
        ]);

        $user = User::query()->where('email', '=', $request->email)->first();
        $clientId = OauthPersonalAccessClient::query()
            ->orderByDesc('created_at')
            ->firstOrFail()
            ->client_id;

        DB::beginTransaction();
        try {
            if ($request->companyId) {
                $company = Company::query()->findOrFail($request->companyId);
            } else if ($user?->company_id) {
                $company = Company::query()->findOrFail($user?->company_id);
            } else {
                $company = new Company();
                $company->name = $request->companyName;
                $company->personnel_number_id = CompanyPersonnelNumber::query()->firstOrFail()->id;
                $company->sector_id = CompanySector::query()->orderBy('name')->firstOrFail()->id;
                $company->save();
            }

            if (!$user) {
                $user = new User();
                $user->full_name = $request->fullName;
                $user->email = $request->email;
                $user->title = 'Manager';
                $user->password = 'ESG';
                $user->company_id = $company->id;
                $user->type_id = UserType::query()->firstOrFail()->id;
                $user->role_id = Role::query()->where('slug', '=', RoleEnum::MANAGER)->firstOrFail()->id;
                $user->email_verified_at = now();
                $user->save();
            }

            $accessToken = new OauthAccessToken();
            $accessToken->id = $request->jwtId;
            $accessToken->user_id = $user->id;
            $accessToken->client_id = $clientId;
            $accessToken->name = env('APP_NAME');
            $accessToken->scopes = '[]';
            $accessToken->revoked = false;
            $accessToken->expires_at = date('Y-m-d H:i:s', strtotime($request->expiredAt));
            $accessToken->save();

            // Başarılı login işlemini logla
            Log::info('ESG Controller Login Success', [
                'user_id' => $user->id,
                'company_id' => $user->company_id,
                'email' => $request->email,
                'access_token_id' => $accessToken->id,
                'client_id' => $clientId,
                'created_new_user' => !User::query()->where('email', '=', $request->email)->exists(),
                'created_new_company' => $request->companyId ? false : true,
                'timestamp' => now()->toDateTimeString()
            ]);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            // Hata durumunu detaylı logla
            Log::error('ESG Controller Login Failed', [
                'email' => $request->email,
                'companyId' => $request->companyId,
                'companyName' => $request->companyName,
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'timestamp' => now()->toDateTimeString()
            ]);

            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->setData([
            'carbonUserId' => $user->id,
            'carbonCompanyId' => $user->company_id,
            'carbonAudience' => $clientId,
        ])->toJson();
    }
}
