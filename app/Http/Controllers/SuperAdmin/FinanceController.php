<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Enums\RoleEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SuperAdmin\Finance\GetCompanyProfileRequest;
use App\Http\Requests\SuperAdmin\Finance\GetProfileRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Company;
use App\Models\Oauth\OauthAccessToken;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class FinanceController extends Controller
{
    public function getUserProfile(GetProfileRequest $request): JsonResponse
    {
        $accessToken = OauthAccessToken::query()
            ->where('revoked', false)
            ->where('expires_at', '>', now())
            ->findOrFail($request->jti);
        $user = User::with('role')->findOrFail($accessToken->user_id);

        return (new GeneralResponse())->setData([
            'id' => $user->id,
            'fullName' => $user->full_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'title' => $user->title,
            'companyId' => $user->company_id,
            'isManager' => $user->role->slug == RoleEnum::MANAGER->value,
        ])->toJson();
    }

    public function getCompaniesProfile(GetCompanyProfileRequest $request): JsonResponse
    {
        $companyIds = explode(',', $request->companyIds);
        $result = [];

        $companies = Company::with('facilities', 'companyNaceCodes.naceCode')
            ->whereIn('id', $companyIds)
            ->get();

        foreach ($companies as $company) {
            Company::mapNaceCodes($company);
            $result[] = [
                'id' => $company->id,
                'companyName' => $company->name,
                'taxNumber' => $company->tax_number,
                'naceCodes' => $company->naceCodes->pluck('code'),
                'facilities' => $company->facilities->map(function ($facility) {
                    return [
                        'id' => $facility->id,
                        'name' => $facility->name,
                    ];
                }),
            ];
        }
        return (new GeneralResponse())->setData($result)->toJson();
    }
}
