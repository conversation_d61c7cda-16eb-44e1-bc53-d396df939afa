<?php

namespace App\Http\Controllers;

use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\MonthEnum;
use App\Enums\RoleEnum;
use App\Enums\UserEnum;
use App\Http\Requests\EcoMentor\CalculateFormRequest;
use App\Http\Requests\EcoMentor\CreateFileUrlRequest;
use App\Http\Requests\EcoMentor\CreateReportRequest;
use App\Http\Requests\EcoMentor\GetCategoryRequest;
use App\Http\Requests\EcoMentor\GetFacilityRequest;
use App\Http\Requests\EcoMentor\GetFacilitySpecieRequest;
use App\Http\Requests\EcoMentor\GetFormDetailRequest;
use App\Http\Requests\EcoMentor\GetMonthRequest;
use App\Http\Requests\EcoMentor\GetReportRequest;
use App\Http\Requests\EcoMentor\GetSectorRequest;
use App\Http\Requests\EcoMentor\GetYearRequest;
use App\Http\Requests\EcoMentor\LoginRequest;
use App\Http\Requests\EcoMentor\RegisterRequest;
use App\Http\Requests\EcoMentor\SyncFacilityRequest;
use App\Http\Requests\Form\CalculateFormResultRequest;
use App\Http\Requests\Form\GetFormBySlugRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\FileService;
use App\Http\Services\FormService;
use App\Http\Services\ReportService;
use App\Models\Company;
use App\Models\CompanySector;
use App\Models\Facility;
use App\Models\FacilitySpecie;
use App\Models\Form;
use App\Models\FormCategory;
use App\Models\Report;
use App\Models\Role;
use App\Models\RolePrivilege;
use App\Models\User;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EcoMentorController extends Controller
{
    private readonly FormService $formService;
    private readonly ReportService $reportService;
    private readonly FileService $fileService;
    private readonly array $formCategories;

    public function __construct()
    {
        $this->formService = new FormService();
        $this->reportService = new ReportService();
        $this->fileService = new FileService();
        $this->formCategories = [1, 2];
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $user = User::with(['company' => fn($q) => $q->select('id', 'name')])
            ->select('id', 'full_name', 'email', 'phone', 'title', 'photo', 'company_id', 'eco_mentor_token')
            ->where('email', $request->email)
            ->whereNotNull('eco_mentor_token')
            ->first();
        if ($user) {
            $user->makeHidden(['company_id']);
            return (new GeneralResponse())->setData($user)->toJson();
        }
        return (new GeneralResponse(false))->toJson();
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $roleId = Role::query()
            ->where('slug', '=', RoleEnum::MANAGER)
            ->firstOrFail()
            ->id;
        $privilegeId = RolePrivilege::query()
            ->where('slug', '=', RolePrivilegeEnum::COMPANY)
            ->firstOrFail()
            ->id;
        $password = Str::random(rand(8, 12));
        $ecoMentorToken = Str::random(rand(150, 255));

        DB::beginTransaction();
        try {
            $company = new Company();
            $company->name = $request->companyName;
            $company->sector_id = $request->companySectorId;
            $company->tax_number = $request->companyTaxNumber;
            $company->save();

            $user = new User();
            $user->full_name = $request->fullName;
            $user->email = $request->email;
            $user->title = 'Yönetici';
            $user->password = Hash::make($password);
            $user->company_id = $company->id;
            $user->type_id = UserEnum::FIRM;
            $user->role_id = $roleId;
            $user->privilege_id = $privilegeId;
            $user->kvkk_approved = $request->kvkkApproved;
            $user->clarification_text = $request->clarificationText;
            $user->privacy_policy = $request->privacyPolicy;
            $user->eco_mentor_token = $ecoMentorToken;
            $user->email_verified_at = now();
            $user->save();

            DB::commit();
            return (new GeneralResponse())->setData(['ecoMentorToken' => $ecoMentorToken])->toJson();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
    }

    public function getFacilities(GetFacilityRequest $request): JsonResponse
    {
        $facilities = Facility::query()
            ->select('id', 'name', 'address', 'code', 'specie_id')
            ->where('company_id', auth()->user()->company_id)
            ->orderBy('code')
            ->get();
        return (new GeneralResponse())->setData($facilities)->toJson();
    }

    public function getFacilitySpecies(GetFacilitySpecieRequest $request): JsonResponse
    {
        $species = FacilitySpecie::query()
            ->select('id', 'name')
            ->orderBy('order')
            ->get();
        return (new GeneralResponse())->setData($species)->toJson();
    }

    public function syncFacilities(SyncFacilityRequest $request): JsonResponse
    {
        $lastFacility = Facility::query()
            ->where('company_id', auth()->user()->company_id)
            ->orderByDesc('code')
            ->first();
        $code = $lastFacility ? $lastFacility->code + 1 : 1;

        DB::beginTransaction();
        try {
            foreach ($request->all() as $f) {
                if (isset($f['id'])) {
                    $facility = Facility::query()
                        ->where('id', $f['id'])
                        ->where('company_id', auth()->user()->company_id)
                        ->firstOrFail();
                } else {
                    $facility = new Facility();
                    $facility->company_id = auth()->user()->company_id;
                    $facility->code = $code++;
                }
                $facility->name = $f['name'];
                $facility->address = $f['address'];
                $facility->specie_id = $f['specieId'];
                $facility->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return $this->getFacilities(new GetFacilityRequest());
    }

    public function getSectors(GetSectorRequest $request): JsonResponse
    {
        $sectors = CompanySector::query()
            ->select('id', 'name')
            ->orderBy('name')
            ->get();
        return (new GeneralResponse())->setData($sectors)->toJson();
    }

    public function getYears(GetYearRequest $request): JsonResponse
    {
        $years = GeneralUtil::getYearsAsArray();
        return (new GeneralResponse())->setData($years)->toJson();
    }

    public function getMonths(GetMonthRequest $request): JsonResponse
    {
        return (new GeneralResponse())->setData(MonthEnum::getMonths())->toJson();
    }

    public function getCategories(GetCategoryRequest $request): JsonResponse
    {
        $categories = FormCategory::with([
            'languages',
            'forms' => fn($q) => $q->select('id', 'is_locked', 'category_id'),
            'forms.languages',
        ])
            ->select('id')
            ->whereIn('id', $this->formCategories)
            ->orderBy('order')
            ->get();
        FormCategory::addLangProperties($categories);
        Form::addLangProperties($categories, 'forms');
        foreach ($categories as $c) {
            $c->forms->makeHidden(['category_id']);
        }
        return (new GeneralResponse())->setData($categories->toArray())->toJson();
    }

    public function getFormDetail(GetFormDetailRequest $request): JsonResponse
    {
        $form = Form::query()
            ->whereIn('category_id', $this->formCategories)
            ->findOrFail($request->formId);
        $formBySlugReq = new GetFormBySlugRequest();
        $formBySlugReq->merge($request->all());

        $formResult = $this->formService->getFormsBySlug($formBySlugReq, $form->slug);
        $formResult->makeHidden(['slug', 'category_id', 'order', 'report_lines', 'has_gwp', 'uncertainty_type']);
        $formResult->category->makeHidden(['color', 'order']);
        foreach ($formResult->inputs as $input) {
            $input->makeHidden(['order', 'form_id', 'is_uncertainty']);
        }
        return (new GeneralResponse())->setData($formResult)->toJson();
    }

    public function calculateForm(CalculateFormRequest $request): JsonResponse
    {
        Form::query()
            ->whereIn('category_id', $this->formCategories)
            ->findOrFail($request->formId);
        $formResultReq = new CalculateFormResultRequest();
        $formResultReq->merge($request->all());

        $formResult = $this->formService->calculateFormResults($formResultReq);
        unset($formResult['uncertainty_value']);
        unset($formResult['uncertainty_value_double']);
        return (new GeneralResponse())->setData($formResult)->toJson();
    }

    public function getReports(GetReportRequest $request): JsonResponse
    {
        $reports = Report::query()
            ->with([
                'form.languages',
                'form.category.languages',
                'form.commons.groups',
                'user' => fn($q) => $q->select('id', 'full_name', 'email'),
                'facility' => fn($q) => $q->select('id', 'name'),
                'files' => fn($q) => $q->select('id', 'user_id', 'report_id', 'url', 'created_at'),
                'files.user',
                'result.values.groupValueLanguages',
            ])
            ->where('company_id', auth()->user()->company_id)
            ->whereHas('form', fn($q) => $q->whereIn('category_id', $this->formCategories));
        if ($request->formId) {
            $reports = $reports->where('form_id', $request->formId);
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->monthIds) {
            foreach (explode(',', $request->monthIds) as $monthId) {
                $reports = $reports->whereRaw('FIND_IN_SET(?, months)', [$monthId]);
            }
        }
        $reports = $reports->get();
        Report::mapResults($reports);
        Report::mapMonths($reports);

        foreach ($reports as $report) {
            $report->greenhouse_gas_double = $report->result->greenhouse_gas_double;
            $report->greenhouse_gas = GeneralUtil::numberFormat($report->result->greenhouse_gas_double);
        }
        $reports->makeHidden(['form_id', 'form', 'user_id', 'company_id', 'facility_id', 'result_id', 'result']);
        return (new GeneralResponse())->setData($reports)->toJson();
    }

    public function createReport(CreateReportRequest $request): JsonResponse
    {
        Form::query()
            ->whereIn('category_id', $this->formCategories)
            ->findOrFail($request->formId);
        $createReportReq = new \App\Http\Requests\Report\CreateReportRequest();
        $createReportReq->merge($request->all());

        $this->reportService->createReport($createReportReq);
        return (new GeneralResponse())->toJson();
    }

    public function createFileUrl(CreateFileUrlRequest $request): JsonResponse
    {
        $createFileUrlReq = new \App\Http\Requests\File\CreateFileUrlRequest();
        $createFileUrlReq->merge($request->all());

        $fileManager = $this->fileService->createFileUrl($createFileUrlReq);
        $fileManager->makeHidden(['s3']);
        return (new GeneralResponse())->setData($fileManager)->toJson();
    }
}
