<?php

namespace App\Http\Controllers;

use App\Enums\LanguageEnum;
use App\Http\Requests\Config\GetConfigRequest;
use App\Http\Requests\Config\GetLanguageRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Config;
use Illuminate\Http\JsonResponse;

class ConfigController extends Controller
{
    public function getConfigs(GetConfigRequest $request): JsonResponse
    {
        $configs = Config::all();
        return (new GeneralResponse())->setData($configs->toArray())->toJson();
    }

    public function getLanguages(GetLanguageRequest $request): JsonResponse
    {
        $languages = LanguageEnum::getLanguages();
        return (new GeneralResponse())->setData($languages)->toJson();
    }
}
