<?php

namespace App\Http\Controllers;

use App\Http\Requests\Education\GetEducationRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Edu\EduDepartment;
use App\Models\Edu\EduFaculty;
use App\Models\Edu\EduUniversity;
use Illuminate\Http\JsonResponse;

class EducationController extends Controller
{
    public function getEducations(GetEducationRequest $request): JsonResponse
    {
        $data = [
            'universities' => EduUniversity::all(),
            'faculties' => EduFaculty::all(),
            'departments' => EduDepartment::all(),
        ];
        return (new GeneralResponse())->setData($data)->toJson();
    }
}
