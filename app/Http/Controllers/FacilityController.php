<?php

namespace App\Http\Controllers;

use App\Http\Requests\Facility\CreateFacilityRequest;
use App\Http\Requests\Facility\DeleteFacilityRequest;
use App\Http\Requests\Facility\GetFacilityRequest;
use App\Http\Requests\Facility\GetSpecieRequest;
use App\Http\Requests\Facility\UpdateFacilityRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Facility;
use App\Models\FacilitySpecie;
use App\Models\TipContentUser;
use App\Models\Report;
use Illuminate\Http\JsonResponse;

class FacilityController extends Controller
{
    public function getSpecies(GetSpecieRequest $request): JsonResponse
    {
        $species = FacilitySpecie::query()->orderBy('order')->get();
        return (new GeneralResponse())->setData($species->toArray())->toJson();
    }

    public function getFacilities(GetFacilityRequest $request): JsonResponse
    {
        $facilities = Facility::query()
            ->where('company_id', auth()->user()->company_id)
            ->orderBy('code')
            ->get();
        return (new GeneralResponse())->setData($facilities->toArray())->toJson();
    }

    public function createFacilities(CreateFacilityRequest $request): JsonResponse
    {
        $code = 1;
        $facility = Facility::query()
            ->where('company_id', auth()->user()->company_id)
            ->orderByDesc('code')
            ->first();
        if ($facility) {
            $code = $facility->code + 1;
        }
        $newFacility = new Facility();
        $newFacility->name = $request->name;
        $newFacility->address = $request->address;
        $newFacility->specie_id = $request->specieId;
        $newFacility->company_id = auth()->user()->company_id;
        $newFacility->code = $code;
        $newFacility->save();

        TipContentUser::addTip('facility-save');
        return (new GeneralResponse())->toJson();
    }

    public function updateFacilities(UpdateFacilityRequest $request, int $facilityId): JsonResponse
    {
        $facility = Facility::query()
            ->where('id', $facilityId)
            ->where('company_id', auth()->user()->company_id)
            ->firstOrFail();
        $facility->name = $request->name;
        $facility->address = $request->address;
        $facility->specie_id = $request->specieId;
        $facility->save();
        return (new GeneralResponse())->toJson();
    }

    public function deleteFacilities(DeleteFacilityRequest $request, int $facilityId): JsonResponse
    {
        $facility = Facility::query()
            ->where('id', $facilityId)
            ->where('company_id', auth()->user()->company_id)
            ->firstOrFail();
        Report::query()->where('facility_id', $facility->id)->delete();
        $facility->delete();
        return (new GeneralResponse())->toJson();
    }
}
