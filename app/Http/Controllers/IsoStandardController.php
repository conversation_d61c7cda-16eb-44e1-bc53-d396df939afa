<?php

namespace App\Http\Controllers;

use App\Enums\MaterialityTypeEnum;
use App\Http\Requests\IsoStandard\CreateAccessTokenRequest;
use App\Http\Requests\IsoStandard\CreateIsoStandardRequest;
use App\Http\Requests\IsoStandard\DeleteIsoStandardRequest;
use App\Http\Requests\IsoStandard\GetCategoriesGasRequest;
use App\Http\Requests\IsoStandard\GetClassificationRequest;
use App\Http\Requests\IsoStandard\GetMultiplierRequest;
use App\Http\Requests\IsoStandard\GetSourceAndClassificationRequest;
use App\Http\Requests\IsoStandard\GetIsoStandardUncertaintyRequest;
use App\Http\Requests\IsoStandard\GetYearsGasRequest;
use App\Http\Requests\IsoStandard\GetFacilityRequest;
use App\Http\Requests\IsoStandard\GetIsoStandardDetailRequest;
use App\Http\Requests\IsoStandard\GetIsoStandardRequest;
use App\Http\Requests\IsoStandard\GetMaterialityRequest;
use App\Http\Requests\IsoStandard\GetYearRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\IsoStandardService;
use App\Http\Services\UncertaintyService;
use App\Models\Config;
use App\Models\Form;
use App\Models\FormCategory;
use App\Models\FormGroupValue;
use App\Models\FormMultiplier;
use App\Models\IsoStandard;
use App\Models\IsoStandardClassification;
use App\Models\IsoStandardSource;
use App\Models\IsoStandardValue;
use App\Models\Report;
use App\Models\User;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class IsoStandardController extends Controller
{
    private readonly IsoStandardService $isoStandardService;
    private readonly UncertaintyService $uncertaintyService;

    public function __construct()
    {
        $this->isoStandardService = new IsoStandardService();
        $this->uncertaintyService = new UncertaintyService();
    }

    public function getIsoStandards(GetIsoStandardRequest $request): JsonResponse
    {
        $isoStandards = IsoStandard::query()
            ->where('company_id', auth()->user()->company_id)
            ->get();
        return (new GeneralResponse())->setData($isoStandards->toArray())->toJson();
    }

    public function getIsoStandardDetail(GetIsoStandardDetailRequest $request): JsonResponse
    {
        $isoStandard = IsoStandard::with('values')
            ->where('company_id', auth()->user()->company_id)
            ->findOrFail($request->id);
        IsoStandard::mapIsoStandards([$isoStandard]);
        return (new GeneralResponse())->setData($isoStandard->toArray())->toJson();
    }

    public function getYears(GetYearRequest $request): JsonResponse
    {
        $years = Report::query()
            ->select('year')
            ->where('company_id', auth()->user()->company_id)
            ->orderBy('year')
            ->pluck('year')
            ->unique()
            ->values();
        return (new GeneralResponse())->setData($years->toArray())->toJson();
    }

    public function getFacilities(GetFacilityRequest $request): JsonResponse
    {
        $facilities = Report::with('facility')
            ->where('company_id', auth()->user()->company_id)
            ->where('year', $request->year)
            ->orderBy('facility_id')
            ->get()
            ->unique('facility_id')
            ->map(fn($r) => ['id' => $r->facility->id, 'name' => $r->facility->name])
            ->values();
        return (new GeneralResponse())->setData($facilities->toArray())->toJson();
    }

    public function getMaterialities(GetMaterialityRequest $request): JsonResponse
    {
        $ignoreFormIds = explode(',', Config::query()->where('key', 'iso_standard_materiality_ignore_form_ids')->firstOrFail()->value);
        $forms = Form::with('languages', 'category')->get();
        Form::addLangProperties($forms);
        $reports = Report::with('form', 'result')->where('company_id', auth()->user()->company_id);
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        Report::mapResults($reports);
        $result = [];
        $totalGas = $reports->sum(fn($r) => $r->result->greenhouse_gas_double);
        foreach ($reports->groupBy('form_id') as $formId => $formReports) {
            $formGas = collect($formReports)->sum(fn($r) => $r->result->greenhouse_gas_double);
            $form = $forms->where('id', $formId)->first();
            $percent = ($formGas / $totalGas) * 100;
            $result[] = [
                'form_id' => $form->id,
                'form_name' => $form->name,
                'form_order' => $form->order,
                'category_order' => $form->category->order,
                'form_gas' => GeneralUtil::numberFormat($formGas),
                'percent' => GeneralUtil::numberFormat($percent, 2),
                'materiality_type' => in_array($form->id, $ignoreFormIds) ? MaterialityTypeEnum::IGNORED : ($percent >= $request->materialityLimit ? MaterialityTypeEnum::IMPORTANT : MaterialityTypeEnum::NON_IMPORTANT),
            ];
        }
        usort($result, fn($r1, $r2) => $r1['form_order'] <=> $r2['form_order']);
        usort($result, fn($r1, $r2) => $r1['category_order'] <=> $r2['category_order']);
        foreach ($result as &$r) {
            unset($r['form_order']);
            unset($r['category_order']);
        }
        return (new GeneralResponse())->setData(array_values($result))->toJson();
    }

    public function getYearsGases(GetYearsGasRequest $request): JsonResponse
    {
        $years = collect(explode(',', $request->years))->unique()->values()->toArray();
        $reports = Report::with('form', 'result')
            ->where('company_id', auth()->user()->company_id)
            ->whereIn('year', $years)
            ->orderBy('year');
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        Report::mapResults($reports);
        $result = [];
        $reportedYears = [];
        foreach ($reports->groupBy('year') as $year => $groupedReports) {
            $totalGas = collect($groupedReports)->sum(fn($r) => $r->result->greenhouse_gas_double);
            $result[] = [
                'year' => $year,
                'total_gas' => GeneralUtil::numberFormat($totalGas),
                'total_gas_double' => $totalGas,
            ];
            $reportedYears[] = $year;
        }
        foreach (array_diff($years, $reportedYears) as $y) {
            $result[] = [
                'year' => +$y,
                'total_gas' => GeneralUtil::numberFormat(0),
                'total_gas_double' => 0,
            ];
        }
        usort($result, fn($a, $b) => $a['year'] <=> $b['year']);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getCategoriesGases(GetCategoriesGasRequest $request): JsonResponse
    {
        $result = $this->isoStandardService->getCategoriesGases($request);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getClassifications(GetClassificationRequest $request): JsonResponse
    {
        $reports = Report::with('form', 'result.values')->where('company_id', auth()->user()->company_id);
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        Report::mapResults($reports);
        $groupValueIds = $reports
            ->map(fn($r) => $r->result->values->map(fn($v) => $v->group_value_id))
            ->flatten(1)
            ->unique()
            ->filter(fn($groupValueId) => $groupValueId)
            ->values();
        $classifications = IsoStandardClassification::query()
            ->whereIn('group_value_id', $groupValueIds)
            ->get();
        return (new GeneralResponse())->setData(
            IsoStandardClassification::mapClassifications($classifications)
        )->toJson();
    }

    public function getSourcesAndClassifications(GetSourceAndClassificationRequest $request): JsonResponse
    {
        $reports = Report::with('form', 'result.values')->where('company_id', auth()->user()->company_id);
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        $formIds = $reports->pluck('form_id')->unique()->values();
        $categories = FormCategory::with('languages', 'forms.languages', 'forms.sources')
            ->whereHas('forms', fn($q) => $q->whereIn('id', $formIds))
            ->orderBy('order')
            ->get();
        $classifications = IsoStandardClassification::all();
        Report::mapResults($reports);
        FormCategory::addLangProperties($categories);
        FormCategory::addLangProperties($categories, 'forms');
        foreach ($categories as $c) {
            $forms = clone $c->forms->whereIn('id', $formIds)->values();
            foreach ($forms as $f) {
                IsoStandardSource::mapSources($f->sources);
                $groupValueIds = $reports
                    ->where('form_id', $f->id)
                    ->map(fn($r) => $r->result->values->map(fn($v) => $v->group_value_id))
                    ->flatten(1)
                    ->unique()
                    ->filter(fn($groupValueId) => $groupValueId)
                    ->values();
                $f->classifications = IsoStandardClassification::mapClassifications($classifications->whereIn('group_value_id', $groupValueIds));
            }
            unset($c->forms);
            $c->forms = $forms;
        }
        return (new GeneralResponse())->setData($categories->toArray())->toJson();
    }

    public function getMultipliers(GetMultiplierRequest $request): JsonResponse
    {
        $reports = Report::with('form', 'result.values')->where('company_id', auth()->user()->company_id);
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        Report::mapResults($reports);
        $groupValueIds = $reports
            ->map(fn($r) => $r->result->values->map(fn($v) => $v->group_value_id))
            ->flatten(1)
            ->unique()
            ->filter(fn($groupValueId) => $groupValueId)
            ->values();
        $multipliers = FormMultiplier::query()
            ->whereIn('form_id', $reports->pluck('form_id')->unique()->toArray())
            ->where('is_unit', false)
            ->where(fn($q) => $groupValueIds->map(fn($vId) => $q->orWhereJsonContains('group_value_ids', $vId)))
            ->get()
            ->each(function ($m) {
                $m->group_value_ids = json_decode($m->group_value_ids);
                $m->multiplier = json_decode($m->multiplier);
                foreach ($m->multiplier as $key => $mData) {
                    $m->multiplier->$key = GeneralUtil::numberFormat($mData);
                }
            });
        $values = FormGroupValue::with('languages')
            ->whereIn('id', $groupValueIds)
            ->get();
        FormGroupValue::addLangProperties($values);
        $result = [];
        foreach ($values as $v) {
            $multiplier = $multipliers->first(fn($m) => in_array($v->id, $m->group_value_ids))?->multiplier;
            if ($multiplier && count((array)$multiplier) > 0) {
                $result[$v->name] = [
                    'name' => $v->name,
                    'multiplier' => $multiplier,
                ];
            }
        }
        ksort($result);
        return (new GeneralResponse())->setData(array_values($result))->toJson();
    }

    public function getUncertainties(GetIsoStandardUncertaintyRequest $request): JsonResponse
    {
        $reports = Report::with('form', 'result.values')
            ->where('company_id', auth()->user()->company_id)
            ->whereHas('uncertainty');
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        $formIds = $reports->pluck('form_id')->unique()->values();

        $categories = FormCategory::with([
            'languages',
            'forms' => fn($q) => $q->select('id', 'slug', 'category_id', 'order', 'uncertainty_type')->whereIn('id', $formIds),
            'forms.languages',
        ])
            ->whereHas('forms', fn($q) => $q->whereIn('id', $formIds))
            ->orderBy('order')
            ->get();
        Report::mapResults($reports);
        Report::mapUncertainties($reports);
        FormCategory::addLangProperties($categories);
        FormCategory::addLangProperties($categories, 'forms');
        foreach ($categories as $c) {
            foreach ($c->forms as $form) {
                $formReports = $reports->where('form_id', $form->id);

                $greenhouseGasDouble = $formReports->sum(fn($r) => $r->result->greenhouse_gas_double);
                $form->greenhouse_gas = GeneralUtil::numberFormat($greenhouseGasDouble);
                $form->greenhouse_gas_double = $greenhouseGasDouble;

                $uncertaintyDouble = $this->uncertaintyService->calculateReport($formReports->toArray());
                $form->uncertainty_value = GeneralUtil::numberFormat($uncertaintyDouble * 100, 2);
                $form->uncertainty_value_double = $uncertaintyDouble;
            }
            $greenhouseGasDouble = $c->forms->sum(fn($form) => $form->greenhouse_gas_double);
            $c->greenhouse_gas = GeneralUtil::numberFormat($greenhouseGasDouble);
            $c->greenhouse_gas_double = $greenhouseGasDouble;

            $uncertaintyDouble = $this->uncertaintyService->calculateReport($c->forms->toArray());
            $c->uncertainty_value = GeneralUtil::numberFormat($uncertaintyDouble * 100, 2);
            $c->uncertainty_value_double = $uncertaintyDouble;
        }

        $greenhouseGasDouble = $categories->sum(fn($c) => $c->greenhouse_gas_double);
        $uncertaintyDouble = $this->uncertaintyService->calculateReport($categories->toArray());
        $categories->prepend((object)[
            'id' => 0,
            'order' => 0,
            'name' => 'Toplam',
            'greenhouse_gas' => GeneralUtil::numberFormat($greenhouseGasDouble),
            'greenhouse_gas_double' => $greenhouseGasDouble,
            'uncertainty_value' => GeneralUtil::numberFormat($uncertaintyDouble * 100, 2),
            'uncertainty_value_double' => $uncertaintyDouble,
        ]);
        return (new GeneralResponse())->setData($categories)->toJson();
    }

    public function createIsoStandard(CreateIsoStandardRequest $request): JsonResponse
    {
        if ($request->id) {
            $isoStandard = IsoStandard::query()
                ->where('company_id', auth()->user()->company_id)
                ->findOrFail($request->id);
        }
        DB::beginTransaction();
        try {
            if (!$request->id) {
                $isoStandard = new IsoStandard();
                $isoStandard->user_id = auth()->id();
                $isoStandard->company_id = auth()->user()->company_id;
                $isoStandard->code = Str::random(random_int(200, 255));
                $isoStandard->save();
            }
            if (isset($request->values)) {
                foreach ($request->values as $value) {
                    $isoStandardValue = IsoStandardValue::query()
                        ->where('iso_standard_id', $isoStandard->id)
                        ->where('key', $value['key'])
                        ->first();
                    if (!$isoStandardValue) {
                        $isoStandardValue = new IsoStandardValue();
                        $isoStandardValue->iso_standard_id = $isoStandard->id;
                        $isoStandardValue->key = $value['key'];
                    }
                    $isoStandardValue->value = json_encode($value['value']);
                    $isoStandardValue->save();
                }
            }
            $isoStandard->updated_at = now();
            $isoStandard->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        IsoStandard::mapIsoStandards([$isoStandard]);
        return (new GeneralResponse())->setData($isoStandard->toArray())->toJson();
    }

    public function createAccessToken(CreateAccessTokenRequest $request): JsonResponse
    {
        $isoStandard = IsoStandard::query()
            ->where('code', $request->code)
            ->firstOrFail();
        $isoStandard->pdf_created = true;
        $isoStandard->save();

        $user = User::query()->findOrFail($isoStandard->user_id);
        $tokenResult = $user->createToken(env('APP_NAME'));
        $tokenResult->token->expires_at = now()->addMinutes(10);
        $tokenResult->token->save();
        return (new GeneralResponse())->setData([
            'iso_standard_id' => $isoStandard->id,
            'access_token' => $tokenResult->accessToken
        ])->toJson();
    }

    public function deleteIsoStandard(DeleteIsoStandardRequest $request): JsonResponse
    {
        IsoStandard::query()
            ->where('company_id', auth()->user()->company_id)
            ->findOrFail($request->id)
            ->delete();
        return (new GeneralResponse())->toJson();
    }
}
