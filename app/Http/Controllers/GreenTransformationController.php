<?php

namespace App\Http\Controllers;

use App\Http\Requests\GreenTransformation\CreateGreenTransformationRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\GreenTransformation;
use Illuminate\Http\JsonResponse;

class GreenTransformationController extends Controller
{
    public function createGreenTransformation(CreateGreenTransformationRequest $request): JsonResponse
    {
        $applications = $request->all();
        if (count($applications) == 0) {
            return (new GeneralResponse(false))
                ->setMessages('En az bir başvuru girmelisiniz.')
                ->toJson();
        }
        $existingEmails = GreenTransformation::query()
            ->whereIn('email', array_column($applications, 'email'))
            ->pluck('email')
            ->toArray();
        $emails = array_column($applications, 'email');
        $duplicateInRequest = array_unique(array_diff_assoc($emails, array_unique($emails)));

        if (!empty($duplicateInRequest)) {
            return (new GeneralResponse(false))
                ->setMessages('Birden fazla kez girilmiş email adresleri: ' . implode(', ', $duplicateInRequest))
                ->toJson();
        }
        if (!empty($existingEmails)) {
            return (new GeneralResponse(false))
                ->setMessages('Bu email adresleriyle daha önce başvuru yapılmış: ' . implode(', ', $existingEmails))
                ->toJson();
        }
        $maxCode = GreenTransformation::query()->max('code') ?? 0;
        $insertData = [];
        foreach ($applications as $index => $data) {
            $insertData[] = [
                'code' => $maxCode + 1,
                'sector_id' => $data['sectorId'],
                'full_name' => $data['fullName'],
                'email' => $data['email'],
                'title' => $data['title'],
                'phone' => $data['phone'],
                'company_name' => $data['companyName'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        GreenTransformation::insert($insertData);
        return (new GeneralResponse())->toJson();
    }
}
