<?php

namespace App\Http\Controllers;

use App\Enums\PermissionEnum;
use App\Http\Requests\Report\CreateReportRequest;
use App\Http\Requests\Report\DeleteReportRequest;
use App\Http\Requests\Report\GetCategoriesGasRequest;
use App\Http\Requests\Report\GetReportGroupByCategoryForPDFRequest;
use App\Http\Requests\Report\GetReportGroupByCategoryRequest;
use App\Http\Requests\Report\GetReportGroupByFacilityForPDFRequest;
use App\Http\Requests\Report\GetReportGroupByFacilityRequest;
use App\Http\Requests\Report\GetReportGroupByYearForPDFRequest;
use App\Http\Requests\Report\GetReportGroupByYearRequest;
use App\Http\Requests\Report\GetReportGroupByYearForAnalysisRequest;
use App\Http\Requests\Report\GetReportRequest;
use App\Http\Requests\Report\GetReportDetail;
use App\Http\Requests\Report\GetReportSummaryRequest;
use App\Http\Requests\Report\GetReportYearRequest;
use App\Http\Requests\Report\UpdateReportRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\FileService;
use App\Http\Services\FormService;
use App\Http\Services\IsoStandardService;
use App\Http\Services\ReportService;
use App\Http\Services\UncertaintyService;
use App\Models\Facility;
use App\Models\FileManager;
use App\Models\FormCategory;
use App\Models\FormGroup;
use App\Models\FormGroupValue;
use App\Models\FormInput;
use App\Models\FormResult;
use App\Models\Report;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class ReportController extends Controller
{
    private readonly ReportService $reportService;
    private readonly UncertaintyService $uncertaintyService;
    private readonly FormService $formService;
    private readonly FileService $fileService;
    private readonly IsoStandardService $isoStandardService;

    public function __construct()
    {
        $this->reportService = new ReportService();
        $this->formService = new FormService();
        $this->fileService = new FileService();
        $this->uncertaintyService = new UncertaintyService();
        $this->isoStandardService = new IsoStandardService();
    }

    public function getReports(GetReportRequest $request): JsonResponse
    {
        $result = $this->reportService->getReports($request);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getReportsSummary(GetReportSummaryRequest $request): JsonResponse
    {
        $reports = Report::with(['form.category', 'facility', 'result'])->where('company_id', auth()->user()->company_id);
        if ($request->formId) {
            $reports = $reports->where('form_id', $request->formId);
        }
        if ($request->formSlug) {
            $reports = $reports->whereHas('form', fn($q) => $q->where('slug', $request->formSlug));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->monthIds) {
            foreach (explode(',', $request->monthIds) as $monthId) {
                $reports = $reports->whereRaw('FIND_IN_SET(?, months)', [$monthId]);
            }
        }
        $reports = $reports->get();
        Report::mapResults($reports);
        Report::mapMonths($reports);
        return (new GeneralResponse())->setData($reports->toArray())->toJson();
    }

    public function getReportDetail(GetReportDetail $request): JsonResponse
    {
        $report = Report::with([
            'result.values',
            'form.commons.groups',
            'uncertainty.pedigrees',
            'uncertainty.electricity',
            'files.user',

        ])
            ->where('id', $request->reportId)
            ->where('company_id', auth()->user()->company_id)
            ->firstOrFail();
        try {
            $reportData = $this->getReportDetailData($request, $report);
            return (new GeneralResponse())->setData($reportData->toArray())->toJson();
        } catch (\Exception $e) {
            Log::error($e);
            return (new GeneralResponse(false, ResponseAlias::HTTP_NOT_ACCEPTABLE))
                ->setMessages(Lang::get('messages.report.detail_not_found'))
                ->toJson();
        }
    }

    private function getReportDetailData(GetReportDetail $request, Report $report): Report
    {
        $formInputs = FormInput::query()->where('form_id', $report->form_id)->orderBy('order')->get();
        FormInput::addLangProperties($formInputs);
        FormInput::mapRelatedInputIds($formInputs);

        $formSlug = $report->form->slug;
        $formCommonIdCacheKey = "form-common-id-" . auth()->id() . "-$formSlug-" . $request->header('Form-Unique-Key');

        $inputs = collect();
        if ($report->result) {
            if ($report->result->form_id != $report->form_id) {
                $firstResultValue = $report->result->values[0];
                $formCommon = $report->form->commons
                    ->where('common_form_id', $report->result->form_id)
                    ->filter(fn($c) => $c->groups->where('common_input_id', $firstResultValue->input_id)->where('common_group_value_id', $firstResultValue->group_value_id)->count() > 0)
                    ->first();
                Cache::put($formCommonIdCacheKey, $formCommon->id, now()->addMinutes(20));
            }
            Report::mapCommonInputs([$report]);
            Report::addMissingValues([$report], $formInputs);
            foreach ($report->result->values as $i => $value) {
                $group = FormGroup::with('values.languages')
                    ->whereHas('values', fn($v) => $v->where('id', $value->group_value_id))
                    ->firstOrFail();
                FormGroupValue::addLangProperties($group->values);
                $selectedValue = $group->values->first(fn($v) => $v->id == $value->group_value_id);
                if ($selectedValue) {
                    $selectedValue->is_selected = true;
                }
                $input = $formInputs->first(fn($inp) => $inp->id == $value->input_id);
                $inputs->push((object)[
                    ...$input->toArray(),
                    'values' => $group->values,
                    'text_value' => null,
                    'numeric_value' => $i == $report->result->values->count() - 1 ? $report->amount : null,
                ]);
            }
        } else {
            $payload = json_decode($report->payload);
            foreach ($payload as $item) {
                $group = null;
                if (isset($item->groupValueId)) {
                    $group = FormGroup::with('values.languages')
                        ->whereHas('values', fn($v) => $v->where('id', $item->groupValueId))
                        ->firstOrFail();
                    FormGroupValue::addLangProperties($group->values);
                    $selectedValue = $group->values->first(fn($v) => $v->id == $item->groupValueId);
                    if ($selectedValue) {
                        $selectedValue->is_selected = true;
                    }
                }
                $input = $formInputs->first(fn($inp) => $inp->id == $item->inputId);
                $inputs->push((object)[
                    ...$input->toArray(),
                    'values' => $group ? $group->values : [],
                    'text_value' => $item->textValue ?? null,
                    'numeric_value' => $item->numericValue ?? null,
                ]);
            }
        }

        Report::mapResults([$report]);
        if ($report->uncertainty) {
            $report->uncertainty->uncertainty_value_double = $report->uncertainty->uncertainty_value;
            $report->uncertainty->uncertainty_value = GeneralUtil::numberFormat($report->uncertainty->uncertainty_value * 100, 2);
        }

        $report->hasS3Files = !!$report->files->first(fn($fileManager) => $fileManager->s3);
        $report->inputs = $inputs;
        $report->makeHidden(['amount', 'form', 'payload']);
        return $report;
    }

    public function getReportsYears(GetReportYearRequest $request): JsonResponse
    {
        $reports = Report::query();//->where('company_id', auth()->user()->company_id);
        if ($request->formId) {
            $reports = $reports->where('form_id', $request->formId);
        }
        if ($request->facilityIds) {
            $facilityIds = explode(',', $request->facilityIds);
            if (count($facilityIds) == 1) {
                $reports = $reports->where('facility_id', $facilityIds[0]);
                $reportYears = $reports->get()->pluck('year')->unique()->sort()->values()->toArray();
            } else {
                $reports = $reports->where(fn($query) => array_map(fn($facilityId) => $query->orWhere('facility_id', $facilityId), $facilityIds))->get();
                $reportYears = [];
                foreach ($reports as $r) {
                    if (array_key_exists($r->year, $reportYears)) {
                        if (!in_array($r->facility_id, $reportYears[$r->year])) {
                            $reportYears[$r->year][] = $r->facility_id;
                        }
                    } else {
                        $reportYears[$r->year] = [$r->facility_id];
                    }
                }
                foreach ($reportYears as $year => $facilities) {
                    if (count($facilities) != count($facilityIds)) {
                        unset($reportYears[$year]);
                    }
                }
                ksort($reportYears);
                $reportYears = array_keys($reportYears);
            }
        } else {
            $reportYears = $reports->get()->pluck('year')->unique()->sort()->values()->toArray();
        }
        return (new GeneralResponse())->setData($reportYears)->toJson();
    }

    public function getReportsGroupByFacilities(GetReportGroupByFacilityRequest $request): JsonResponse
    {
        $yearsByFacilities = [];
        $reports = Report::with('form', 'result')->where('company_id', auth()->user()->company_id);
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        foreach ($reports as $r) {
            $yearsByFacilities[$r->year][] = $r->facility_id;
        }
        foreach ($yearsByFacilities as $i => &$facilityIds) {
            $facilityIds = array_unique($facilityIds);
            if (count($facilityIds) < 2) {
                unset($yearsByFacilities[$i]);
            }
        }
        krsort($yearsByFacilities);
        $years = array_keys($yearsByFacilities);
        if (count($years) == 0 || ($request->year && !array_key_exists($request->year, $yearsByFacilities))) {
            return (new GeneralResponse())->setData(['years' => $years, 'reports' => []])->toJson();
        } else if (!$request->year && count($years) > 0) {
            $request->replace(['year' => $years[0]]);
        }

        $facilities = Facility::query()->where('company_id', auth()->user()->company_id);
        if ($request->year) {
            $reports = $reports->where('year', $request->year)->whereIn('facility_id', $yearsByFacilities[$request->year]);
            $facilities = $facilities->whereIn('id', $yearsByFacilities[$request->year]);
        }
        $facilities = $facilities->orderBy('code')->get();
        Report::mapResults($reports);

        $reportsGroupByFacilities = [];
        foreach ($facilities as $facility) {
            $reportsGroupByFacilities[$facility->id] = [
                'facility_id' => $facility->id,
                'facility_name' => $facility->name,
                'total_greenhouse_gas' => 0,
            ];
        }
        foreach ($reports as $report) {
            $reportsGroupByFacilities[$report->facility_id]['total_greenhouse_gas'] += $report->result->greenhouse_gas_double;
        }
        foreach ($reportsGroupByFacilities as &$r) {
            $r['total_greenhouse_gas'] = GeneralUtil::numberFormat($r['total_greenhouse_gas']);
        }
        $reportsGroupByFacilities = count($reportsGroupByFacilities) > 1 ? array_values($reportsGroupByFacilities) : [];

        return (new GeneralResponse())->setData([
            'years' => $years,
            'reports' => $reportsGroupByFacilities,
        ])->toJson();
    }

    public function getReportsGroupByFacilitiesForPDF(GetReportGroupByFacilityForPDFRequest $request): JsonResponse
    {
        $result = [];
        $reportRequest = new GetReportGroupByFacilityRequest();
        $years = $this->getReportsGroupByFacilities($reportRequest)->getData()->data->years;
        foreach ($years as $year) {
            $reportRequest->merge(['year' => $year]);
            $result[] = [
                'year' => $year,
                'reports' => $this->getReportsGroupByFacilities($reportRequest)->getData()->data->reports,
            ];
        }
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getReportsGroupByYears(GetReportGroupByYearRequest $request): JsonResponse
    {
        $reports = Report::with('form', 'result')->where('company_id', auth()->user()->company_id);
        if ($request->formId) {
            $reports = $reports->where('form_id', $request->formId);
        }
        if ($request->facilityId) {
            $reports = $reports->where('facility_id', $request->facilityId);
        }
        $reports = $reports->get();
        Report::mapResults($reports);

        $reportsGroupByYears = [];
        foreach ($reports as $report) {
            if (array_key_exists($report->year, $reportsGroupByYears)) {
                $reportsGroupByYears[$report->year] += $report->result->greenhouse_gas_double;
            } else {
                $reportsGroupByYears[$report->year] = $report->result->greenhouse_gas_double;
            }
        }
        foreach ($reportsGroupByYears as $year => &$r) {
            $r = [
                'year' => $year,
                'total_greenhouse_gas' => GeneralUtil::numberFormat($r),
            ];
        }
        ksort($reportsGroupByYears);
        return (new GeneralResponse())->setData(array_values($reportsGroupByYears))->toJson();
    }

    public function getReportsGroupByYearsForPDF(GetReportGroupByYearForPDFRequest $request): JsonResponse
    {
        $reportRequest = new GetReportGroupByYearRequest();
        $result = [
            [
                'facility' => null, // for all facilities
                'reports' => $this->getReportsGroupByYears($reportRequest)->getData()->data,
            ]
        ];
        $facilities = Facility::query()->where('company_id', auth()->user()->company_id)->get();
        foreach ($facilities as $facility) {
            $reportRequest->merge(['facilityId' => $facility->id]);
            $result[] = [
                'facility' => $facility->name,
                'reports' => $this->getReportsGroupByYears($reportRequest)->getData()->data,
            ];
        }
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getReportsGroupByYearsForAnalysis(GetReportGroupByYearForAnalysisRequest $request): JsonResponse
    {
        $user = auth()->user();
        $allFormInputs = FormInput::with('languages')->get();
        $reports = Report::query()
            ->with([
                'form.languages',
                'form.category.languages',
                'form.commons.groups',
                'user' => fn($q) => $q->select('id', 'full_name', 'title', 'photo'),
                'facility',
                'files.user',
                'result.values.groupValueLanguages',
                'uncertainty',
            ])
            ->where('company_id', $user->company_id);
        if ($user->hasPermission(PermissionEnum::REPORT_SELF)) {
            $reports = $reports->where('user_id', $user->id);
        }
        if ($request->formId) {
            $reports = $reports->where('form_id', $request->formId);
        }
        if ($request->formSlug) {
            $reports = $reports->whereHas('form', fn($q) => $q->where('slug', $request->formSlug));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->monthIds) {
            foreach (explode(',', $request->monthIds) as $monthId) {
                $reports = $reports->whereRaw('FIND_IN_SET(?, months)', [$monthId]);
            }
        }
        $reports = $reports->get();
        $reports->makeHidden('form');
        Report::mapCommonInputs($reports);
        Report::mapResults($reports);
        Report::mapMonths($reports);
        Report::mapValueLanguages($reports);
        Report::mapUncertainties($reports);

        $result = [];
        foreach ($reports as $report) {
            $year = $report->year;
            $categoryId = $report->form->category_id;
            $formId = $report->form_id;

            if (isset($result[$year]['greenhouse_gas_double'])) {
                $result[$year]['greenhouse_gas_double'] += $report->result->greenhouse_gas_double;
            } else {
                $result[$year] = [
                    'year' => $year,
                    'greenhouse_gas' => '0',
                    'greenhouse_gas_double' => $report->result->greenhouse_gas_double,
                    'uncertainty_value' => '0',
                    'uncertainty_value_double' => 0,
                ];
            }
            $result[$year]['greenhouse_gas'] = GeneralUtil::numberFormat($result[$year]['greenhouse_gas_double']);

            if (isset($result[$year]['categories'][$categoryId]['greenhouse_gas_double'])) {
                $result[$year]['categories'][$categoryId]['greenhouse_gas_double'] += $report->result->greenhouse_gas_double;
            } else {
                $result[$year]['categories'][$categoryId] = [
                    'id' => $categoryId,
                    'name' => $report->form->category->name,
                    'order' => $report->form->category->order,
                    'greenhouse_gas' => '0',
                    'greenhouse_gas_double' => $report->result->greenhouse_gas_double,
                    'uncertainty_value' => '0',
                    'uncertainty_value_double' => 0,
                ];
            }
            $result[$year]['categories'][$categoryId]['greenhouse_gas'] = GeneralUtil::numberFormat($result[$year]['categories'][$categoryId]['greenhouse_gas_double']);

            if (isset($result[$year]['categories'][$categoryId]['forms'][$formId]['greenhouse_gas_double'])) {
                $result[$year]['categories'][$categoryId]['forms'][$formId]['greenhouse_gas_double'] += $report->result->greenhouse_gas_double;
                $result[$year]['categories'][$categoryId]['forms'][$formId]['reports'][] = $report;
            } else {
                $formReports = $reports->where('year', $year)->where('form_id', $formId);
                $uncertaintyDouble = $this->uncertaintyService->calculateReport($formReports->toArray());

                $inputs = $allFormInputs->where('form_id', $formId)->sortBy('order')->values();
                FormInput::addLangProperties($inputs);
                $result[$year]['categories'][$categoryId]['forms'][$formId] = [
                    'id' => $formId,
                    'name' => $report->form->name,
                    'description' => $report->form->description,
                    'order' => $report->form->order,
                    'report_lines' => $report->form->report_lines,
                    'greenhouse_gas' => '0',
                    'greenhouse_gas_double' => $report->result->greenhouse_gas_double,
                    'uncertainty_value' => GeneralUtil::numberFormat($uncertaintyDouble * 100, 2),
                    'uncertainty_value_double' => $uncertaintyDouble,
                    'inputs' => $inputs,
                    'reports' => [$report],
                ];
            }
            $result[$year]['categories'][$categoryId]['forms'][$formId]['greenhouse_gas'] = GeneralUtil::numberFormat($result[$year]['categories'][$categoryId]['forms'][$formId]['greenhouse_gas_double']);

            Report::addMissingValues([$report], $result[$year]['categories'][$categoryId]['forms'][$formId]['inputs']);
            $report->hasS3Files = !!$report->files->first(fn($fileManager) => $fileManager->s3);
        }

        krsort($result);
        $result = array_values($result);
        foreach ($result as &$yearReport) {
            foreach ($yearReport['categories'] as &$categoryReport) {
                $uncertaintyDouble = $this->uncertaintyService->calculateReport($categoryReport['forms']);
                $categoryReport['uncertainty_value'] = GeneralUtil::numberFormat($uncertaintyDouble * 100, 2);
                $categoryReport['uncertainty_value_double'] = $uncertaintyDouble;

                usort($categoryReport['forms'], fn($a, $b) => $a['order'] <=> $b['order']);
            }
            $uncertaintyDouble = $this->uncertaintyService->calculateReport($yearReport['categories']);
            $yearReport['uncertainty_value'] = GeneralUtil::numberFormat($uncertaintyDouble * 100, 2);
            $yearReport['uncertainty_value_double'] = $uncertaintyDouble;

            usort($yearReport['categories'], fn($a, $b) => $a['order'] <=> $b['order']);
        }
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getReportsGroupByCategories(GetReportGroupByCategoryRequest $request): JsonResponse
    {
        $reports = Report::with('form', 'result')->where('company_id', auth()->user()->company_id);
        if ($request->years) {
            $reports = $reports->whereIn('year', explode(',', $request->years));
        }
        if ($request->facilityIds) {
            $reports = $reports->whereIn('facility_id', explode(',', $request->facilityIds));
        }
        if ($request->formIds) {
            $reports = $reports->whereIn('form_id', explode(',', $request->formIds));
        }
        $reports = $reports->get();
        Report::mapResults($reports);

        $categories = FormCategory::with('languages')->get();
        FormCategory::addLangProperties($categories);

        $reportsGroupByCategories = [];
        foreach ($reports as $report) {
            if (array_key_exists($report->form->category_id, $reportsGroupByCategories)) {
                $reportsGroupByCategories[$report->form->category_id] += $report->result->greenhouse_gas_double;
            } else {
                $reportsGroupByCategories[$report->form->category_id] = $report->result->greenhouse_gas_double;
            }
        }
        foreach ($reportsGroupByCategories as $categoryId => &$r) {
            $category = $categories->find($categoryId);
            $r = [
                'category_id' => $category->id,
                'category_name' => $category->name,
                'category_color' => $category->color,
                'total_greenhouse_gas' => GeneralUtil::numberFormat($r),
            ];
        }
        ksort($reportsGroupByCategories);
        return (new GeneralResponse())->setData(array_values($reportsGroupByCategories))->toJson();
    }

    public function getReportsGroupByCategoriesForPDF(GetReportGroupByCategoryForPDFRequest $request): JsonResponse
    {
        $reportRequest = new GetReportGroupByCategoryRequest();
        $yearRequest = new GetReportYearRequest();
        $result = [];
        $facilities = Facility::query()->where('company_id', auth()->user()->company_id)->get();
        foreach ($facilities as $facility) {
            $yearRequest->merge(['facilityIds' => $facility->id]);
            $years = $this->getReportsYears($yearRequest)->getData()->data;
            foreach ($years as $year) {
                $reportRequest->merge(['facilityIds' => $facility->id, 'years' => $year]);
                $result[] = [
                    'facility' => $facility->name,
                    'year' => $year,
                    'reports' => $this->getReportsGroupByCategories($reportRequest)->getData()->data,
                ];
            }
        }
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function getCategoriesGases(GetCategoriesGasRequest $request): JsonResponse
    {
        $categoriesGassesReq = new \App\Http\Requests\IsoStandard\GetCategoriesGasRequest();
        $categoriesGassesReq->merge($request->all());
        $categoriesGassesReq->merge(['divider' => 1]);
        $result = $this->isoStandardService->getCategoriesGases($categoriesGassesReq);
        return (new GeneralResponse())->setData($result)->toJson();
    }

    public function createReport(CreateReportRequest $request): JsonResponse
    {
        $report = $this->reportService->createReport($request);
        return (new GeneralResponse())->setData(['id' => $report->id])->toJson();
    }

    public function updateReport(UpdateReportRequest $request): JsonResponse
    {
        $report = Report::with('form')
            ->where('company_id', auth()->user()->company_id)
            ->findOrFail($request->reportId);

        $gasses = [];
        if ($request->payload) {
            $gasses = $this->formService->calculateFormulaGasses($report->form_id, $request->payload);
        }

        DB::beginTransaction();
        try {
            $report->facility_id = $request->facilityId;
            $report->result_id = $request->resultId ?? null;
            $report->months = implode(',', $request->monthIds);
            $report->year = $request->year;
            $report->amount = $request->amount;
            $report->carbon_dioxide = $gasses['carbon_dioxide'] ?? null;
            $report->nitrous_oxide = $gasses['nitrous_oxide'] ?? null;
            $report->methane = $gasses['methane'] ?? null;
            $report->extra_gas = $gasses['extra_gas'] ?? null;
            $report->payload = $request->payload ?? null;
            $report->description = $request->description ?? null;
            $report->save();

            if ($request->resultId) {
                $formResult = FormResult::with('values')->findOrFail($request->resultId);
                $groupValueIds = $formResult->values->pluck('group_value_id')->toArray();
                $gasses = $this->formService->calculateStandardGasses($formResult, $request->amount);
            } else {
                $groupValueIds = collect($request->payload)->filter(fn($v) => isset($v['groupValueId']))->map(fn($v) => $v['groupValueId'])->toArray();
            }
            $this->uncertaintyService->upsertUncertaintyReport(
                $report->id,
                $report->form,
                $gasses,
                $groupValueIds,
                $request->errorMargin,
                $request->isCalibration,
                $request->uncertaintyValue,
                $request->pedigreeTabs,
                $request->electricity,
            );

            if (isset($request->fileIds)) {
                FileManager::query()
                    ->whereIn('id', $request->fileIds)
                    ->update(['report_id' => $report->id]);
                $deleteFileManagersQuery = FileManager::query()
                    ->whereNotIn('id', $request->fileIds)
                    ->where('report_id', $report->id);
                $deleteFileManagers = $deleteFileManagersQuery->get();
                $deleteFileManagersQuery->delete();

                $paths = $deleteFileManagers
                    ->filter(fn($fileManager) => $fileManager->s3)
                    ->map(fn($fileManager) => $this->fileService->getFilePath($fileManager->url));
                Storage::disk('s3')->delete($paths);
            }

            DB::commit();
            return (new GeneralResponse())->setData(['id' => $report->id])->toJson();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
    }

    public function deleteReport(DeleteReportRequest $request, int $reportId): JsonResponse
    {
        $report = Report::query()
            ->where('id', $reportId)
            ->where('company_id', auth()->user()->company_id)
            ->firstOrFail();
        $report->delete();
        return (new GeneralResponse())->toJson();
    }
}
