<?php

namespace App\Http\Controllers\Admin;

use App\Enums\ClassificationGassesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\IsoStandard\CreateClassificationRequest;
use App\Http\Requests\Admin\IsoStandard\DeleteClassificationRequest;
use App\Http\Requests\Admin\IsoStandard\GetClassificationGasRequest;
use App\Http\Requests\Admin\IsoStandard\GetGroupValueRequest;
use App\Http\Requests\Admin\IsoStandard\GetClassificationRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\FormGroupValue;
use App\Models\IsoStandardClassification;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class IsoStandardController extends Controller
{
    public function getGroupValues(GetGroupValueRequest $request): JsonResponse
    {
        $groupValues = FormGroupValue::with('languages')
            ->whereHas('languages', fn($q) => $q->where('name', 'LIKE', "%$request->search%"))
            ->get();
        FormGroupValue::addLangProperties($groupValues);
        return (new GeneralResponse())->setData($groupValues->toArray())->toJson();
    }

    public function getClassifications(GetClassificationRequest $request): JsonResponse
    {
        $classifications = IsoStandardClassification::with('groupValue.languages')->get();
        foreach ($classifications as $c) {
            $gasses = json_decode($c->gasses);
            unset($c->gasses);
            $c->gasses = $gasses;
        }
        FormGroupValue::addLangProperties($classifications, 'groupValue');
        $classifications->makeHidden('group_value_id');
        return (new GeneralResponse())->setData($classifications->toArray())->toJson();
    }

    public function getClassificationsGasses(GetClassificationGasRequest $request): JsonResponse
    {
        $gasses = ClassificationGassesEnum::values();
        return (new GeneralResponse())->setData($gasses)->toJson();
    }

    public function createClassifications(CreateClassificationRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            foreach ($request->groupValueIds as $groupValueId) {
                $classification = IsoStandardClassification::query()
                    ->where('group_value_id', $groupValueId)
                    ->first();
                if (!$classification) {
                    $classification = new IsoStandardClassification();
                    $classification->group_value_id = $groupValueId;
                }
                $classification->gasses = json_encode(array_values(array_unique($request->gasses)));
                $classification->updated_at = now();
                $classification->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function deleteClassifications(DeleteClassificationRequest $request): JsonResponse
    {
        IsoStandardClassification::query()
            ->findOrFail($request->id)
            ->delete();
        return (new GeneralResponse())->toJson();
    }
}
