<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Uncertainty\CreateUncertaintyRequest;
use App\Http\Requests\Admin\Uncertainty\DeleteUncertaintyRequest;
use App\Http\Requests\Admin\Uncertainty\GetUncertaintyFieldRequest;
use App\Http\Requests\Admin\Uncertainty\GetUncertaintyGroupRequest;
use App\Http\Requests\Admin\Uncertainty\GetUncertaintyRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\FormCommon;
use App\Models\FormGroup;
use App\Models\FormGroupValue;
use App\Models\FormInputToGroup;
use App\Models\FormOperation;
use App\Models\FormUncertainty;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Collection as DBCollection;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class UncertaintyController extends Controller
{
    public function getFields(GetUncertaintyFieldRequest $request): JsonResponse
    {
        $operations = FormOperation::query()->where('form_id', $request->formId)->get();
        $fields = collect();
        foreach ($operations as $op) {
            $fields->push(...(json_decode($op->operation)->uncertainties ?? []));
        }
        $fields = $fields->unique()->values()->toArray();
        return (new GeneralResponse())->setData($fields)->toJson();
    }

    public function getGroups(GetUncertaintyGroupRequest $request): JsonResponse
    {
        $groupIds = FormInputToGroup::query()
            ->whereNotNull('group_id')
            ->whereHas('input', fn($q) => $q->where('form_id', $request->formId))
            ->get()
            ->pluck('group_id')
            ->values();

        $commons = FormCommon::with('groups')->where('form_id', $request->formId)->get();
        $commonGroupIds = collect();
        foreach ($commons as $common) {
            $commonFormInputToGroups = FormInputToGroup::query()
                ->whereHas('input', fn($q) => $q->where('form_id', $common->common_form_id))
                ->get();
            $this->getCommonGroupIds($common, $commonFormInputToGroups, $commonGroupIds);
        }
        $groupIds->push(...$commonGroupIds);
        $groupIds = $groupIds->unique()->values();

        $groups = FormGroup::with('values.languages')->whereIn('id', $groupIds)->get();
        FormGroup::addLangProperties($groups, 'values');
        return (new GeneralResponse())->setData($groups->toArray())->toJson();
    }

    private function getCommonGroupIds(FormCommon $common, DBCollection $formInputToGroups, Collection &$groupIdsCollection, int $groupValueId = null): void
    {
        if (!$groupValueId) {
            $groupValueId = $common->groups->whereNotNull('common_group_value_id')->first()->common_group_value_id;
        }
        $groupIds = $formInputToGroups
            ->where('related_group_value_id', $groupValueId)
            ->pluck('group_id')
            ->unique()
            ->values();
        $groupIds = $groupIds->filter(fn($groupId) => !$groupIdsCollection->contains($groupId));
        if ($groupIds->count() == 0) {
            return;
        }
        $groupIdsCollection->push(...$groupIds);
        $groupValues = FormGroupValue::query()->whereIn('group_id', $groupIds)->get();
        foreach ($groupValues as $groupValue) {
            $this->getCommonGroupIds($common, $formInputToGroups, $groupIdsCollection, $groupValue->id);
        }
    }

    public function getUncertainties(GetUncertaintyRequest $request): JsonResponse
    {
        $uncertainties = FormUncertainty::query()
            ->where('form_id', $request->formId)
            ->whereNotNull('uncertainty')
            ->get();
        FormUncertainty::mapUncertainties($uncertainties);
        return (new GeneralResponse())->setData($uncertainties)->toJson();
    }

    public function createUncertainty(CreateUncertaintyRequest $request): JsonResponse
    {
        $uncertainty = FormUncertainty::query()
            ->where('form_id', $request->formId)
            ->where('group_value_id', $request->groupValueId)
            ->first();
        if (!$uncertainty) {
            $uncertainty = new FormUncertainty();
        }
        $uncertainty->form_id = $request->formId;
        $uncertainty->group_value_id = $request->groupValueId;
        $uncertainty->uncertainty = $request->uncertainty;
        $uncertainty->save();
        return (new GeneralResponse())->toJson();
    }

    public function deleteUncertainty(DeleteUncertaintyRequest $request): JsonResponse
    {
        FormUncertainty::query()->findOrFail($request->id)->delete();
        return (new GeneralResponse())->toJson();
    }
}
