<?php

namespace App\Http\Controllers\Admin;

use App\Enums\FormInputTypeEnum;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Export\ExcelExportRequest;
use App\Http\Requests\Admin\Export\ExcelExportEsgRequest;
use App\Models\CompanySector;
use App\Models\Form;
use App\Models\FormGroupValue;
use App\Models\Report;
use App\Models\TubitakApplication;
use App\Models\User;
use App\Models\UserPending;
use App\Utils\GeneralUtil;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\Response;

class ExportController extends Controller
{
    public function excelExport(ExcelExportRequest $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return $this->generateExcel(true, 'greentim-data.xlsx');
    }

    public function excelExportForEsg(ExcelExportEsgRequest $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return $this->generateExcel(false, 't4earth-data.xlsx');
    }

    private function generateExcel(bool $usePrivilegeCheck, string $filename): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $spreadsheet = new Spreadsheet();

//        $this->getLoginUsers($spreadsheet, $usePrivilegeCheck);
//        $this->getReportUsers($spreadsheet, $usePrivilegeCheck);
//        $this->getReportCategoryUsers($spreadsheet, $usePrivilegeCheck);
//        $this->getIsoStandardUsers($spreadsheet, $usePrivilegeCheck);
//        $this->getPendingUsers($spreadsheet);
//        $this->getArtificialUsers($spreadsheet);
//        $this->getTubitakApplications($spreadsheet);
//        $this->getSectorReports($spreadsheet);
//        $this->getFormGroupValues($spreadsheet);

        // TODO
        try {
            $this->getFormGroupValueMetrics($spreadsheet);
        } catch (\Exception $e) {
        }

        $spreadsheet->setActiveSheetIndex(0);
        $writer = new Xlsx($spreadsheet);
        $temp_file = tempnam(sys_get_temp_dir(), $filename);
        $writer->save($temp_file);

        return Response::download($temp_file, $filename)->deleteFileAfterSend();
    }

    private function getLoginUsers(Spreadsheet $spreadsheet, bool $usePrivilegeCheck = true): void
    {
        $query = User::with('company.sector', 'company.personnel')
            ->whereNotNull('last_activity_at');

        if ($usePrivilegeCheck) {
            $query->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::COMPANY));
        } else {
            $query->whereNotNull('company_id');
        }

        $users = $query->get();

        $sheet = $spreadsheet->setActiveSheetIndex(0);
        $sheet->setTitle('Giriş Yapan Kullanıcılar');

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Ad Soyad');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Telefon');
        $sheet->setCellValue('E1', 'Ünvan');
        $sheet->setCellValue('F1', 'Şirket');
        $sheet->setCellValue('G1', 'Sektör');
        $sheet->setCellValue('H1', 'Personel Sayısı');
        $sheet->setCellValue('I1', 'Son Aktiflik Tarihi');
        $sheet->setCellValue('J1', 'Kayıt Tarihi');

        $row = 2;
        foreach ($users as $user) {
            $sheet->setCellValue('A' . $row, $user->id);
            $sheet->setCellValue('B' . $row, $user->full_name);
            $sheet->setCellValue('C' . $row, $user->email);
            $sheet->setCellValue('D' . $row, $user->phone);
            $sheet->setCellValue('E' . $row, $user->title);
            $sheet->setCellValue('F' . $row, $user->company->name);
            $sheet->setCellValue('G' . $row, $user->company->sector->name);
            $sheet->setCellValue('H' . $row, $user->company?->personnel?->number_range);
            $sheet->setCellValue('I' . $row, GeneralUtil::formatDate($user->last_activity_at));
            $sheet->setCellValue('J' . $row, GeneralUtil::formatDate($user->created_at));
            $row++;
        }
    }

    private function getReportUsers(Spreadsheet $spreadsheet, bool $usePrivilegeCheck = true): void
    {
        $query = User::with('company.sector', 'company.personnel')
            ->has('reports');

        if ($usePrivilegeCheck) {
            $query->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::COMPANY));
        } else {
            $query->whereNotNull('company_id');
        }

        $users = $query->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Hesaplama Kaydeden Kullanıcılar');

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Ad Soyad');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Telefon');
        $sheet->setCellValue('E1', 'Ünvan');
        $sheet->setCellValue('F1', 'Şirket');
        $sheet->setCellValue('G1', 'Sektör');
        $sheet->setCellValue('H1', 'Personel Sayısı');
        $sheet->setCellValue('I1', 'Son Aktiflik Tarihi');
        $sheet->setCellValue('J1', 'Kayıt Tarihi');

        $row = 2;
        foreach ($users as $user) {
            $sheet->setCellValue('A' . $row, $user->id);
            $sheet->setCellValue('B' . $row, $user->full_name);
            $sheet->setCellValue('C' . $row, $user->email);
            $sheet->setCellValue('D' . $row, $user->phone);
            $sheet->setCellValue('E' . $row, $user->title);
            $sheet->setCellValue('F' . $row, $user->company->name);
            $sheet->setCellValue('G' . $row, $user->company->sector->name);
            $sheet->setCellValue('H' . $row, $user->company?->personnel?->number_range);
            $sheet->setCellValue('I' . $row, GeneralUtil::formatDate($user->last_activity_at));
            $sheet->setCellValue('J' . $row, GeneralUtil::formatDate($user->created_at));
            $row++;
        }
    }

    private function getReportCategoryUsers(Spreadsheet $spreadsheet, bool $usePrivilegeCheck = true): void
    {
        $query = User::with('company.sector', 'company.personnel')
            ->has('reports');

        if ($usePrivilegeCheck) {
            $query->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::COMPANY));
        } else {
            $query->whereNotNull('company_id');
        }

        $users = $query->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Hesaplanan Kategoriler');

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Ad Soyad');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Şirket');
        $sheet->setCellValue('E1', 'Sektör');
        $sheet->setCellValue('F1', 'Personel Sayısı');
        $sheet->setCellValue('G1', 'Rapor Kategorisi');
        $sheet->setCellValue('H1', 'Rapor Yılı');
        $sheet->setCellValue('I1', 'Rapor Sayısı');
        $sheet->setCellValue('J1', 'Rapor Son Oluşturma Tarihi');
        $sheet->setCellValue('K1', 'Sera Gazı Emisyonu');

        $row = 2;
        foreach ($users as $user) {
            $userReports = Report::with('form.category', 'form.commons.groups', 'result.values')
                ->where('user_id', $user->id)
                ->get();
            Report::mapCommonInputs($userReports);
            Report::mapResults($userReports);
            $userReports->groupBy('form_id')->each(function ($formReports, $form_id) use ($sheet, &$row, $user) {
                $formReports->groupBy('year')->each(function ($yearReports, $year) use ($sheet, &$row, $user) {
                    $form = $yearReports->first()->form;
                    $totalGas = $yearReports->sum(fn($r) => $r->result->greenhouse_gas_double);

                    $sheet->setCellValue('A' . $row, $user->id);
                    $sheet->setCellValue('B' . $row, $user->full_name);
                    $sheet->setCellValue('C' . $row, $user->email);
                    $sheet->setCellValue('D' . $row, $user->company->name);
                    $sheet->setCellValue('E' . $row, $user->company->sector->name);
                    $sheet->setCellValue('F' . $row, $user->company?->personnel?->number_range);
                    $sheet->setCellValueExplicit('G' . $row, $form->category->order . '.' . $form->order, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
                    $sheet->setCellValue('H' . $row, $year);
                    $sheet->setCellValue('I' . $row, $yearReports->count());
                    $sheet->setCellValue('J' . $row, GeneralUtil::formatDate($yearReports->max('created_at')));
                    $sheet->setCellValue('K' . $row, GeneralUtil::numberFormat($totalGas));
                    $row++;
                });
            });
        }
    }

    private function getIsoStandardUsers(Spreadsheet $spreadsheet, bool $usePrivilegeCheck = true): void
    {
        $query = User::with('company.sector', 'company.personnel')
            ->has('isoStandards');

        if ($usePrivilegeCheck) {
            $query->whereHas('privilege', fn($q) => $q->where('slug', RolePrivilegeEnum::COMPANY));
        } else {
            $query->whereNotNull('company_id');
        }

        $users = $query->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('ISO Raporu Giren Kullanıcılar');

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Ad Soyad');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Telefon');
        $sheet->setCellValue('E1', 'Ünvan');
        $sheet->setCellValue('F1', 'Şirket');
        $sheet->setCellValue('G1', 'Sektör');
        $sheet->setCellValue('H1', 'Personel Sayısı');
        $sheet->setCellValue('I1', 'ISO Rapor Sayısı');
        $sheet->setCellValue('J1', 'PDF İndirme Sayısı');
        $sheet->setCellValue('K1', 'Son ISO Raporu Kayıt Tarihi');

        $row = 2;
        foreach ($users as $user) {
            $sheet->setCellValue('A' . $row, $user->id);
            $sheet->setCellValue('B' . $row, $user->full_name);
            $sheet->setCellValue('C' . $row, $user->email);
            $sheet->setCellValue('D' . $row, $user->phone);
            $sheet->setCellValue('E' . $row, $user->title);
            $sheet->setCellValue('F' . $row, $user->company->name);
            $sheet->setCellValue('G' . $row, $user->company->sector->name);
            $sheet->setCellValue('H' . $row, $user->company?->personnel?->number_range);
            $sheet->setCellValue('I' . $row, $user->isoStandards->count());
            $sheet->setCellValue('J' . $row, $user->isoStandards->where('pdf_created', '=', true)->count());
            $sheet->setCellValue('K' . $row, GeneralUtil::formatDate($user->isoStandards->max('created_at')));
            $row++;
        }
    }

    private function getPendingUsers(Spreadsheet $spreadsheet): void
    {
        $userPendings = UserPending::with('company.sector', 'company.personnel')->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Sisteme Başvuran Kullanıcılar');

        $sheet->setCellValue('A1', 'Ad Soyad');
        $sheet->setCellValue('B1', 'Email');
        $sheet->setCellValue('C1', 'Telefon');
        $sheet->setCellValue('D1', 'Ünvan');
        $sheet->setCellValue('E1', 'Rol');
        $sheet->setCellValue('F1', 'Şirket');
        $sheet->setCellValue('G1', 'Sektör');
        $sheet->setCellValue('H1', 'Personel Sayısı');
        $sheet->setCellValue('I1', 'Başvuru Tarihi');

        $row = 2;
        foreach ($userPendings as $pending) {
            $sheet->setCellValue('A' . $row, $pending->full_name);
            $sheet->setCellValue('B' . $row, $pending->email);
            $sheet->setCellValue('C' . $row, $pending->phone);
            $sheet->setCellValue('D' . $row, $pending->title);
            $sheet->setCellValue('E' . $row, $pending->is_manager ? 'Yönetici' : 'Çalışan');
            $sheet->setCellValue('F' . $row, $pending->company->name);
            $sheet->setCellValue('G' . $row, $pending->company->sector->name);
            $sheet->setCellValue('H' . $row, $pending->company?->personnel?->number_range);
            $sheet->setCellValue('I' . $row, GeneralUtil::formatDate($pending->created_at));
            $row++;
        }
    }

    private function getArtificialUsers(Spreadsheet $spreadsheet): void
    {
        $users = User::with('company', 'artificials')
            ->has('artificials')
            ->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Yapay Zeka Soruları');

        $sheet->setCellValue('A1', 'ID');
        $sheet->setCellValue('B1', 'Ad Soyad');
        $sheet->setCellValue('C1', 'Email');
        $sheet->setCellValue('D1', 'Şirket');
        $sheet->setCellValue('E1', 'Soru');
        $sheet->setCellValue('F1', 'Cevap');
        $sheet->setCellValue('G1', 'Beğenme Durumu');
        $sheet->setCellValue('H1', 'Soru Tarihi');

        $row = 2;
        foreach ($users as $user) {
            foreach ($user->artificials as $artificial) {
                $likeText = '';
                if ($artificial->is_like !== null) {
                    $likeText = $artificial->is_like ? 'Like' : 'Unlike';
                }
                $sheet->setCellValue('A' . $row, $user->id);
                $sheet->setCellValue('B' . $row, $user->full_name);
                $sheet->setCellValue('C' . $row, $user->email);
                $sheet->setCellValue('D' . $row, $user->company->name);
                $sheet->setCellValue('E' . $row, $artificial->question);
                $sheet->setCellValue('F' . $row, $artificial->answer);
                $sheet->setCellValue('G' . $row, $likeText);
                $sheet->setCellValue('H' . $row, GeneralUtil::formatDate($artificial->created_at));
                $row++;
            }
        }
    }

    private function getTubitakApplications(Spreadsheet $spreadsheet): void
    {
        $applications = TubitakApplication::with('sector')->get();

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('TUBİTAK Başvuruları');

        $sheet->setCellValue('A1', 'Şirket');
        $sheet->setCellValue('B1', 'Vergi No');
        $sheet->setCellValue('C1', 'İl');
        $sheet->setCellValue('D1', 'İlçe');
        $sheet->setCellValue('E1', 'Ciro');
        $sheet->setCellValue('F1', 'Çalışan Sayısı');
        $sheet->setCellValue('G1', 'Sektör');
        $sheet->setCellValue('H1', 'Prodis Kaydı');
        $sheet->setCellValue('I1', 'Yönetici Ad Soyad');
        $sheet->setCellValue('J1', 'Yönetici Email');
        $sheet->setCellValue('K1', 'Yönetici Telefon');
        $sheet->setCellValue('L1', 'Yönetici Ünvanı');
        $sheet->setCellValue('M1', 'İletişim Ad Soyad');
        $sheet->setCellValue('N1', 'İletişim Email');
        $sheet->setCellValue('O1', 'İletişim Telefon');
        $sheet->setCellValue('P1', 'İletişim Ünvanı');
        $sheet->setCellValue('Q1', 'Başvuru Tarihi');

        $row = 2;
        foreach ($applications as $application) {
            $sheet->setCellValue('A' . $row, $application->company_name);
            $sheet->setCellValue('B' . $row, $application->tax_number);
            $sheet->setCellValue('C' . $row, $application->city);
            $sheet->setCellValue('D' . $row, $application->state);
            $sheet->setCellValue('E' . $row, $application->turnover);
            $sheet->setCellValue('F' . $row, $application->personnel_number);
            $sheet->setCellValue('G' . $row, $application->sector->name);
            $sheet->setCellValue('H' . $row, $application->is_prodis ? 'Evet' : 'Hayır');
            $sheet->setCellValue('I' . $row, $application->manager_name . ' ' . $application->manager_surname);
            $sheet->setCellValue('J' . $row, $application->manager_email);
            $sheet->setCellValue('K' . $row, $application->manager_phone);
            $sheet->setCellValue('L' . $row, $application->manager_title);
            $sheet->setCellValue('M' . $row, $application->contact_name . ' ' . $application->contact_surname);
            $sheet->setCellValue('N' . $row, $application->contact_email);
            $sheet->setCellValue('O' . $row, $application->contact_phone);
            $sheet->setCellValue('P' . $row, $application->contact_title);
            $sheet->setCellValue('Q' . $row, GeneralUtil::formatDate($application->created_at));
            $row++;
        }
    }

    private function getSectorReports(Spreadsheet $spreadsheet): void
    {
        $sectors = CompanySector::with('companies')->orderBy('order')->get();
        $forms = Form::with('category')
            ->orderBy('order')
            ->get()
            ->sort(fn($f1, $f2) => $f1->category->order <=> $f2->category->order);

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Sektör Bazlı Hesaplamalar');

        $sheet->setCellValue('A1', 'Sektör');
        $sheet->setCellValue('B1', 'Kategori');
        $sheet->setCellValue('C1', 'Sera Gazı Emisyonu');
        $sheet->setCellValue('D1', 'Şirket Sayısı');

        $row = 2;
        foreach ($sectors as $s) {
            $companyIds = $s->companies->pluck('id')->toArray();
            $reports = Report::with('form.commons.groups', 'result.values')
                ->whereIn('company_id', $companyIds)->get();
            Report::mapCommonInputs($reports);
            Report::mapResults($reports);
            foreach ($forms as $f) {
                $formReports = $reports->where('form_id', $f->id);
                $totalGas = $formReports->sum(fn($r) => $r->result->greenhouse_gas_double);
                $formCompanyCount = $formReports->pluck('company_id')->unique()->count();

                $sheet->setCellValue('A' . $row, $s->name);
                $sheet->setCellValueExplicit('B' . $row, $f->category->order . '.' . $f->order, \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING);
                $sheet->setCellValue('C' . $row, GeneralUtil::numberFormat($totalGas));
                $sheet->setCellValue('D' . $row, $formCompanyCount);
                $row++;
            }
        }
    }

    private function getFormGroupValues(Spreadsheet $spreadsheet): void
    {
        $forms = Form::with('category', 'languages', 'inputs.groups.values.languages')
            ->orderBy('order')
            ->get()
            ->sort(fn($f1, $f2) => $f1->category->order <=> $f2->category->order);
        Form::addLangProperties($forms);

        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Kategori Aktivite Kaynakları');

        $row = 1;
        foreach ($forms as $form) {
            $sheet->setCellValue('A' . $row, $form->name);
            $row++;

            $values = $form->inputs->map(fn($i) => $i->groups->map(fn($g) => $g->values))->flatten(2);
            FormGroupValue::addLangProperties($values);
            $valueNames = $values->pluck('name')->unique()->values();

            foreach ($valueNames as $v) {
                $sheet->setCellValue('A' . $row, $v);
                $row++;
            }
            $row += 2;
        }
    }

    private function getFormGroupValueMetrics(Spreadsheet $spreadsheet): void
    {
        // TODO: remove this line
        ini_set('max_execution_time', 3600);

        $forms = Form::with('category', 'languages', 'inputs.groups.values.languages', 'commons.groups')
            ->orderBy('order')
            ->get()
            ->sort(fn($f1, $f2) => $f1->category->order <=> $f2->category->order);
        Form::addLangProperties($forms);

        $sheet = $spreadsheet->setActiveSheetIndex(0); // TODO
        //$sheet = $spreadsheet->createSheet();
        $sheet->setTitle('Kategori Metrikleri');

        $columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

        $row = 1;
        foreach ($forms as $form) {
            if ($form->inputs[0]->type_id != FormInputTypeEnum::DROPDOWN->value) {
                continue;
            }

            $sheet->setCellValue('A' . $row, $form->name);
            $row++;

            $formGroups = $form->inputs->map(fn($i) => $i->groups)->flatten(1);
            $values = $formGroups->map(fn($g) => $g->values)->flatten(1);
            FormGroupValue::addLangProperties($values);

            foreach ($form->inputs[0]->groups[0]->values as $value) {
                $allValueRows = $this->formGroupValueMetricsSetValues($sheet, $formGroups, $value, $columns, []);
                foreach ($allValueRows as $rowValues) {
                    $i = 0;
                    foreach ($rowValues as $cellValue) {
                        $sheet->setCellValue($columns[$i] . $row, $cellValue);
                        $i++;
                    }
                    $row++;
                }
            }

            $row += 2;
        }
    }

    private function formGroupValueMetricsSetValues(
        Worksheet      $sheet,
        Collection     $formGroups,
        FormGroupValue $value,
        array          $columns,
        array          $rowValues = [],
    ): array
    {
        $allRowValues = [];
        $relatedValues = $formGroups->filter(fn($g) => $g->related_group_value_id == $value->id)->map(fn($g) => $g->values)->flatten(1);

        if ($relatedValues->isEmpty()) {
            // Base case: no more related values, return current row
            if (count($rowValues) > 0) {
                return [$rowValues];
            } else {
                return [[$value->name]];
            }
        }

        foreach ($relatedValues as $relatedValue) {
            // Create new row values array for this branch
            $currentRowValues = [];
            if (count($rowValues) > 0) {
                $currentRowValues = array_merge($rowValues, [$relatedValue->name]);
            } else {
                $currentRowValues = [$value->name, $relatedValue->name];
            }

            // Recursively get all combinations from this branch
            $subRowValues = $this->formGroupValueMetricsSetValues($sheet, $formGroups, $relatedValue, $columns, $currentRowValues);

            // Add all sub-combinations to our result
            $allRowValues = array_merge($allRowValues, $subRowValues);
        }

        return $allRowValues;
    }
}
