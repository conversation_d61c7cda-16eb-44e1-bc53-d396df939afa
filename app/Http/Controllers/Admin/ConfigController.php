<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Config\CreateOrUpdateConfigRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Config;
use Illuminate\Http\JsonResponse;

class ConfigController extends Controller
{
    public function createOrUpdateConfigs(CreateOrUpdateConfigRequest $request): JsonResponse
    {
        $config = Config::query()->where('key', $request->key)->first();
        if (!$config) {
            $config = new Config();
            $config->key = $request->key;
        }
        $config->value = $request->value;
        $config->save();
        return (new GeneralResponse())->toJson();
    }
}
