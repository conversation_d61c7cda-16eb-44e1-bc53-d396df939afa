<?php

namespace App\Http\Controllers\Admin;

use App\Enums\LanguageEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Tip\CreateContentRequest;
use App\Http\Requests\Admin\Tip\GetContentRequest;
use App\Http\Requests\Admin\Tip\GetTipRequest;
use App\Http\Requests\Admin\Tip\UpdateContentRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Language;
use App\Models\Tip;
use App\Models\TipContent;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class TipController extends Controller
{
    public function getTips(GetTipRequest $request): JsonResponse
    {
        $tips = Tip::query()->get();
        return (new GeneralResponse())->setData($tips)->toJson();
    }

    public function getContents(GetContentRequest $request): JsonResponse
    {
        $contents = TipContent::with('tip', 'languages', 'allLanguages')->get();
        TipContent::addLangProperties($contents);
        return (new GeneralResponse())->setData($contents)->toJson();
    }

    public function createContents(CreateContentRequest $request): JsonResponse
    {
        $languages = [];
        DB::beginTransaction();
        try {
            $content = new TipContent();
            $content->front_slug = $request->frontSlug;
            $content->tip_id = $request->tipId;
            $content->save();
            foreach ($request->titles as $title) {
                $languages[] = Language::createLanguage($title['text'], LanguageEnum::from($title['language']), $content->id, TipContent::class, 'title');
            }
            foreach ($request->descriptions as $description) {
                $languages[] = Language::createLanguage($description['text'], LanguageEnum::from($description['language']), $content->id, TipContent::class, 'description');
            }
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateContents(UpdateContentRequest $request, int $contentId): JsonResponse
    {
        $languages = [];
        $content = TipContent::query()->findOrFail($contentId);
        DB::beginTransaction();
        try {
            $content->front_slug = $request->frontSlug;
            $content->tip_id = $request->tipId;
            $content->save();
            foreach ($request->titles as $title) {
                $languages[] = Language::createLanguage($title['text'], LanguageEnum::from($title['language']), $content->id, TipContent::class, 'title');
            }
            foreach ($request->descriptions as $description) {
                $languages[] = Language::createLanguage($description['text'], LanguageEnum::from($description['language']), $content->id, TipContent::class, 'description');
            }
            Language::query()
                ->where('related_id', $content->id)
                ->where('related_model', TipContent::class)
                ->delete();
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }
}
