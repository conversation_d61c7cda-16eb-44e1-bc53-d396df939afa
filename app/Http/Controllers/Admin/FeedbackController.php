<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Feedback\CompleteFeedbackRequest;
use App\Http\Requests\Admin\Feedback\GetFeedbackRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Feedback;
use Illuminate\Http\JsonResponse;

class FeedbackController extends Controller
{
    public function getFeedbacks(GetFeedbackRequest $request): JsonResponse
    {
        $feedbacks = Feedback::with(['user', 'languages'])->get();
        Feedback::addLangProperties($feedbacks);
        return (new GeneralResponse())->setData($feedbacks->toArray())->toJson();
    }

    public function completeFeedbacks(CompleteFeedbackRequest $request, int $feedbackId): JsonResponse
    {
        $feedback = Feedback::query()->findOrFail($feedbackId);
        $feedback->is_complete = $request->isComplete;
        $feedback->save();
        return (new GeneralResponse())->toJson();
    }
}
