<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\User\CreateUserAccessTokenRequest;
use App\Http\Requests\Admin\User\GetUserRequest;
use App\Http\Requests\Admin\User\SendCustomMailRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\User;
use App\Notifications\UserCustomMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class UserController extends Controller
{
    public function getUsers(GetUserRequest $request): JsonResponse
    {
        $users = User::with('company.sector', 'company.personnel')
            ->orderBy('created_at')
            ->get();
        $users->makeVisible(['created_at', 'updated_at']);
        return (new GeneralResponse())->setData($users->toArray())->toJson();
    }

    public function sendCustomMail(SendCustomMailRequest $request): JsonResponse
    {
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($request->file('excel')->path());
        $emails = [];
        foreach ($spreadsheet->getActiveSheet()->toArray() as $i => $row) {
            if ($i == 0) {
                continue;
            }
            if (!$row[0]) {
                break;
            }
            $emails[] = $row[0];
        }
        $users = User::with('company')
            ->has('company')
            ->whereIn('email', $emails)
            ->get();
        if ($users->count() != count($emails)) {
            throw new BadRequestHttpException('Emails invalid');
        }

        $job = DB::table('jobs')->orderByDesc('available_at')->first();
        $delay = $job ? Carbon::parse($job->available_at) : now();

        foreach ($users as $u) {
            $notify = (new UserCustomMail($u, $request->subject, $request->text))->delay($delay->addSeconds(3));
            Notification::route('mail', $u->email)->notify($notify);
        }
        return (new GeneralResponse())->toJson();
    }

    public function createUserAccessToken(CreateUserAccessTokenRequest $request): JsonResponse
    {
        $user = User::with('company')->where('email', $request->email)->firstOrFail();
        $token = $user->createToken(env('APP_NAME'))->accessToken;
        return (new GeneralResponse())->setData(['user' => $user, 'token' => $token])->toJson();
    }
}
