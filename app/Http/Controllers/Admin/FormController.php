<?php

namespace App\Http\Controllers\Admin;

use App\Enums\LanguageEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Form\CreateFormRequest;
use App\Http\Requests\Admin\Form\CreateGroupRequest;
use App\Http\Requests\Admin\Form\CreateInputRequest;
use App\Http\Requests\Admin\Form\DeleteFormResultRequest;
use App\Http\Requests\Admin\Form\DeleteGroupRequest;
use App\Http\Requests\Admin\Form\GetFormRequest;
use App\Http\Requests\Admin\Form\GetFormResultRequest;
use App\Http\Requests\Admin\Form\GetGroupRequest;
use App\Http\Requests\Admin\Form\GetInputRequest;
use App\Http\Requests\Admin\Form\UpdateFormRequest;
use App\Http\Requests\Admin\Form\UpdateFormResultRequest;
use App\Http\Requests\Admin\Form\UpdateGroupRequest;
use App\Http\Requests\Admin\Form\UpdateGroupValueRequest;
use App\Http\Requests\Admin\Form\UpdateInputRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Form;
use App\Models\FormGroup;
use App\Models\FormGroupValue;
use App\Models\FormInput;
use App\Models\FormInputToGroup;
use App\Models\FormResult;
use App\Models\FormResultValue;
use App\Models\Language;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class FormController extends Controller
{
    public function getForms(GetFormRequest $request): JsonResponse
    {
        $forms = Form::with('languages', 'category');
        if ($request->id) {
            $forms = $forms->with(['allLanguages', 'inputs.allLanguages', 'inputs.groups'])->where('id', $request->id);
        }
        $forms = $forms->orderBy('order')->get();
        Form::addLangProperties($forms);
        $forms = $forms
            ->sort(fn($f1, $f2) => $f1->category->order <=> $f2->category->order)
            ->values();
        $forms->makeHidden(['category']);
        return (new GeneralResponse())->setData($forms->toArray())->toJson();
    }

    public function createForms(CreateFormRequest $request): JsonResponse
    {
        $i = 1;
        $languages = [];
        $insertedInputIds = [];
        DB::beginTransaction();
        try {
            $form = new Form();
            $form->slug = $request->slug;
            $form->is_locked = $request->isLocked;
            $form->category_id = $request->categoryId;
            $form->save();
            foreach ($request->post('names') as $name) {
                $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $form->id, Form::class, 'name');
            }
            if ($request->post('infos')) {
                foreach ($request->post('infos') as $info) {
                    $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $form->id, Form::class, 'info');
                }
            }
            foreach ($request->post('inputs') as $input) {
                $formInput = new FormInput();
                $formInput->order = $i++;
                $formInput->form_id = $form->id;
                if (isset($input['relatedInputIndexes']) && is_iterable($input['relatedInputIndexes']) && count($input['relatedInputIndexes']) > 0) {
                    $relatedInputIds = [];
                    foreach ($input['relatedInputIndexes'] as $relatedInputIndex) {
                        $relatedInputIds[] = $insertedInputIds[$relatedInputIndex];
                    }
                    $formInput->related_input_id = implode(',', $relatedInputIds);
                }
                $formInput->save();
                $insertedInputIds[] = $formInput->id;
                foreach ($input['names'] as $name) {
                    $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $formInput->id, FormInput::class, 'name');
                }
                if (isset($input['infos']) && is_iterable($input['infos'])) {
                    foreach ($input['infos'] as $info) {
                        $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $formInput->id, FormInput::class, 'info');
                    }
                }
                if (isset($input['groups']) && is_iterable($input['groups'])) {
                    foreach ($input['groups'] as $group) {
                        $inputToGroup = new FormInputToGroup();
                        $inputToGroup->input_id = $formInput->id;
                        $inputToGroup->group_id = $group['groupId'];
                        if (isset($group['relatedGroupValueId'])) {
                            $inputToGroup->related_group_value_id = $group['relatedGroupValueId'];
                        }
                        $inputToGroup->save();
                    }
                }
            }
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateForms(UpdateFormRequest $request, int $formId): JsonResponse
    {
        $languages = [];
        $form = Form::query()->findOrFail($formId);

        DB::beginTransaction();
        try {
            $form->slug = $request->slug;
            $form->is_locked = $request->isLocked;
            $form->category_id = $request->categoryId;
            $form->save();
            if (isset($request->names) && is_iterable($request->names)) {
                Language::query()
                    ->where('related_model', '=', Form::class)
                    ->where('related_id', '=', $form->id)
                    ->where('related_property', '=', 'name')
                    ->delete();
                foreach ($request->names as $name) {
                    $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $form->id, Form::class, 'name');
                }
            }
            if (isset($request->infos) && is_iterable($request->infos)) {
                Language::query()
                    ->where('related_model', '=', Form::class)
                    ->where('related_id', '=', $form->id)
                    ->where('related_property', '=', 'info')
                    ->delete();
                foreach ($request->infos as $info) {
                    $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $form->id, Form::class, 'info');
                }
            }
            if (isset($request->descriptions) && is_iterable($request->descriptions)) {
                Language::query()
                    ->where('related_model', '=', Form::class)
                    ->where('related_id', '=', $form->id)
                    ->where('related_property', '=', 'description')
                    ->delete();
                foreach ($request->descriptions as $description) {
                    $languages[] = Language::createLanguage($description['text'], LanguageEnum::from($description['language']), $form->id, Form::class, 'description');
                }
            }
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function getFormResults(GetFormResultRequest $request): JsonResponse
    {
        $form = Form::with(['results.values.inputLanguages', 'results.values.groupValueLanguages'])
            ->where('id', $request->id)
            ->first();
        Form::mapResultValueLanguages($form);
        return (new GeneralResponse())->setData($form->toArray())->toJson();
    }

    public function updateFormResults(UpdateFormResultRequest $request, int $formId): JsonResponse
    {
        $formResultValues = [];
        $inputIdAndValueIds = [];
        $form = Form::with('inputs.groups.values')->findOrFail($formId);

        $resultExist = FormResult::with('values')->where('form_id', $formId);
        foreach ($request->values as $value) {
            $resultExist->whereHas('values', function ($q) use ($value) {
                $q->where('input_id', $value['inputId'])->where('group_value_id', $value['groupValueId']);
            });
        }
        $resultExist = $resultExist->first();
        if ($resultExist && count($resultExist->values) == count($request->values)) {
            $resultExist->carbon_dioxide = $request->carbonDioxide;
            $resultExist->nitrous_oxide = $request->nitrousOxide;
            $resultExist->methane = $request->methane;
            $resultExist->save();
            return (new GeneralResponse())->setMessages('Çarpan değerleri mevcutta olduğu için yeni sayısal değerler ile güncellendi.')->toJson();
        }

        DB::beginTransaction();
        try {
            $formResult = new FormResult();
            $formResult->form_id = $formId;
            $formResult->carbon_dioxide = $request->carbonDioxide;
            $formResult->nitrous_oxide = $request->nitrousOxide;
            $formResult->methane = $request->methane;
            $formResult->save();
            foreach ($request->values as $value) {
                $input = $form->inputs->find($value['inputId']);
                $groups = $input->groups;
                $relatedGroupValueIds = $groups->filter(fn($g) => $g->values->filter(fn($v) => $v->id == $value['groupValueId'])->count() > 0)->map(fn($g) => $g->related_group_value_id)->toArray();
                if (count($relatedGroupValueIds) == 0) {
                    throw new \Exception("value is not exist on input groups for inputId: $value[inputId], groupValueId: $value[groupValueId]", ResponseAlias::HTTP_BAD_REQUEST);
                }
                if ($input->related_input_id) {
                    $relatedInputControl = false;
                    foreach (explode(',', $input->related_input_id) as $relatedInputId) {
                        if (isset($inputIdAndValueIds[$relatedInputId]) && in_array($inputIdAndValueIds[$relatedInputId], $relatedGroupValueIds)) {
                            $relatedInputControl = true;
                            break;
                        }
                    }
                    if (!$relatedInputControl) {
                        throw new \Exception("related value is not exist on related input groups for inputId: $value[inputId]", ResponseAlias::HTTP_BAD_REQUEST);
                    }
                }
                $formResultValues[] = [
                    'input_id' => $value['inputId'],
                    'group_value_id' => $value['groupValueId'],
                    'result_id' => $formResult->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                $inputIdAndValueIds[$value['inputId']] = $value['groupValueId'];
            }
            FormResultValue::query()->insert($formResultValues);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            if ($e->getCode() == ResponseAlias::HTTP_BAD_REQUEST) {
                return (new GeneralResponse(false))->setMessages('Değerler form yapısına uygun değil.')->toJson();
            }
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->setMessages('Çarpan değerleri eklendi.')->toJson();
    }

    public function deleteFormResults(DeleteFormResultRequest $request, int $formResultId): JsonResponse
    {
        $formResult = FormResult::query()->findOrFail($formResultId);
        DB::beginTransaction();
        try {
            FormResultValue::query()->where('result_id', $formResult->id)->delete();
            $formResult->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function getInputs(GetInputRequest $request): JsonResponse
    {
        $inputs = FormInput::with('groups')->where('form_id', $request->form_id)->get();
        return (new GeneralResponse())->setData($inputs->toArray())->toJson();
    }

    public function createInputs(CreateInputRequest $request): JsonResponse
    {
        $inputToGroups = [];
        $languages = [];
        $lastFormInput = FormInput::query()->where('form_id', $request->formId)->orderByDesc('order')->first();
        DB::beginTransaction();
        try {
            $input = new FormInput();
            $input->order = $lastFormInput ? $lastFormInput->order + 1 : 1;
            $input->form_id = $request->formId;
            $input->related_input_id = $request->relatedInputId;
            $input->save();
            foreach ($request->names as $name) {
                $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $input->id, FormInput::class, 'name');
            }
            if (isset($request->infos) && is_iterable($request->infos)) {
                foreach ($request->infos as $info) {
                    $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $input->id, FormInput::class, 'info');
                }
            }
            $i = 0;
            foreach ($request->groups as $group) {
                $inputToGroups[$i] = [
                    'input_id' => $input->id,
                    'group_id' => $group['groupId'],
                ];
                if (isset($group['relatedGroupValueId'])) {
                    $inputToGroups[$i]['related_group_value_id'] = $group['relatedGroupValueId'];
                }
                $i++;
            }
            FormInputToGroup::query()->create($inputToGroups);
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateInputs(UpdateInputRequest $request, int $inputId): JsonResponse
    {
        $languages = [];
        DB::beginTransaction();
        try {
            foreach ($request->names as $name) {
                $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $inputId, FormInput::class, 'name');
            }
            if (isset($request->infos)) {
                foreach ($request->infos as $info) {
                    $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $inputId, FormInput::class, 'info');
                }
            }
            if (isset($request->amountNames)) {
                foreach ($request->amountNames as $amountName) {
                    $languages[] = Language::createLanguage($amountName['text'], LanguageEnum::from($amountName['language']), $inputId, FormInput::class, 'amount_name');
                }
            }
            Language::query()
                ->where('related_id', $inputId)
                ->where('related_model', FormInput::class)
                ->delete();
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function getGroups(GetGroupRequest $request): JsonResponse
    {
        $groups = FormGroup::with('values.allLanguages');
        if ($request->id) {
            $groups = $groups->where('id', $request->id);
        }
        if ($request->name) {
            $groups = $groups->whereHas('values.allLanguages', function ($query) use ($request) {
                $query->whereRaw("LOWER(name) LIKE ?", ["%" . strtolower($request->name) . "%"]);
            });
        }
        return (new GeneralResponse())->setData($groups->get()->toArray())->toJson();
    }

    public function createGroups(CreateGroupRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $languages = [];
            $group = new FormGroup();
            $group->name = $request->name;
            $group->save();
            foreach ($request->post('values') as $v) {
                $value = new FormGroupValue();
                $value->group_id = $group->id;
                $value->save();
                foreach ($v['names'] as $name) {
                    $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $value->id, FormGroupValue::class, 'name');
                }
                if (isset($v['infos'])) {
                    foreach ($v['infos'] as $info) {
                        $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $value->id, FormGroupValue::class, 'info');
                    }
                }
            }
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateGroups(UpdateGroupRequest $request, int $groupId): JsonResponse
    {
        $languages = [];
        $updatedValueIds = [];
        DB::beginTransaction();
        try {
            $group = FormGroup::query()->findOrFail($groupId);
            $group->name = $request->name;
            $group->save();
            $values = FormGroupValue::query()->where('group_id', $groupId)->get();
            foreach ($request->post('values') as $v) {
                if (isset($v['id'])) {
                    $valueId = $v['id'];
                    $updatedValueIds[] = $valueId;
                } else {
                    $value = new FormGroupValue();
                    $value->group_id = $group->id;
                    $value->save();
                    $valueId = $value->id;
                }
                foreach ($v['names'] as $name) {
                    $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $valueId, FormGroupValue::class, 'name');
                }
                if (isset($v['infos'])) {
                    foreach ($v['infos'] as $info) {
                        $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $valueId, FormGroupValue::class, 'info');
                    }
                }
            }
            FormGroupValue::query()
                ->whereIn('id', $values->pluck('id'))
                ->whereNotIn('id', $updatedValueIds)
                ->delete();
            Language::query()
                ->whereIn('related_id', $values->pluck('id'))
                ->where('related_model', FormGroupValue::class)
                ->delete();
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function deleteGroups(DeleteGroupRequest $request, int $groupId): JsonResponse
    {
        DB::beginTransaction();
        try {
            $group = FormGroup::query()->findOrFail($groupId);
            $values = FormGroupValue::query()->where('group_id', $groupId)->get();
            /*
            FormInputToGroup::query()
                ->where('group_id', $groupId)
                ->orWhereIn('related_group_value_id', $values->pluck('id'))
                ->delete();
            */
            Language::query()
                ->whereIn('related_id', $values->pluck('id'))
                ->where('related_model', FormGroupValue::class)
                ->delete();
            FormGroupValue::query()->where('group_id', $groupId)->delete();
            $group->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            if ($e instanceof ModelNotFoundException) {
                throw new NotFoundHttpException();
            }
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function updateGroupValues(UpdateGroupValueRequest $request, int $groupValueId): JsonResponse
    {
        $languages = [];
        DB::beginTransaction();
        try {
            foreach ($request->names as $name) {
                $languages[] = Language::createLanguage($name['text'], LanguageEnum::from($name['language']), $groupValueId, FormGroupValue::class, 'name');
            }
            if (isset($request->infos)) {
                foreach ($request->infos as $info) {
                    $languages[] = Language::createLanguage($info['text'], LanguageEnum::from($info['language']), $groupValueId, FormGroupValue::class, 'info');
                }
            }
            Language::query()
                ->where('related_id', $groupValueId)
                ->where('related_model', FormGroupValue::class)
                ->delete();
            Language::query()->insert($languages);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }
}
