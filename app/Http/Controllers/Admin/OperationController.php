<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Operation\CreateMultiplierRequest;
use App\Http\Requests\Admin\Operation\DeleteMultiplierRequest;
use App\Http\Requests\Admin\Operation\GetOperationFieldRequest;
use App\Http\Requests\Admin\Operation\GetOperationGroupRequest;
use App\Http\Requests\Admin\Operation\GetMultiplierByValueRequest;
use App\Http\Requests\Admin\Operation\GetMultiplierRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\FormGroup;
use App\Models\FormInputToGroup;
use App\Models\FormMultiplier;
use App\Models\FormOperation;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class OperationController extends Controller
{
    public function getFields(GetOperationFieldRequest $request): JsonResponse
    {
        $operations = FormOperation::query()->where('form_id', $request->formId)->get();
        $fields = collect();
        foreach ($operations as $op) {
            $fields->push(...(json_decode($op->operation)->fields ?? []));
        }
        return (new GeneralResponse())->setData($fields->unique()->values()->toArray())->toJson();
    }

    public function getGroups(GetOperationGroupRequest $request): JsonResponse
    {
        $groupIds = FormInputToGroup::query()
            ->whereNotNull('group_id')
            ->whereHas('input', fn($q) => $q->where('form_id', $request->formId))
            ->get()->pluck('group_id')->unique();
        $groups = FormGroup::with('values.languages')->whereIn('id', $groupIds)->get();
        FormGroup::addLangProperties($groups, 'values');
        return (new GeneralResponse())->setData($groups->toArray())->toJson();
    }

    public function getMultipliers(GetMultiplierRequest $request): JsonResponse
    {
        $multipliers = FormMultiplier::query()->where('form_id', $request->formId)->get();
        FormMultiplier::mapMultipliers($multipliers);
        return (new GeneralResponse())->setData($multipliers->toArray())->toJson();
    }

    public function getMultiplierByValues(GetMultiplierByValueRequest $request): JsonResponse
    {
        $groupValueIds = collect(explode(',', $request->groupValueIds))->map(fn($id) => +$id)->values();
        $formMul = FormMultiplier::query()
            ->where('form_id', $request->formId)
            ->where(fn($q) => $groupValueIds->map(fn($vId) => $q->whereJsonContains('group_value_ids', $vId)))
            ->get()
            ->first(fn($m) => count(json_decode($m->group_value_ids)) == $groupValueIds->count());
        if (!$formMul) {
            throw new BadRequestHttpException('Çarpan bulunamadı');
        }
        FormMultiplier::mapMultipliers([$formMul]);
        return (new GeneralResponse())->setData($formMul->toArray())->toJson();
    }

    public function createMultipliers(CreateMultiplierRequest $request): JsonResponse
    {
        $groupValueIds = collect($request->groupValueIds);
        $formMul = FormMultiplier::query()
            ->where('form_id', $request->formId)
            ->where(fn($q) => $groupValueIds->map(fn($vId) => $q->whereJsonContains('group_value_ids', $vId)))
            ->get()
            ->first(fn($m) => count(json_decode($m->group_value_ids)) == $groupValueIds->count());
        if (!$formMul) {
            $formMul = new FormMultiplier();
        }
        $formMul->form_id = $request->formId;
        $formMul->is_unit = $request->isUnit;
        $formMul->group_value_ids = json_encode($request->groupValueIds);
        $formMul->multiplier = json_encode($request->multiplier);
        $formMul->save();
        return (new GeneralResponse())->toJson();
    }

    public function deleteMultipliers(DeleteMultiplierRequest $request, int $multiplierId): JsonResponse
    {
        FormMultiplier::query()
            ->findOrFail($multiplierId)
            ->delete();
        return (new GeneralResponse())->toJson();
    }
}
