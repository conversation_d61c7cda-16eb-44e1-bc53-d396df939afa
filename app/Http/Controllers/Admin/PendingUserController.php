<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\RoleEnum;
use App\Enums\UserEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PendingUser\ApprovePendingUserRequest;
use App\Http\Requests\Admin\PendingUser\DenyPendingUserRequest;
use App\Http\Requests\Admin\PendingUser\GetPendingUserRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Company;
use App\Models\Role;
use App\Models\RolePrivilege;
use App\Models\User;
use App\Models\UserPending;
use App\Notifications\UserPendingApprovedMail;
use App\Notifications\UserPendingDenyMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class PendingUserController extends Controller
{
    public function getPendingUsers(GetPendingUserRequest $request): JsonResponse
    {
        $pendings = UserPending::with('company')->get();
        return (new GeneralResponse())->setData($pendings->toArray())->toJson();
    }

    public function approvePendingUsers(ApprovePendingUserRequest $request): JsonResponse
    {
        $pendings = UserPending::query()
            ->where('company_id', $request->companyId)
            ->get();
        $roleId = Role::query()
            ->where('slug', '=', RoleEnum::MANAGER)
            ->firstOrFail()
            ->id;
        $privilegeId = RolePrivilege::query()
            ->where('slug', '=', RolePrivilegeEnum::COMPANY)
            ->firstOrFail()
            ->id;
        DB::beginTransaction();
        try {
            foreach ($pendings as $p) {
                $password = Str::random(rand(8, 12));
                $user = new User();
                $user->full_name = $p->full_name;
                $user->email = $p->email;
                $user->phone = $p->phone;
                $user->title = $p->title;
                $user->is_approved_password = false;
                $user->password = Hash::make($password);
                $user->company_id = $p->company_id;
                $user->type_id = UserEnum::FIRM;
                $user->role_id = $roleId;
                $user->privilege_id = $privilegeId;
                $user->privacy_policy = true;
                $user->email_verified_at = now();
                $user->save();

                Notification::route('mail', $user->email)->notify(new UserPendingApprovedMail($user, $password));
                $p->delete();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }

    public function denyPendingUsers(DenyPendingUserRequest $request): JsonResponse
    {
        $pendings = UserPending::query()->where('company_id', $request->companyId)->get();
        foreach ($pendings as $p) {
            Notification::route('mail', $p->email)->notify(new UserPendingDenyMail($p));
            $p->delete();
        }
        Company::query()->find($request->companyId)->delete();
        return (new GeneralResponse())->toJson();
    }
}
