<?php

namespace App\Http\Controllers;

use App\Enums\FeedbackSubjectEnum;
use App\Http\Requests\Feedback\CreateFeedbackRequest;
use App\Http\Requests\Feedback\GetSubjectRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Feedback;
use App\Notifications\AdminFeedbackMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Notification;

class FeedbackController extends Controller
{
    public function getSubjects(GetSubjectRequest $request): JsonResponse
    {
        $subjects = FeedbackSubjectEnum::getSubjects();
        return (new GeneralResponse())->setData($subjects)->toJson();
    }

    public function createFeedbacks(CreateFeedbackRequest $request): JsonResponse
    {
        $feedback = new Feedback();
        $feedback->user_id = auth()->id();
        $feedback->subject_enum_id = $request->subjectEnumId;
        $feedback->message = $request->message;
        $feedback->save();

        Notification::route('mail', env('FEEDBACK_ADMIN_EMAIL'))->notify(new AdminFeedbackMail($feedback->id));
        return (new GeneralResponse())->toJson();
    }
}
