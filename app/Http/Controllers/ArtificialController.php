<?php

namespace App\Http\Controllers;

use App\Http\Requests\Artificial\CreateQuestionRequest;
use App\Http\Requests\Artificial\UpdateQuestionRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Artificial;
use Illuminate\Http\JsonResponse;

class ArtificialController extends Controller
{
    public function createQuestion(CreateQuestionRequest $request): JsonResponse
    {
        $artificial = new Artificial();
        $artificial->user_id = auth()->id();
        $artificial->question = $request->question;
        $artificial->answer = $request->answer;
        $artificial->save();

        return (new GeneralResponse())->setData($artificial->toArray())->toJson();
    }

    public function updateQuestion(UpdateQuestionRequest $request): JsonResponse
    {
        $artificial = Artificial::query()
            ->where('user_id', auth()->id())
            ->findOrFail($request->id);
        $artificial->is_like = $request->isLike === null ? null : $request->isLike;
        $artificial->save();

        return (new GeneralResponse())->toJson();
    }
}
