<?php

namespace App\Http\Controllers;

use App\Http\Requests\Tubitak\CreateApplicationRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\TubitakApplication;
use App\Notifications\TubitakInformationMail;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Notification;

class TubitakController extends Controller
{
    public function createApplications(CreateApplicationRequest $request): JsonResponse
    {
        $application = new TubitakApplication();
        $application->company_name = $request->companyName;
        $application->tax_number = $request->taxNumber;
        $application->city = $request->city;
        $application->state = $request->state;
        $application->turnover = $request->turnover;
        $application->personnel_number = $request->personnelNumber;
        $application->sector_id = $request->sectorId;
        $application->is_prodis = $request->isProdis;
        $application->manager_name = $request->managerName;
        $application->manager_surname = $request->managerSurname;
        $application->manager_email = $request->managerEmail;
        $application->manager_phone = $request->managerPhone;
        $application->manager_title = $request->managerTitle;
        $application->contact_name = $request->contactName;
        $application->contact_surname = $request->contactSurname;
        $application->contact_email = $request->contactEmail;
        $application->contact_phone = $request->contactPhone;
        $application->contact_title = $request->contactTitle;
        $application->save();

        Notification::route('mail', $application->manager_email)->notify(new TubitakInformationMail("$application->manager_name $application->manager_surname"));
        if ($application->manager_email != $application->contact_email) {
            Notification::route('mail', $application->contact_email)->notify(new TubitakInformationMail("$application->contact_name $application->contact_surname"));
        }
        return (new GeneralResponse())->toJson();
    }
}
