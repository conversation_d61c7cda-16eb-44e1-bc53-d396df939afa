<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\TipContentUser;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;

class UserController extends Controller
{
    public function adminLogin(Request $request)
    {
        $user = User::query()
            ->where('email', '=', $request->email)
            ->where('is_admin', '=', true)
            ->first();
        if ($user && Hash::check($request->password, $user->password)) {
            auth('web')->login($user);
            return redirect()->to('telescope');
        }
        return redirect()->back()
            ->withErrors(['email' => 'E-mail veya şifre yanlış.'])
            ->withInput();
    }
}
