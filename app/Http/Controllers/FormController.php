<?php

namespace App\Http\Controllers;

use App\Enums\MonthEnum;
use App\Http\Requests\Form\GetCategoryRequest;
use App\Http\Requests\Form\GetFormBySlugRequest;
use App\Http\Requests\Form\CalculateFormResultRequest;
use App\Http\Requests\Form\GetMonthRequest;
use App\Http\Requests\Form\GetYearRequest;
use App\Http\Responses\GeneralResponse;
use App\Http\Services\FormService;
use App\Models\Form;
use App\Models\FormCategory;
use App\Utils\GeneralUtil;
use Illuminate\Http\JsonResponse;

class FormController extends Controller
{
    private readonly FormService $formService;

    public function __construct()
    {
        $this->formService = new FormService();
    }

    public function getYears(GetYearRequest $request): JsonResponse
    {
        $years = GeneralUtil::getYearsAsArray();
        return (new GeneralResponse())->setData($years)->toJson();
    }

    public function getMonths(GetMonthRequest $request): JsonResponse
    {
        return (new GeneralResponse())->setData(MonthEnum::getMonths())->toJson();
    }

    public function getCategories(GetCategoryRequest $request): JsonResponse
    {
        $categories = FormCategory::with(['languages', 'forms.languages'])->orderBy('order')->get();
        FormCategory::addLangProperties($categories);
        Form::addLangProperties($categories, 'forms');
        return (new GeneralResponse())->setData($categories->toArray())->toJson();
    }

    public function getFormsBySlug(GetFormBySlugRequest $request, string $slug): JsonResponse
    {
        $form = $this->formService->getFormsBySlug($request, $slug);
        return (new GeneralResponse())->setData($form)->toJson();
    }

    public function calculateFormResults(CalculateFormResultRequest $request): JsonResponse
    {
        $formResult = $this->formService->calculateFormResults($request);
        return (new GeneralResponse())->setData($formResult)->toJson();
    }
}
