<?php

namespace App\Http\Controllers;

use App\Http\Requests\Company\GetCompanyProfileRequest;
use App\Http\Requests\Company\GetNaceCodeRequest;
use App\Http\Requests\Company\GetPersonnelNumberRequest;
use App\Http\Requests\Company\GetSectorRequest;
use App\Http\Requests\Company\UpdateCompanyRequest;
use App\Http\Responses\GeneralResponse;
use App\Models\Company;
use App\Models\CompanyNaceCode;
use App\Models\CompanyPersonnelNumber;
use App\Models\CompanySector;
use App\Models\NaceCode;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CompanyController extends Controller
{
    public function getCompanyProfile(GetCompanyProfileRequest $request): JsonResponse
    {
        $company = Company::with('companyNaceCodes.naceCode')
            ->findOrFail(auth()->user()->company_id);
        Company::mapNaceCodes($company);
        return (new GeneralResponse())->setData($company)->toJson();
    }

    public function getNaceCodes(GetNaceCodeRequest $request): JsonResponse
    {
        $naceCodes = NaceCode::query()
            ->where(fn($q) => isset($request->search) ? $q->where('nace_code', 'LIKE', "%$request->search%")->orWhere('nace_description', 'LIKE', "%$request->search%") : $q)
            ->paginate($request->limit);
        return (new GeneralResponse())
            ->setTotalCount($naceCodes->total())
            ->setData($naceCodes->items())
            ->toJson();
    }

    public function getPersonnelNumbers(GetPersonnelNumberRequest $request): JsonResponse
    {
        $personnelNumbers = CompanyPersonnelNumber::all();
        return (new GeneralResponse())->setData($personnelNumbers->toArray())->toJson();
    }

    public function getSectors(GetSectorRequest $request): JsonResponse
    {
        $sectors = CompanySector::query()
            ->where(fn($q) => isset($request->visibleInsouitApplication) ? $q->where('visible_inosuit_application', $request->visibleInsouitApplication) : $q)
            ->orderBy('name')
            ->get();
        return (new GeneralResponse())->setData($sectors->toArray())->toJson();
    }

    public function updateCompany(UpdateCompanyRequest $request): JsonResponse
    {
        $company = Company::query()->findOrFail(auth()->user()->company_id);
        if ($company->tax_number && $request->taxNumber && $company->tax_number != $request->taxNumber) {
            throw new BadRequestHttpException('Vergi numarası değiştirilemez.');
        }

        DB::beginTransaction();
        try {
            $company->name = $request->name ?? $company->name;
            $company->description = $request->has('description') && $request->description === null ? null : ($request->description ?? $company->description);
            $company->tax_number = $request->taxNumber ?? $company->tax_number;
            $company->email = $request->has('email') && $request->email === null ? null : ($request->email ?? $company->email);
            $company->phone = $request->has('phone') && $request->phone === null ? null : ($request->phone ?? $company->phone);
            $company->linkedin_url = $request->has('linkedinUrl') && $request->linkedinUrl === null ? null : ($request->linkedinUrl ?? $company->linkedin_url);
            $company->twitter_url = $request->has('twitterUrl') && $request->twitterUrl === null ? null : ($request->twitterUrl ?? $company->twitter_url);
            $company->website = $request->has('website') && $request->website === null ? null : ($request->website ?? $company->website);
            $company->logo = $request->has('logo') && $request->logo === null ? null : ($request->logo ?? $company->logo);
            $company->address = $request->has('address') && $request->address === null ? null : ($request->address ?? $company->address);
            $company->save();

            if (isset($request->naceCodes)) {
                $naceCodes = [];
                foreach ($request->naceCodes as $naceCode) {
                    $companyNaceCode = CompanyNaceCode::query()
                        ->where('company_id', $company->id)
                        ->whereHas('naceCode', fn($q) => $q->where('nace_code', $naceCode['code']))
                        ->orWhere('nace_code', $naceCode['code'])
                        ->first();
                    $naceCodes[] = $naceCode['code'];

                    if (!$companyNaceCode) {
                        $companyNaceCode = new CompanyNaceCode();
                        $companyNaceCode->company_id = $company->id;
                    }

                    $defaultNaceCode = NaceCode::query()->where('nace_code', $naceCode['code'])->first();
                    if ($defaultNaceCode) {
                        $companyNaceCode->nace_code_id = $defaultNaceCode->id;
                        $companyNaceCode->nace_code = null;
                        $companyNaceCode->nace_description = null;
                    } else {
                        $companyNaceCode->nace_code_id = null;
                        $companyNaceCode->nace_code = $naceCode['code'];
                        $companyNaceCode->nace_description = $naceCode['description'] ?? null;
                    }
                    $companyNaceCode->save();
                }
                CompanyNaceCode::query()
                    ->where('company_id', $company->id)
                    ->whereHas('naceCode', fn($q) => $q->whereNotIn('nace_code', $naceCodes))
                    ->orWhereNotIn('nace_code', $naceCodes)
                    ->delete();
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.unexpected'))->toJson();
        }
        return (new GeneralResponse())->toJson();
    }
}
