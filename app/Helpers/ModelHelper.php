<?php

namespace App\Helpers;

use App\Models\Language;
use App\Utils\GeneralUtil;

trait ModelHelper
{
    public function languages(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Language::class, 'related_id', $this->languageLocalKey ?? 'id')
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', $this->languageRelatedModel ?? static::class);
    }

    public function allLanguages(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Language::class, 'related_id', 'id')
            ->where('related_model', '=', static::class);
    }

    public static function addLangProperties(
        \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection|array $data, string $relationName = null
    ): void
    {
        foreach ($data as $d) {
            if ($relationName) {
                if (is_iterable($d->$relationName)) {
                    foreach ($d->$relationName as $r) {
                        self::addLangProperty($r);
                    }
                } else {
                    self::addLangProperty($d->$relationName);
                }
            } else {
                self::addLangProperty($d);
            }
        }
    }

    private static function addLangProperty($obj): void
    {
        if ($obj->languages && count($obj->languages) > 0) {
            foreach ($obj->languages as $l) {
                $property = $l->related_property;
                $obj->$property = $l->name;
            }
        }
        $obj->makeHidden('languages');
    }
}
