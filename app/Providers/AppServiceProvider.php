<?php

namespace App\Providers;

use App\Utils\GeneralUtil;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $baseRequestPath = base_path('app/Http/Requests');
        $paths = [$baseRequestPath . '/GeneralRequest.php'];

        $ecoMentorApiKey = env('ECO_MENTOR_API_KEY');
        if ($ecoMentorApiKey) {
            $paths[] = $baseRequestPath . '/EcoMentor';
        } else {
            $directories = File::directories($baseRequestPath);
            foreach ($directories as $dir) {
                if (basename($dir) !== 'EcoMentor') {
                    $paths[] = $dir;
                }
            }
        }
        config(['l5-swagger.documentations.default.paths.annotations' => $paths]);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Lang::setLocale(GeneralUtil::getAppLanguage());
    }
}
