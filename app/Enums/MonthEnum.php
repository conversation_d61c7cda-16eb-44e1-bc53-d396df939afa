<?php

namespace App\Enums;

use App\Models\Language;
use App\Helpers\TypeHelper;
use App\Utils\GeneralUtil;

enum MonthEnum: int
{
    use TypeHelper;

    case JANUARY = 1;
    case FEBRUARY = 2;
    case MARCH = 3;
    case APRIL = 4;
    case MAY = 5;
    case JUNE = 6;
    case JULY = 7;
    case AUGUST = 8;
    case SEPTEMBER = 9;
    case OCTOBER = 10;
    case NOVEMBER = 11;
    case DECEMBER = 12;

    public static function getMonths(): array
    {
        $languages = Language::query()
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', self::class)
            ->get();
        $monthIds = self::values();
        $data = [];
        foreach ($monthIds as $id) {
            $data[] = ['key' => $id, 'value' => $languages->where('related_id', $id)->first()->name];
        }
        return $data;
    }
}
