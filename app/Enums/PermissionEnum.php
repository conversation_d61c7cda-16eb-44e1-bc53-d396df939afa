<?php

namespace App\Enums;

use App\Helpers\TypeHelper;
use App\Models\Language;
use App\Utils\GeneralUtil;

enum PermissionEnum: int
{
    use TypeHelper;

    case AUTHORIZATION = 1;
    case EDUCATION = 2;
    case FORM = 3;
    case REPORT = 4;
    case FACILITY_LIST = 5;
    case FACILITY_OPERATION = 6;
    case REPORT_SELF = 7;

    public function toInt(): int
    {
        return match ($this) {
            PermissionEnum::AUTHORIZATION => 1,
            PermissionEnum::EDUCATION => 2,
            PermissionEnum::FORM => 3,
            PermissionEnum::REPORT => 4,
            PermissionEnum::FACILITY_LIST => 5,
            PermissionEnum::FACILITY_OPERATION => 6,
            PermissionEnum::REPORT_SELF => 7,
        };
    }

    public static function getPermissions(): array
    {
        $languages = Language::query()
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', self::class)
            ->get();
        $permissionIds = self::values();
        $data = [];
        foreach ($permissionIds as $id) {
            $data[] = ['key' => $id, 'value' => $languages->where('related_id', $id)->first()->name];
        }
        return $data;
    }
}
