<?php

namespace App\Enums;

use App\Helpers\TypeHelper;

enum LanguageEnum: string
{
    use TypeHelper;

    case TURKISH = 'tr';
    case ENGLISH = 'en';

    public function toString(): string
    {
        return match ($this) {
            LanguageEnum::TURKISH => 'tr',
            LanguageEnum::ENGLISH => 'en',
        };
    }

    public static function getLanguages(): array
    {
        return [
            ['key' => LanguageEnum::TURKISH->toString(), 'value' => 'Türkçe'],
            ['key' => LanguageEnum::ENGLISH->toString(), 'value' => 'English'],
        ];
    }
}
