<?php

namespace App\Enums;

use App\Helpers\TypeHelper;

enum ClassificationGassesEnum: string
{
    use TypeHelper;

    case CO2 = 'CO2';
    case CH4 = 'CH4';
    case N2O = 'N2O';
    case PFC = 'PFC';
    case SF6 = 'SF6';
    case HFC = 'HFC';
    case NF3 = 'NF3';

    public static function getExtraGasses(): array
    {
        return [self::PFC->name, self::SF6->name, self::HFC->name, self::NF3->name];
    }
}
