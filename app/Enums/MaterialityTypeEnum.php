<?php

namespace App\Enums;

use App\Helpers\TypeHelper;

enum MaterialityTypeEnum: int
{
    use TypeHelper;

    case IMPORTANT = 1;
    case NON_IMPORTANT = 2;
    case IGNORED = 3;

    public function toInt(): int
    {
        return match ($this) {
            MaterialityTypeEnum::IMPORTANT => 1,
            MaterialityTypeEnum::NON_IMPORTANT => 2,
            MaterialityTypeEnum::IGNORED => 3,
        };
    }
}
