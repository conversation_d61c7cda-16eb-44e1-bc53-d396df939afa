<?php

namespace App\Enums;

use App\Helpers\TypeHelper;
use App\Models\Language;
use App\Utils\GeneralUtil;

enum FeedbackSubjectEnum: int
{
    use TypeHelper;

    case CALCULATE = 1;
    case EDUCATIONS = 2;
    case TIP = 3;
    case REPORT = 4;
    case FACILITY = 5;

    public static function getSubjects(): array
    {
        $languages = Language::query()
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', self::class)
            ->get();
        $subjectIds = self::values();
        $data = [];
        foreach ($subjectIds as $id) {
            $data[] = ['key' => $id, 'value' => $languages->where('related_id', $id)->first()->name];
        }
        return $data;
    }
}
