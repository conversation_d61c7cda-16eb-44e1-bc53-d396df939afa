<?php

namespace App\Enums\Inosuit;

use App\Helpers\TypeHelper;
use App\Models\Language;
use App\Utils\GeneralUtil;

enum CompanyPaperAnswerEnum: int
{
    use TypeHelper;

    case VERY_BAD = 1;
    case BAD = 2;
    case MEDIUM = 3;
    case GOOD = 4;
    case VERY_GOOD = 5;

    public static function getAnswers(): array
    {
        $languages = Language::query()
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', self::class)
            ->get();
        $answerIds = self::values();
        $data = [];
        foreach ($answerIds as $id) {
            $data[] = ['key' => $id, 'value' => $languages->where('related_id', $id)->first()->name];
        }
        return $data;
    }
}
