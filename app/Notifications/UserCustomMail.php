<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class UserCustomMail extends Notification implements ShouldQueue, HasLocalePreference
{
    use Queueable;

    private User $user;
    private string $subject;
    private string $text;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, string $subject, string $text)
    {
        $this->user = $user;
        $this->subject = $subject;
        $this->text = $text;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject($this->subject)
            ->view('emails.custom', [
                'greeting' => '<PERSON><PERSON><PERSON><PERSON> ' . $this->user->full_name,
                'companyName' => $this->user->company->name . ',',
                'text' => new HtmlString($this->text),
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
