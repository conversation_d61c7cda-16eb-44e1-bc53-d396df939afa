<?php

namespace App\Notifications;

use App\Models\UserPending;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserPendingInformationMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private UserPending $pending;

    /**
     * Create a new notification instance.
     */
    public function __construct(UserPending $pending)
    {
        $this->pending = $pending;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Hesabınız Onay Sürecinde')
            ->view('emails.pending-information', ['fullName' => $this->pending->full_name]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
