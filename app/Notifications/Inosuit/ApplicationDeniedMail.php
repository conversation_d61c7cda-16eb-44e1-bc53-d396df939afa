<?php

namespace App\Notifications\Inosuit;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationDeniedMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private string $fullName;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $fullName)
    {
        $this->fullName = $fullName;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $appInosuitName = env('APP_INOSUIT_NAME');
        $feedbackInosuitEmail = env('FEEDBACK_INOSUIT_EMAIL');

        return (new MailMessage)
            ->mailer('inosuit')
            ->subject($appInosuitName . ' Başvurunuz Malesef Onaylanmadı')
            ->view('emails.inosuit.application-denied', [
                'appInosuitName' => $appInosuitName,
                'feedbackInosuitEmail' => $feedbackInosuitEmail,
                'fullName' => $this->fullName,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
