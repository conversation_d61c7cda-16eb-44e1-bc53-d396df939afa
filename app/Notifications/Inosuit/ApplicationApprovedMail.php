<?php

namespace App\Notifications\Inosuit;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ApplicationApprovedMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private User $user;
    private ?string $password;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, ?string $password)
    {
        $this->user = $user;
        $this->password = $password;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $appInosuitName = env('APP_INOSUIT_NAME');
        $feedbackInosuitEmail = env('FEEDBACK_INOSUIT_EMAIL');

        if ($this->password) {
            return (new MailMessage)
                ->mailer('inosuit')
                ->subject($appInosuitName . ' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Onaylandı')
                ->view('emails.inosuit.application-approved', [
                    'appInosuitName' => $appInosuitName,
                    'feedbackInosuitEmail' => $feedbackInosuitEmail,
                    'fullName' => $this->user->full_name,
                    'email' => $this->user->email,
                    'password' => $this->password,
                    'url' => env('INOSUIT_FRONTEND_URL'),
                ]);
        } else {
            return (new MailMessage)
                ->mailer('inosuit')
                ->subject($appInosuitName . ' Hesabınız Onaylandı')
                ->view('emails.inosuit.application-approved-user-exist', [
                    'appInosuitName' => $appInosuitName,
                    'feedbackInosuitEmail' => $feedbackInosuitEmail,
                    'fullName' => $this->user->full_name,
                    'url' => env('INOSUIT_FRONTEND_URL'),
                ]);
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
