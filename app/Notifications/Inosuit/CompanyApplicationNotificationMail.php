<?php

namespace App\Notifications\Inosuit;

use App\Models\CompanyInosuitApplication;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CompanyApplicationNotificationMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private CompanyInosuitApplication $application;

    /**
     * Create a new notification instance.
     */
    public function __construct(CompanyInosuitApplication $application)
    {
        $this->application = $application;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $appInosuitName = env('APP_INOSUIT_NAME');

        return (new MailMessage)
            ->mailer('inosuit')
            ->subject($appInosuitName . ' Yeni Firma Başvurusu Alındı')
            ->view('emails.inosuit.company-application-notification', [
                'appInosuitName' => $appInosuitName,
                'application' => $this->application,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
