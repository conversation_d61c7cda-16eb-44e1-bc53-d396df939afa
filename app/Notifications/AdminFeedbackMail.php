<?php

namespace App\Notifications;

use App\Models\Feedback;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminFeedbackMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private int $feedbackId;

    /**
     * Create a new notification instance.
     */
    public function __construct(int $feedbackId)
    {
        $this->feedbackId = $feedbackId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $feedback = Feedback::with(['user', 'languages'])->findOrFail($this->feedbackId);
        Feedback::addLangProperties([$feedback]);

        return (new MailMessage)
            ->subject('GreenTİM - Feedback')
            ->greeting('Feedback ayrıntı<PERSON><PERSON> a<PERSON>ağıda yer almaktadır.')
            ->line('Ad Soyad: ' . $feedback->user->full_name)
            ->line('E-mail: ' . $feedback->user->email)
            ->line('Başlık: ' . $feedback->subject)
            ->line('Mesaj: ' . $feedback->message);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
