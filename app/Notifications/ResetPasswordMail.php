<?php

namespace App\Notifications;

use App\Enums\FrontProjectEnum;
use App\Models\User;
use App\Utils\GeneralUtil;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ResetPasswordMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private User $user;
    private string $token;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, string $token)
    {
        $this->user = $user;
        $this->token = $token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $frontProject = GeneralUtil::getFrontProject();
        if ($frontProject === FrontProjectEnum::ECOTIM || $frontProject === FrontProjectEnum::INOSUIT) {
            $appInosuitName = env('APP_INOSUIT_NAME');
            $feedbackInosuitEmail = env('FEEDBACK_INOSUIT_EMAIL');
            $url = env('INOSUIT_FRONTEND_URL') . '/change-password?token=' . $this->token . '&email=' . $this->user->email;
            return (new MailMessage)
                ->mailer('inosuit')
                ->subject('Şifre Sıfırlama')
                ->view('emails.inosuit.reset-password', [
                    'appInosuitName' => $appInosuitName,
                    'feedbackInosuitEmail' => $feedbackInosuitEmail,
                    'fullName' => $this->user->full_name,
                    'url' => $url,
                ]);
        } else {
            $url = env('GREENTIM_FRONTEND_URL') . '/auth/new-password-generate/' . $this->token;
            return (new MailMessage)
                ->mailer('smtp')
                ->subject('Şifre Sıfırlama')
                ->view('emails.reset-password', [
                    'fullName' => $this->user->full_name,
                    'url' => $url,
                ]);
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
