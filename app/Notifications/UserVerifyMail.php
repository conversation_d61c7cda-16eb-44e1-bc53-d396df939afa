<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserVerifyMail extends Notification implements HasLocalePreference
{
    use Queueable;

    private User $user;
    private string $token;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, string $token)
    {
        $this->user = $user;
        $this->token = $token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $url = env('GREENTIM_FRONTEND_URL') . '/auth/verify/' . $this->token;
        return (new MailMessage)
            ->subject('Hesap Aktivasyon')
            ->greeting('Merhaba ' . $this->user->full_name . ',')
            ->line('Aşağıdaki butona tıklayarak hesabını onaylayabilirsin.')
            ->action('Hesabını Onayla', $url);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    public function preferredLocale(): string
    {
        return $this->locale;
    }
}
