<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyPaperAnswer extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'id',
        'company_paper_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function question()
    {
        return $this->hasOne(CompanyPaperQuestion::class, 'id', 'question_id');
    }
}
