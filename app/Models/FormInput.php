<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormInput extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function form()
    {
        return $this->hasOne(Form::class, 'id', 'form_id');
    }

    public function groups()
    {
        return $this->hasMany(FormInputToGroup::class, 'input_id', 'id');
    }

    public static function mapRelatedInputIds(Collection|array $inputs): void
    {
        if (!is_iterable($inputs)) {
            $inputs = [$inputs];
        }
        foreach ($inputs as $input) {
            $input->related_input_ids = $input->related_input_id ? explode(',', $input->related_input_id) : [];
            $input->related_input_ids = array_map(fn($id) => (int)$id, $input->related_input_ids);
            unset($input->related_input_id);
        }
    }
}
