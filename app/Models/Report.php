<?php

namespace App\Models;

use App\Enums\ClassificationGassesEnum;
use App\Enums\FormResultEnum;
use App\Enums\MonthEnum;
use App\Helpers\ModelHelper;
use App\Utils\GeneralUtil;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Report extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function form()
    {
        return $this->hasOne(Form::class, 'id', 'form_id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function facility()
    {
        return $this->hasOne(Facility::class, 'id', 'facility_id');
    }

    public function result()
    {
        return $this->hasOne(FormResult::class, 'id', 'result_id');
    }

    public function files()
    {
        return $this->hasMany(FileManager::class, 'report_id', 'id');
    }

    public function uncertainty()
    {
        return $this->hasOne(ReportUncertainty::class, 'report_id', 'id');
    }

    public static function mapCommonInputs(Collection|array $reports): void
    {
        foreach ($reports as $r) {
            $r->form->makeHidden('commons');
            $cloneResult = unserialize(serialize($r->result));
            if (count($r->form->commons) == 0 || $r->payload) {
                continue;
            }
            $commonFormId = $cloneResult->form_id;
            if ($r->form_id == $commonFormId) {
                continue;
            }
            $commonGroups = $r->form->commons->where('common_form_id', $commonFormId)->firstOrFail()->groups;
            foreach ($cloneResult->values as $v) {
                $v->input_id = $commonGroups->where('common_input_id', $v->input_id)->firstOrFail()->input_id;
            }
            unset($r->result);
            $r->result = $cloneResult;
        }
    }

    public static function mapResults(Collection|array $reports, $includeGases = false): void
    {
        foreach ($reports as $r) {
            $r->is_formula = !!$r->payload;
            if ($r->result) {
                $cloneResult = FormResult::mapResult($r->form, $r->result, $r->amount, false, $includeGases);
                unset($r->result);
                $r->result = $cloneResult;
            }
            if ($r->payload) {
                unset($r->result);
                $r->carbon_dioxide = $r->carbon_dioxide ?? 0;
                $r->nitrous_oxide = $r->nitrous_oxide ?? 0;
                $r->methane = $r->methane ?? 0;
                $r->extra_gas = $r->extra_gas ?? 0;
                $greenhouseGas = $r->carbon_dioxide + $r->nitrous_oxide + $r->methane + $r->extra_gas;
                $r->result = new FormResult();
                $r->result->greenhouse_gas = GeneralUtil::numberFormat($greenhouseGas);
                $r->result->greenhouse_gas_double = $greenhouseGas;
                $r->result->type = FormResultEnum::FORMULA;
                $r->result->values = collect(json_decode($r->payload))->map(function ($p) {
                    $resultValue = new FormResultValue();
                    $resultValue->input_id = $p->inputId;
                    if (isset($p->groupValueId)) {
                        $resultValue->group_value_id = $p->groupValueId;
                    }
                    if (isset($p->numericValue)) {
                        $resultValue->numeric_value = $p->numericValue;
                    }
                    if (isset($p->textValue)) {
                        $resultValue->text_value = $p->textValue;
                    }
                    return $resultValue;
                });
                if ($includeGases) {
                    $r->result->carbon_dioxide = GeneralUtil::numberFormat($r->carbon_dioxide);
                    $r->result->nitrous_oxide = GeneralUtil::numberFormat($r->nitrous_oxide);
                    $r->result->methane = GeneralUtil::numberFormat($r->methane);
                    $r->result->extra_gas = GeneralUtil::numberFormat($r->extra_gas);
                    $r->result->carbon_dioxide_double = $r->carbon_dioxide;
                    $r->result->nitrous_oxide_double = $r->nitrous_oxide;
                    $r->result->methane_double = $r->methane;
                    $r->result->extra_gas_double = $r->extra_gas;
                }
            }
            $r->makeHidden(['carbon_dioxide', 'nitrous_oxide', 'methane', 'extra_gas', 'payload']);
        }
    }

    public static function mapExtraGasses(Collection $reports): void
    {
        $classifications = IsoStandardClassification::all();
        $extraGasses = ClassificationGassesEnum::getExtraGasses();
        foreach ($reports as $r) {
            $groupValueIds = $r->result->values
                ->map(fn($v) => $v->group_value_id)
                ->unique()
                ->filter(fn($groupValueId) => $groupValueId)
                ->values();
            $reportClassifications = IsoStandardClassification::mapClassifications($classifications->whereIn('group_value_id', $groupValueIds));
            $extraGasList = collect($reportClassifications)->filter(fn($gas) => in_array($gas, $extraGasses));
            foreach ($extraGasList as $extraGas) {
                $r->result->$extraGas = $r->result->extra_gas_double > 0 ? $r->result->extra_gas_double : $r->result->greenhouse_gas_double;
            }
            if (count($extraGasList) > 0 && !$r->result->extra_gas_double) {
                $r->result->carbon_dioxide_double = 0;
                $r->result->nitrous_oxide_double = 0;
                $r->result->methane_double = 0;
                $r->result->extra_gas_double = 0;
            }
        }
    }

    public static function mapMonths(Collection $reports): void
    {
        $languages = Language::query()
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', MonthEnum::class)
            ->get();
        foreach ($reports as $r) {
            $r->monthNames = $languages->whereIn('related_id', explode(',', $r->months))->pluck('name');
        }
    }

    public static function mapValueLanguages(Collection $reports): void
    {
        foreach ($reports as $r) {
            Form::addLangProperties([$r->form]);
            FormCategory::addLangProperties([$r->form->category]);
            foreach ($r->result->values as $value) {
                FormResultValue::mapLanguages($value, false);
            }
        }
    }

    public static function mapUncertainties(Collection $reports): void
    {
        foreach ($reports as $r) {
            $r->uncertainty_value = null;
            $r->uncertainty_value_double = null;
            if ($r->uncertainty) {
                $r->uncertainty_value = GeneralUtil::numberFormat($r->uncertainty->uncertainty_value * 100, 2);
                $r->uncertainty_value_double = $r->uncertainty->uncertainty_value;
            }
            $r->makeHidden(['uncertainty']);
        }
    }

    public static function addMissingValues(Collection|array $reports, Collection $inputs): void
    {
        foreach ($reports as $r) {
            if ($r->form->commons->count() == 0) {
                continue;
            }
            $cloneResult = clone $r->result;
            $groupValueId = null;
            $firstValue = $cloneResult->values->first();
            $missingInputIds = [];
            foreach ($inputs as $input) {
                if ($input->id == $firstValue->input_id) {
                    $groupValueId = $firstValue->group_value_id;
                    break;
                }
                $missingInputIds[] = $input->id;
            }
            $missingInputIds = array_reverse($missingInputIds);
            if (count($missingInputIds) > 0) {
                $groupValueId = FormCommonGroup::with('common')
                    ->whereHas('common', fn($q) => $q->where('form_id', $r->form_id))
                    ->where('common_group_value_id', $groupValueId)
                    ->first()->group_value_id;
            }
            foreach ($missingInputIds as $missingInputId) {
                $relatedGroupValueId = FormInputToGroup::with('values')
                    ->where('input_id', $firstValue->input_id)
                    ->whereHas('values', fn($q) => $q->where('id', $groupValueId))
                    ->first()
                    ->related_group_value_id;
                $relatedGroupValue = FormGroupValue::with('languages')->find($relatedGroupValueId);
                $firstValue = (object)[
                    'id' => null,
                    'input_id' => $missingInputId,
                    'group_value_id' => $relatedGroupValue->id,
                    'group_value_name' => $relatedGroupValue->languages->first()->name,
                ];
                $groupValueId = $relatedGroupValue->id;
                $cloneResult->values->prepend($firstValue);
            }
            unset($r->result);
            $r->result = $cloneResult;
        }
    }
}
