<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class IsoStandard extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'deleted_at',
    ];

    public function values()
    {
        return $this->hasMany(IsoStandardValue::class, 'iso_standard_id', 'id');
    }

    public static function mapIsoStandards(Collection|array $isoStandards): void
    {
        foreach ($isoStandards as $isoStandard) {
            foreach ($isoStandard->values as $value) {
                $value->value = json_decode($value->value);
            }
        }
    }
}
