<?php

namespace App\Models;

use App\Enums\ActivityEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Activity extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_user_id',
        'effected_user_ids',
        'updated_at',
        'deleted_at'
    ];

    protected $casts = [
        'effected_user_ids' => 'array',
        'payload' => 'array'
    ];

    public function createdUser()
    {
        return $this->hasOne(User::class, 'id', 'created_user_id')
            ->select('id', 'full_name', 'title', 'photo', 'privilege_id', 'company_id');
    }

    public static function createActivity(ActivityEnum $activityType, array $effectedUserIds, array $payload): void
    {
        $activity = new Activity();
        $activity->created_user_id = auth()->id();
        $activity->activity_type = $activityType;
        $activity->effected_user_ids = array_values($effectedUserIds);
        $activity->payload = $payload;
        $activity->save();
    }
}
