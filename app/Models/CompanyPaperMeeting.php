<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyPaperMeeting extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'id',
        'company_paper_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function meeting()
    {
        return $this->hasOne(CompanyMeeting::class, 'id', 'meeting_id');
    }
}
