<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class IsoStandardSource extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'form_id',
        'urls',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static function mapSources(Collection $sources): void
    {
        foreach ($sources as $s) {
            $s->sources = json_decode($s->sources);
        }
    }
}
