<?php

namespace App\Models;

use App\Enums\FeedbackSubjectEnum;
use App\Helpers\ModelHelper;
use App\Utils\GeneralUtil;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Feedback extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $table = 'feedbacks';

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected string $languageLocalKey = 'subject_enum_id';
    protected string $languageRelatedModel = FeedbackSubjectEnum::class;

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id')->select('id', 'full_name', 'email');
    }

    public function subjectLanguages(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Language::class, 'related_id', 'id')
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', static::class);
    }

    public static function mapSubjects(Collection $feedbacks): void
    {
        foreach ($feedbacks as $feedback) {

        }
    }
}
