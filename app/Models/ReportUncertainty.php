<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReportUncertainty extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'id',
        'report_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function pedigrees()
    {
        return $this->hasMany(ReportUncertaintyPedigree::class, 'report_uncertainty_id', 'id');
    }

    public function electricity()
    {
        return $this->hasOne(ReportUncertaintyElectricity::class, 'report_uncertainty_id', 'id');
    }
}
