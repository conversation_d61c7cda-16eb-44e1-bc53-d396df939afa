<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\Inosuit\RolePrivilegeEnum;
use App\Enums\PermissionEnum;
use App\Models\Mentor\Mentor;
use App\Models\Mentor\MentorInosuitApplication;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'full_name',
        'email',
        'title',
        'is_approved_password',
        'password',
        'company_id',
        'type_id',
        'role_id',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'email_verified_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function company()
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function reports()
    {
        return $this->hasMany(Report::class, 'user_id', 'id');
    }

    public function isoStandards()
    {
        return $this->hasMany(IsoStandard::class, 'user_id', 'id');
    }

    public function artificials()
    {
        return $this->hasMany(Artificial::class, 'user_id', 'id');
    }

    public function association()
    {
        return $this->hasOne(Association::class, 'id', 'association_id');
    }

    public function role()
    {
        return $this->hasOne(Role::class, 'id', 'role_id');
    }

    public function privilege()
    {
        return $this->hasOne(RolePrivilege::class, 'id', 'privilege_id');
    }

    public function mentor()
    {
        return $this->hasOne(Mentor::class, 'user_id', 'id');
    }

    public function companyPeriods()
    {
        return $this->hasMany(CompanyPeriod::class, 'mentor_user_id', 'id');
    }

    public function inosuitApplication()
    {
        return $this->hasOne(MentorInosuitApplication::class, 'mentor_user_id', 'id');
    }

    public function hasPermission(PermissionEnum ...$permissionEnums): bool
    {
        $permissions = array_map(fn($p) => $p->toInt(), $permissionEnums);
        return (bool)$this->role->permissions->whereIn('permission_id', $permissions)->first();
    }

    public function mapPermissions(): void
    {
        $enumCombine = PermissionEnum::combine();
        $this->role->permissions->each(fn($p) => $p->value = $enumCombine[$p->permission_id]);
    }

    public function mapAppsAccess(): void
    {
        $this->access_greentim = false;
        $this->access_inosuit = false;
        if ($this->company) {
            $this->access_greentim = true;
        }
        if ($this->privilege) {
            if ($this->privilege->slug == RolePrivilegeEnum::TIM_ADMIN->value) {
                $this->access_inosuit = true;
            } else if ($this->privilege->slug == RolePrivilegeEnum::ASSOCIATION->value) {
                $this->access_inosuit = $this->association ? true : false;
            } else if ($this->privilege->slug == RolePrivilegeEnum::COMPANY->value) {
                $this->access_inosuit = $this->company->companyPeriods->count() > 0;
            } else if ($this->privilege->slug == RolePrivilegeEnum::MENTOR->value) {
                $this->access_inosuit = $this->mentor ? true : false;
            }
        }
    }
}
