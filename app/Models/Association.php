<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Association extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public static function getCompanies(int $periodId): Collection
    {
        $associationId = auth()->user()->association_id;
        if (!$associationId) {
            return new Collection();
        }
        return Company::query()
            ->with([
                'companyPeriods' => fn($q) => $q->where('period_id', $periodId)->where('association_id', $associationId),
            ])
            ->whereHas('companyPeriods', fn($q) => $q->where('period_id', $periodId)->where('association_id', $associationId))
            ->get();
    }
}
