<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyPaper extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_user_id',
        'company_period_id',
        'deleted_at'
    ];

    public function createdUser()
    {
        return $this->hasOne(User::class, 'id', 'created_user_id')
            ->select('id', 'full_name', 'email', 'phone', 'photo');
    }

    public function companyPeriod()
    {
        return $this->hasOne(CompanyPeriod::class, 'id', 'company_period_id');
    }

    public function companyPaperMeetings()
    {
        return $this->hasMany(CompanyPaperMeeting::class, 'company_paper_id', 'id');
    }

    public function companyPaperAnswers()
    {
        return $this->hasMany(CompanyPaperAnswer::class, 'company_paper_id', 'id');
    }
}
