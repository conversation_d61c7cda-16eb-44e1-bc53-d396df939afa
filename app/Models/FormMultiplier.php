<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormMultiplier extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public static function mapMultipliers(Collection|array $multipliers)
    {
        $groupValueIds = collect();
        foreach ($multipliers as $m) {
            $m->group_value_ids = json_decode($m->group_value_ids);
            $m->multiplier = json_decode($m->multiplier);
            $groupValueIds->push(...$m->group_value_ids);
        }
        $groupValues = FormGroupValue::with('languages')
            ->whereIn('id', $groupValueIds->unique())
            ->get();
        FormGroupValue::addLangProperties($groupValues);
        foreach ($multipliers as $m) {
            $m->group_values = $groupValues->whereIn('id', $m->group_value_ids)->values();
            $m->makeHidden(['group_value_ids']);
        }
    }
}
