<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function permissions()
    {
        return $this->hasMany(RolePermission::class, 'role_id', 'id');
    }
}
