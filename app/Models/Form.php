<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Form extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function inputs()
    {
        return $this->hasMany(FormInput::class, 'form_id', 'id')->orderBy('order');
    }

    public function results()
    {
        return $this->hasMany(FormResult::class, 'form_id', 'id');
    }

    public function category()
    {
        return $this->hasOne(FormCategory::class, 'id', 'category_id');
    }

    public function commons()
    {
        return $this->hasMany(FormCommon::class, 'form_id', 'id');
    }

    public function operation()
    {
        return $this->hasOne(FormOperation::class, 'form_id', 'id');
    }

    public function operations()
    {
        return $this->hasMany(FormOperation::class, 'form_id', 'id');
    }

    public function multipliers()
    {
        return $this->hasMany(FormMultiplier::class, 'form_id', 'id');
    }

    public function sources()
    {
        return $this->hasMany(IsoStandardSource::class, 'form_id', 'id');
    }

    public static function mapResultValueLanguages(self $form): void
    {
        foreach ($form->results as $result) {
            foreach ($result->values as $value) {
                FormResultValue::mapLanguages($value);
            }
        }
    }
}
