<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormUncertainty extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'form_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    protected $casts = [
        'uncertainty' => 'array',
    ];

    public static function mapUncertainties(Collection|array $uncertainties)
    {
        $groupValueIds = $uncertainties->pluck('group_value_id')->unique();
        $groupValues = FormGroupValue::with('languages')
            ->whereIn('id', $groupValueIds)
            ->get();
        FormGroupValue::addLangProperties($groupValues);
        foreach ($uncertainties as $u) {
            $u->group_value = $groupValues->where('id', $u->group_value_id)->first();
            $u->makeHidden(['group_value_id']);
        }
    }
}
