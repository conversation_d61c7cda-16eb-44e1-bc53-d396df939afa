<?php

namespace App\Models\Mentor;

use App\Models\Edu\EduDepartment;
use App\Models\Edu\EduFaculty;
use App\Models\Edu\EduUniversity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MentorInosuitApplication extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'updated_at',
        'deleted_at'
    ];

    protected $casts = [
        'extra_urls' => 'array',
    ];

    public function university()
    {
        return $this->hasOne(EduUniversity::class, 'id', 'university_id');
    }

    public function faculty()
    {
        return $this->hasOne(EduFaculty::class, 'id', 'faculty_id');
    }

    public function department()
    {
        return $this->hasOne(EduDepartment::class, 'id', 'department_id');
    }
}
