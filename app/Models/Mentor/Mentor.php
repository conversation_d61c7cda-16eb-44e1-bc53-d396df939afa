<?php

namespace App\Models\Mentor;

use App\Models\Company;
use App\Models\Edu\EduDepartment;
use App\Models\Edu\EduFaculty;
use App\Models\Edu\EduUniversity;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Mentor extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'id',
        'user_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function university()
    {
        return $this->hasOne(EduUniversity::class, 'id', 'university_id');
    }

    public function faculty()
    {
        return $this->hasOne(EduFaculty::class, 'id', 'faculty_id');
    }

    public function department()
    {
        return $this->hasOne(EduDepartment::class, 'id', 'department_id');
    }

    public static function getCompanies(int $periodId): Collection
    {
        return Company::query()
            ->with([
                'companyPeriods' => fn($q) => $q->where('period_id', $periodId)->where('mentor_user_id', auth()->id()),
            ])
            ->whereHas('companyPeriods', fn($q) => $q->where('period_id', $periodId)->where('mentor_user_id', auth()->id()))
            ->get();
    }

    public static function getCurrentCompanies(): Collection
    {
        return Company::with('currentPeriod')
            ->whereHas('currentPeriod', fn($q) => $q->where('mentor_user_id', auth()->id()))
            ->get();
    }

    public static function getCompanyPeriodIds(): array
    {
        $companies = self::getCurrentCompanies();
        return $companies->map(fn($c) => $c->currentPeriod->id)->toArray();
    }
}
