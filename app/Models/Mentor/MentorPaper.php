<?php

namespace App\Models\Mentor;

use App\Models\CompanyPeriod;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MentorPaper extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_user_id',
        'company_period_id',
        'deleted_at'
    ];

    protected $casts = [
        'extra_urls' => 'array',
    ];

    public function createdUser()
    {
        return $this->hasOne(User::class, 'id', 'created_user_id')
            ->select('id', 'full_name', 'email', 'phone', 'photo');
    }

    public function companyPeriod()
    {
        return $this->hasOne(CompanyPeriod::class, 'id', 'company_period_id');
    }

    public function mentorPaperMeetings()
    {
        return $this->hasMany(MentorPaperMeeting::class, 'mentor_paper_id', 'id');
    }
}
