<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyMeeting extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'company_period_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function createdUser()
    {
        return $this->hasOne(User::class, 'id', 'created_user_id')
            ->select('id', 'full_name', 'title', 'photo');
    }

    public function companyPeriod()
    {
        return $this->hasOne(CompanyPeriod::class, 'id', 'company_period_id');
    }
}
