<?php

namespace App\Models;

use App\Utils\GeneralUtil;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Company extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function sector()
    {
        return $this->hasOne(CompanySector::class, 'id', 'sector_id');
    }

    public function facilities()
    {
        return $this->hasMany(Facility::class, 'company_id', 'id');
    }

    public function personnel()
    {
        return $this->hasOne(CompanyPersonnelNumber::class, 'id', 'personnel_number_id');
    }

    public function inosuitApplication()
    {
        return $this->hasOne(CompanyInosuitApplication::class, 'company_id', 'id');
    }

    public function currentPeriod()
    {
        $periodId = GeneralUtil::getInosuitPeriodId();
        return $this
            ->hasOne(CompanyPeriod::class, 'company_id', 'id')
            ->where('period_id', $periodId);
    }

    public function companyPeriods()
    {
        return $this->hasMany(CompanyPeriod::class, 'company_id', 'id');
    }

    public function companyNaceCodes()
    {
        return $this->hasMany(CompanyNaceCode::class, 'company_id', 'id');
    }

    public static function mapNaceCodes(Collection|Model $company)
    {
        $naceCodes = collect();
        foreach ($company->companyNaceCodes as $cnc) {
            if ($cnc->naceCode) {
                $naceCodes->push((object)[
                    'code' => $cnc->naceCode->nace_code,
                    'description' => $cnc->naceCode->nace_description,
                ]);
            } else {
                $naceCodes->push((object)[
                    'code' => $cnc->nace_code,
                    'description' => $cnc->nace_description,
                ]);
            }
        }
        $company->naceCodes = $naceCodes;
        $company->makeHidden(['companyNaceCodes']);
    }
}
