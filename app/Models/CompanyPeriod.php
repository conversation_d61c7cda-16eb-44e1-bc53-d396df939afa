<?php

namespace App\Models;

use App\Models\Mentor\MentorPaper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CompanyPeriod extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'company_id',
        'period_id',
        'lane_id',
        'association_id',
        'mentor_user_id',
        'calendar_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function company()
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function period()
    {
        return $this->hasOne(Period::class, 'id', 'period_id');
    }

    public function lane()
    {
        return $this->hasOne(PeriodLane::class, 'id', 'lane_id');
    }

    public function association()
    {
        return $this->hasOne(Association::class, 'id', 'association_id');
    }

    public function mentor()
    {
        return $this->hasOne(User::class, 'id', 'mentor_user_id')
            ->select('id', 'full_name', 'email', 'phone', 'photo');
    }

    public function meetings()
    {
        return $this->hasMany(CompanyMeeting::class, 'company_period_id', 'id');
    }

    public function companyPapers()
    {
        return $this->hasMany(CompanyPaper::class, 'company_period_id', 'id');
    }

    public function mentorPapers()
    {
        return $this->hasMany(MentorPaper::class, 'company_period_id', 'id');
    }

    public static function getCurrentPeriod(): CompanyPeriod
    {
        $company = Company::with('currentPeriod')->findOrFail(auth()->user()->company_id);
        if (!$company->currentPeriod) {
            throw new BadRequestHttpException('Güncel dönem bulunamadı.');
        }
        return $company->currentPeriod;
    }
}
