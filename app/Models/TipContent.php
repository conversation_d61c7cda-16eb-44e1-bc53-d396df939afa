<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TipContent extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'tip_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function tip()
    {
        return $this->hasOne(Tip::class, 'id', 'tip_id');
    }

    public function user()
    {
        return $this->hasOne(TipContentUser::class, 'content_id', 'id')->where('user_id', auth()->id());
    }

    public static function mapUserReads(Collection $contents): void
    {
        foreach ($contents as $c) {
            $c->is_read = (bool)$c->user?->is_read;
            $c->makeHidden('user');
        }
    }
}
