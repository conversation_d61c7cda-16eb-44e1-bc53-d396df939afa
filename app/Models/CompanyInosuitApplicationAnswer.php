<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyInosuitApplicationAnswer extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'application_id',
        'question_id',
        'updated_at',
        'deleted_at'
    ];

    public function question()
    {
        return $this->hasOne(CompanyInosuitApplicationQuestion::class, 'id', 'question_id');
    }
}
