<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanySector extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'visible_inosuit_application',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function companies()
    {
        return $this->hasMany(Company::class, 'sector_id', 'id');
    }
}
