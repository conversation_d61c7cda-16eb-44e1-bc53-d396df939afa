<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormInputToGroup extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $hidden = [
        'input_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    protected $fillable = [
        'input_id',
        'group_id',
        'related_group_value_id',
    ];

    public function values()
    {
        return $this->hasMany(FormGroupValue::class, 'group_id', 'group_id')
            ->orderBy('order');
    }

    public function input()
    {
        return $this->hasMany(FormInput::class, 'id', 'input_id');
    }
}
