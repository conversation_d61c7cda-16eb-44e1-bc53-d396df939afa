<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TipContentUser extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static function addTip(string $action, int $userId = null): void
    {
        $authUserId = $userId ?? auth()->id();
        $tip = Tip::with('contents')->where('action', $action)->first();
        if (!$tip) {
            return;
        }
        $existContentIds = TipContentUser::query()->where('user_id', $authUserId)->get()->pluck('content_id')->toArray();
        $contentUsers = [];
        foreach ($tip->contents as $content) {
            if (!in_array($content->id, $existContentIds)) {
                $contentUsers[] = [
                    'content_id' => $content->id,
                    'user_id' => $authUserId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }
        TipContentUser::query()->insert($contentUsers);
    }
}
