<?php

namespace App\Models;

use App\Enums\FormResultEnum;
use App\Enums\GwpEnum;
use App\Utils\GeneralUtil;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormResult extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'form_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function values()
    {
        return $this->hasMany(FormResultValue::class, 'result_id', 'id');
    }

    public static function mapResult(Form $form, FormResult $result, float $amount, $hiddenValues = false, $includeGases = false): FormResult
    {
        $cloneResult = clone $result;
        if ($form->has_gwp) {
            $greenhouseGas = ($amount * ($cloneResult->carbon_dioxide + $cloneResult->nitrous_oxide + $cloneResult->methane)) / 1000;
        } else {
            $greenhouseGas = ($amount * (($cloneResult->carbon_dioxide * GwpEnum::$CO2) + ($cloneResult->nitrous_oxide * GwpEnum::$N2O) + ($cloneResult->methane * GwpEnum::$CH4))) / 1000;
        }
        $cloneResult->greenhouse_gas = GeneralUtil::numberFormat($greenhouseGas);
        $cloneResult->greenhouse_gas_double = $greenhouseGas;
        $cloneResult->makeHidden('carbon_dioxide', 'nitrous_oxide', 'methane');
        if ($hiddenValues) {
            $cloneResult->makeHidden('values');
        }
        if ($includeGases) {
            $cloneResult->carbon_dioxide_double = $amount * $cloneResult->carbon_dioxide / 1000;
            $cloneResult->nitrous_oxide_double = $amount * $cloneResult->nitrous_oxide / 1000;
            $cloneResult->methane_double = $amount * $cloneResult->methane / 1000;
            if (!$form->has_gwp) {
                $cloneResult->carbon_dioxide_double *= GwpEnum::$CO2;
                $cloneResult->nitrous_oxide_double *= GwpEnum::$N2O;
                $cloneResult->methane_double *= GwpEnum::$CH4;
            }
            $cloneResult->carbon_dioxide = GeneralUtil::numberFormat($cloneResult->carbon_dioxide_double);
            $cloneResult->nitrous_oxide = GeneralUtil::numberFormat($cloneResult->nitrous_oxide_double);
            $cloneResult->methane = GeneralUtil::numberFormat($cloneResult->methane_double);
            $cloneResult->makeVisible('carbon_dioxide', 'nitrous_oxide', 'methane');
        }
        $cloneResult->type = FormResultEnum::DEFAULT;
        return $cloneResult;
    }
}
