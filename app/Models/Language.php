<?php

namespace App\Models;

use App\Enums\LanguageEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Language extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'related_id',
        'related_model',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static function createLanguage(string $name, LanguageEnum $langType, int $relatedId, string $relatedModel, string $relatedProperty): array
    {
        return [
            'name' => $name,
            'lang_type' => $langType,
            'related_id' => $relatedId,
            'related_model' => $relatedModel,
            'related_property' => $relatedProperty,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
