<?php

namespace App\Models;

use App\Helpers\ModelHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReportUncertaintyElectricity extends Model
{
    use HasFactory, SoftDeletes, ModelHelper;

    protected $table = 'report_uncertainty_electricity';

    protected $hidden = [
        'id',
        'report_uncertainty_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
}
