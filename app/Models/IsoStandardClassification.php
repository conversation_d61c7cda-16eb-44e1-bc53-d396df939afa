<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class IsoStandardClassification extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function groupValue()
    {
        return $this->hasOne(FormGroupValue::class, 'id', 'group_value_id');
    }

    public static function mapClassifications(Collection $classifications): array
    {
        $gasses = collect();
        foreach ($classifications as $c) {
            $gasses->push(...json_decode($c->gasses));
        }
        return $gasses->unique()->values()->toArray();
    }
}
