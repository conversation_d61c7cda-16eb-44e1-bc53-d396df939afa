<?php

namespace App\Models;

use App\Utils\GeneralUtil;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormResultValue extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'result_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function inputLanguages()
    {
        return $this->hasMany(Language::class, 'related_id', 'input_id')
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', FormInput::class);
    }

    public function groupValueLanguages()
    {
        return $this->hasMany(Language::class, 'related_id', 'group_value_id')
            ->where('lang_type', '=', GeneralUtil::getAppLanguage())
            ->where('related_model', '=', FormGroupValue::class);
    }

    public static function mapLanguages(self $resultValue, $forInput = true): void
    {
        if ($forInput && $resultValue->inputLanguages && count($resultValue->inputLanguages) > 0) {
            foreach ($resultValue->inputLanguages as $l) {
                $property = 'input_' . $l->related_property;
                $resultValue->$property = $l->name;
            }
        }
        if ($resultValue->groupValueLanguages && count($resultValue->groupValueLanguages) > 0) {
            foreach ($resultValue->groupValueLanguages as $l) {
                $property = 'group_value_' . $l->related_property;
                $resultValue->$property = $l->name;
            }
        }
        $resultValue->makeHidden(['inputLanguages', 'groupValueLanguages']);
    }
}
