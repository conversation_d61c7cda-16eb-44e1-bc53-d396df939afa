<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Period extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'visible_inosuit_application',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    public function companyPeriods()
    {
        return $this->hasMany(CompanyPeriod::class, 'period_id', 'id');
    }

    public function lanes()
    {
        return $this->hasMany(PeriodLane::class, 'period_id', 'id')->orderBy('order');
    }
}
