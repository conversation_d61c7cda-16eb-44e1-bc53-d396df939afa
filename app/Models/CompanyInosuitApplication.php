<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyInosuitApplication extends Model
{
    use HasFactory, SoftDeletes;

    protected $hidden = [
        'updated_at',
        'deleted_at'
    ];

    public function sector()
    {
        return $this->hasOne(CompanySector::class, 'id', 'sector_id');
    }

    public function association()
    {
        return $this->hasOne(Association::class, 'id', 'association_id');
    }

    public function period()
    {
        return $this->hasOne(Period::class, 'id', 'period_id');
    }

    public function answers()
    {
        return $this->hasMany(CompanyInosuitApplicationAnswer::class, 'application_id', 'id');
    }
}
