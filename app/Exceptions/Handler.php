<?php

namespace App\Exceptions;

use App\Http\Responses\GeneralResponse;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ItemNotFoundException;
use League\OAuth2\Server\Exception\OAuthServerException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            if ($e instanceof OAuthServerException) {
                return false;
            }
            Log::error($e);
            return false;
        });
        $this->renderable(function (\Exception $e, $request) {
            if ($e instanceof HttpException) {
                if ($e->getStatusCode() == Response::HTTP_FORBIDDEN) {
                    return (new GeneralResponse(false, Response::HTTP_FORBIDDEN))->setMessages(Lang::get('messages.general.forbidden'))->toJson();
                } else if ($e->getStatusCode() == Response::HTTP_NOT_FOUND) {
                    Log::error($e);
                    return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.not_found'))->toJson();
                } else if ($e->getStatusCode() == Response::HTTP_BAD_REQUEST) {
                    return (new GeneralResponse(false))->setMessages($e->getMessage())->toJson();
                }
            } else if ($e instanceof ItemNotFoundException) {
                Log::error($e);
                return (new GeneralResponse(false))->setMessages(Lang::get('messages.general.not_found'))->toJson();
            }
        });
    }
}
