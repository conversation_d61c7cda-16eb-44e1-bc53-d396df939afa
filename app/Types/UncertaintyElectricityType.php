<?php

namespace App\Types;

use App\Enums\UncertaintyElectricityClassTypeEnum;
use App\Enums\UncertaintyElectricityMeterTypeEnum;
use App\Enums\UncertaintyElectricityTemperatureTypeEnum;

class UncertaintyElectricityType
{
    public static array $values = [
        UncertaintyElectricityClassTypeEnum::A->value => [
            UncertaintyElectricityMeterTypeEnum::SINGLE_PHASE->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'min' => 0.035,
                    'max' => 0.035,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'min' => 0.05,
                    'max' => 0.045,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'min' => 0.07,
                    'max' => 0.07,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'min' => 0.09,
                    'max' => 0.09,
                ],
            ],
            UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_BALANCED_LOAD->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'min' => 0.035,
                    'max' => 0.035,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'min' => 0.05,
                    'max' => 0.045,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'min' => 0.07,
                    'max' => 0.07,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'min' => 0.09,
                    'max' => 0.09,
                ],
            ],
            UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_SINGLE_LOAD->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'max' => 0.045,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'max' => 0.05,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'max' => 0.07,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'max' => 0.09,
                ],
            ],
        ],

        UncertaintyElectricityClassTypeEnum::B->value => [
            UncertaintyElectricityMeterTypeEnum::SINGLE_PHASE->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'min' => 0.02,
                    'max' => 0.02,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'min' => 0.025,
                    'max' => 0.025,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'min' => 0.035,
                    'max' => 0.035,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'min' => 0.04,
                    'max' => 0.04,
                ],
            ],
            UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_BALANCED_LOAD->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'min' => 0.02,
                    'max' => 0.02,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'min' => 0.025,
                    'max' => 0.025,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'min' => 0.035,
                    'max' => 0.035,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'min' => 0.04,
                    'max' => 0.04,
                ],
            ],
            UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_SINGLE_LOAD->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'max' => 0.025,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'max' => 0.03,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'max' => 0.04,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'max' => 0.045,
                ],
            ],
        ],

        UncertaintyElectricityClassTypeEnum::C->value => [
            UncertaintyElectricityMeterTypeEnum::SINGLE_PHASE->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'min' => 0.01,
                    'max' => 0.007,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'min' => 0.013,
                    'max' => 0.01,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'min' => 0.017,
                    'max' => 0.013,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'min' => 0.02,
                    'max' => 0.015,
                ],
            ],
            UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_BALANCED_LOAD->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'min' => 0.01,
                    'max' => 0.007,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'min' => 0.013,
                    'max' => 0.01,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'min' => 0.017,
                    'max' => 0.013,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'min' => 0.02,
                    'max' => 0.015,
                ],
            ],
            UncertaintyElectricityMeterTypeEnum::MULTI_PHASE_SINGLE_LOAD->value => [
                UncertaintyElectricityTemperatureTypeEnum::T_5_30->value => [
                    'max' => 0.01,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_30_40->value => [
                    'max' => 0.013,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_40_55->value => [
                    'max' => 0.017,
                ],
                UncertaintyElectricityTemperatureTypeEnum::T_55_70->value => [
                    'max' => 0.02,
                ],
            ],
        ],
    ];
}
