<?php

namespace App\Types;

use App\Enums\UncertaintyPedigreeTabEnum;

class UncertaintyPedigreeIndicatorType
{
    public static array $indicators = [
        UncertaintyPedigreeTabEnum::RELIABILITY->value => [
            1 => 1,
            2 => 1.05,
            3 => 1.1,
            4 => 1.2,
            5 => 1.5,
        ],
        UncertaintyPedigreeTabEnum::COMPLETENESS->value => [
            1 => 1,
            2 => 1.02,
            3 => 1.05,
            4 => 1.1,
            5 => 1.2,
        ],
        UncertaintyPedigreeTabEnum::TEMPORAL->value => [
            1 => 1,
            2 => 1.03,
            3 => 1.1,
            4 => 1.2,
            5 => 1.5,
        ],
        UncertaintyPedigreeTabEnum::GEOGRAPHIC->value => [
            1 => 1,
            2 => 1.01,
            3 => 1.02,
            4 => 1.05,
            5 => 1.1,
        ],
        UncertaintyPedigreeTabEnum::TECHNOLOGY->value => [
            1 => 1,
            2 => 1.1,
            3 => 1.2,
            4 => 1.5,
            5 => 2,
        ],
    ];
}
